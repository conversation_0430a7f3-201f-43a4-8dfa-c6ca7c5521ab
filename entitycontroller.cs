// Namespace: 
public class EntityController : MonoBehaviour // TypeDefIndex: 469
{
	// Fields
	private const int FAKE_RANDOM = 123456789;
	private const uint XOR1 = 3287472563;
	private const uint XOR2 = 3549484382;
	public static FuzzingCoefficients countCoefficients; // 0x0
	private static int m_awayUniqueId; // 0xC
	public static int m_battleId; // 0x10
	public static int count; // 0x14
	public int initialOwnerId; // 0x20
	public static Dictionary<TagEnum, FuzzedInt> countByType; // 0x18
	public TagEnum entityId; // 0x24
	private int m_uniqueId; // 0x28
	public int battleId; // 0x2C
	public PlayerModel pm; // 0x30
	public EventMgr eventMgr; // 0x38
	public bool isInAnimationInterrupt; // 0x40
	[CompilerGenerated]
	private bool <InSimulation>k__BackingField; // 0x41
	public bool attackMode; // 0x42
	private string m_bubbleTextMessage; // 0x48
	public ParticleSystem m_currentTargetSystem; // 0x50
	private bool m_errorSent; // 0x58
	private bool m_isAirUnit; // 0x59
	private bool m_isAirFighterUnit; // 0x5A
	private bool m_isBomberUnit; // 0x5B
	private bool m_isTransportUnit; // 0x5C
	private bool m_isScoutUnit; // 0x5D
	private bool m_isParatrooperUnit; // 0x5E
	private bool m_isSingleBattleUnit; // 0x5F
	private bool m_isHiddenInAttackHistory; // 0x60
	private EntityController m_creatingFactory; // 0x68
	private bool m_killedAudioFlag; // 0x70
	protected UpgradeHelper m_upgrade; // 0x78
	protected BuildingHelper m_building; // 0x80
	protected TownHallHelper m_townHall; // 0x88
	public StatsHelper2 m_stats; // 0x90
	public StatsHelper2 m_maxStats; // 0x98
	private ResourceContainerHelper m_resourceContainer; // 0xA0
	private TradeGoodContainerHelper m_tradeGoodContainer; // 0xA8
	protected CannonHelper m_cannon; // 0xB0
	protected DoDamageAoEHelper m_doDamageAoE; // 0xB8
	protected RotatingShooterHelper m_rotatingShooter; // 0xC0
	protected AgeDisplayHelper m_ageDisplay; // 0xC8
	protected CaravanHelper m_caravan; // 0xD0
	protected NextHelper m_next; // 0xD8
	protected CaravanLootHelper m_caravanLoot; // 0xE0
	protected CitizenHelper m_citizen; // 0xE8
	protected DebrisHelper m_debris; // 0xF0
	protected DecorationHelper m_decoration; // 0xF8
	protected ExpansionHelper m_expansion; // 0x100
	protected GoodyHutHelper m_goodyHut; // 0x108
	protected ItemHelper m_item; // 0x110
	protected GoodyCampHelper m_goodyCamp; // 0x118
	protected GoldMineHelper m_goldMine; // 0x120
	protected HuntableHelper m_huntable; // 0x128
	protected WonderHelper m_wonder; // 0x130
	protected TrapHelper m_trap; // 0x138
	protected TroopHelper m_troop; // 0x140
	protected ArmoryHelper m_armory; // 0x148
	protected CitizenHomeHelper m_citizenHome; // 0x150
	protected HarvestHelper m_harvest; // 0x158
	protected LibraryHelper m_library; // 0x160
	protected UniversityHelper m_university; // 0x168
	protected MuseumHelper m_museum; // 0x170
	protected CouncilChambersHelper m_council; // 0x178
	protected ServerHarvestHelper m_serverHarvest; // 0x180
	protected RoadHelper m_road; // 0x188
	protected TollHelper m_toll; // 0x190
	protected WallHelper m_wall; // 0x198
	protected GateHelper m_gate; // 0x1A0
	protected DescriptionHelper m_description; // 0x1A8
	protected GatherableHelper m_gatherable; // 0x1B0
	protected PurchasedResourceHelper m_purchasedResource; // 0x1B8
	protected PurchasedCollectibleHelper m_purchasedCollectible; // 0x1C0
	protected SpecialDealHelper m_specialDeal; // 0x1C8
	protected FaunaHelper m_fauna; // 0x1D0
	protected TroopContainerHelper m_troopContainer; // 0x1D8
	protected TerraCottaCampModel m_terraCottaCamp; // 0x1E0
	protected TroopFactoryHelper m_troopFactory; // 0x1E8
	protected SpellFactoryHelper m_spellFactory; // 0x1F0
	protected TroopSpellContainerHelper m_troopSpellContainer; // 0x1F8
	protected TroopCampHelper m_troopCamp; // 0x200
	protected TradeHelper m_trade; // 0x208
	protected GroupHelper m_group; // 0x210
	protected DefenderFactoryHelper m_defenderFactory; // 0x218
	protected GarrisonedDefenderHelper m_garrisonedDefender; // 0x220
	protected NationSelecterHelper m_nationSelecter; // 0x228
	protected SelectedInfoHelper m_selectedInfo; // 0x230
	protected HealthBarHelper m_healthBar; // 0x238
	protected AmmoBarHelper m_ammoBar; // 0x240
	protected EntityColorHelper m_entityColor; // 0x248
	protected ReplaceOnBuiltHelper m_replaceOnBuilt; // 0x250
	protected HuntableSpawnerHelper m_huntableSpawner; // 0x258
	protected DebrisSpawnerHelper m_debrisSpawner; // 0x260
	protected GoodySpawnerHelper m_goodySpawner; // 0x268
	protected TollCollectorHelper m_tollCollector; // 0x270
	protected DraggablePurchaseHelper m_draggablePurchase; // 0x278
	protected DraggablePlacementHelper m_draggablePlacement; // 0x280
	protected ExpandSelectionHelper m_expandSelection; // 0x288
	protected EmissaryHelper m_emissary; // 0x290
	protected LeagueBoatHelper m_leagueBoat; // 0x298
	protected LeagueDockHelper m_leagueDock; // 0x2A0
	protected PortHelper m_port; // 0x2A8
	protected TurtleShipHelper m_turtleShip; // 0x2B0
	protected ExpeditionBoatHelper m_expeditionBoat; // 0x2B8
	protected CrownPassBoatHelper m_crownPassBoat; // 0x2C0
	protected CrownPassBoatContainerHelper m_crownPassBoatContainer; // 0x2C8
	protected EmissaryGuardHelper m_emissaryGuard; // 0x2D0
	protected DonatedTroopsMarkerHelper m_donatedTroopsMarker; // 0x2D8
	protected MercenaryArmyMarkerHelper m_mercenaryArmyMarker; // 0x2E0
	protected TerraCottaArmyMarkerHelper m_terraCottaArmyMarker; // 0x2E8
	protected SummonArmyMarkerHelper m_summonArmyMarker; // 0x2F0
	protected ReinforcementHelper m_reinforcements; // 0x2F8
	protected TendHelper m_tend; // 0x300
	protected BoostHelper m_boost; // 0x308
	protected EventPassBoostHelper m_eventPassBoost; // 0x310
	protected PowerPlantHelper m_powerPlant; // 0x318
	protected ActivationHelper m_activation; // 0x320
	protected TilesetHelper m_tileset; // 0x328
	protected FTUEHelper m_ftue; // 0x330
	protected RoadLootHelper m_roadLoot; // 0x338
	protected VaultHelper m_vault; // 0x340
	protected ShipmentHelper m_shipment; // 0x348
	protected AllianceGateHelper m_allianceGate; // 0x350
	protected EmbassyHelper m_embassy; // 0x358
	protected CoalitionHelper m_coalition; // 0x360
	protected GeneralHelper m_general; // 0x368
	protected GeneralDefenderHelper m_generalDefender; // 0x370
	protected SpellMarkerHelper m_spellMarker; // 0x378
	protected DonatedDefendersHelper m_donatedDefenders; // 0x380
	protected ExpansionDisplayHelper m_expansionDisplay; // 0x388
	protected TroopZoneHelper m_troopZone; // 0x390
	protected TreatyHelper m_treaty; // 0x398
	protected PartisanHelper m_partisan; // 0x3A0
	protected WorldWarLootHelper m_worldWarLoot; // 0x3A8
	protected EventBuildingHelper m_eventBuilding; // 0x3B0
	protected EventGeneralBuildingHelper m_eventGeneralBuilding; // 0x3B8
	protected EventPresentWorkshopHelper m_eventPresentWorkshop; // 0x3C0
	protected EventPresentWorkshopV2Helper m_eventPresentWorkshopV2; // 0x3C8
	protected EventSpellFactoryHelper m_eventSpellFactory; // 0x3D0
	protected EventDonateBuildingHelper m_eventDonateBuilding; // 0x3D8
	protected ActionIconHelper m_actionIconHelper; // 0x3E0
	protected EventFillableBuildingHelper m_eventFillableBuilding; // 0x3E8
	protected EventHarvestHelper m_eventHarvest; // 0x3F0
	protected DecoyTroopHelper m_decoyTroopHelper; // 0x3F8
	protected TravelingMerchantHelper m_travelingMerchant; // 0x400
	protected TravelingMerchantDebrisHelper m_travelingMerchantDebris; // 0x408
	protected GateControlHelper m_gateControlHelper; // 0x410
	protected TKUIScaleHelper m_tkUIScaleHelper; // 0x418
	protected ThreatHelper m_threat; // 0x420
	protected SupplyDropSpawnerHelper m_supplyDropSpawner; // 0x428
	protected SupplyDropHelper m_supplyDrop; // 0x430
	protected DecoyTrapHelper m_decoyTrap; // 0x438
	protected CrownDockHelper m_crownDock; // 0x440
	protected ArchiveHelper m_archiveHelper; // 0x448
	protected RotatableHelper m_rotatableHelper; // 0x450
	protected BastionHelper m_bastion; // 0x458
	protected TroopDamageReductionHelper m_troopDamageReductionHelper; // 0x460
	protected GuitarPickHelper m_guitarPickHelper; // 0x468
	protected ParliamentHelper m_parliament; // 0x470
	protected RoadManagementHelper m_roadManager; // 0x478
	protected DroneFactoryHelper m_droneFactoryHelper; // 0x480
	protected DroneAOOHelper m_droneAOOHelper; // 0x488
	protected FabricatorHelper m_fabricator; // 0x490
	protected DetachmentMarkerHelper m_detachmentMarker; // 0x498
	public IsoBody isobody; // 0x4A0
	public Display display; // 0x4A8
	private Tag m_tag; // 0x4B0
	private Overlay m_overlay; // 0x4B8
	private GroupChild m_groupChild; // 0x4C0
	private MovableArrows m_arrows; // 0x4C8
	public static bool m_scheduleMapEditorFTUE; // 0x20
	public static bool m_scheduleRevolutionFTUE; // 0x21
	public EntityConfig c; // 0x4D0
	public TagEnum currentDisplayAction; // 0x4D8
	private List<TagEnum> m_particleEffectsTracking; // 0x4E0
	private int m_wallAttackerCount; // 0x4E8
	private int m_towerTroopLevel; // 0x4EC
	private bool m_isNationOverriden; // 0x4F0
	private TagEnum m_nationOverride; // 0x4F2
	private TagEnum m_cachedArtset; // 0x4F4
	private bool m_fireBubbleText; // 0x4F6
	private bool m_multiLines; // 0x4F7
	private bool m_isSafeToDismiss; // 0x4F8
	private bool m_buildingRegenerating; // 0x4F9
	private GameObject m_targetedParticle; // 0x500
	private GameObject m_targetedParticleContainer; // 0x508
	private GameObject m_particle; // 0x510
	private GameObject m_particlePersistentBattleFx; // 0x518
	public int debugC; // 0x520
	private Action<EntityController> _onRemoveFromSimulationListener; // 0x528
	private GameObject m_rangeRadiusGraphic; // 0x530
	private GameObject m_minRangeRadiusGraphic; // 0x538
	private GameObject m_damageRangeRadiusGraphic; // 0x540
	private GameObject m_burjHouseRangeRadiusGraphic; // 0x548
	private bool endLockdownParticleCoroutineRunning; // 0x550
	private float m_tradeGoodThreshold; // 0x554
	private EntityController.StolenParticleCounter stolenGoldParticleCounter; // 0x558
	private EntityController.StolenParticleCounter stolenFoodParticleCounter; // 0x560
	private EntityController.StolenParticleCounter stolenOilParticleCounter; // 0x568
	public float troopDamageBeingDoneToMe; // 0x570
	public EntityController myCurrentTroopTarget; // 0x578
	public List<EntityController> troopsAttackingMe; // 0x580
	private bool m_reenableOverlay; // 0x588
	private static readonly IEqualityComparer<EntityController> ID_COMPARER_INSTANCE; // 0x28

	// Properties
	public int uniqueId { get; set; }
	public bool InSimulation { get; set; }
	public int wallAttackerCount { get; set; }
	public bool IsFruitTree { get; }
	public bool IsGoldMine { get; }
	public bool IsMill { get; }
	public bool IsMarket { get; }
	public static IEqualityComparer<EntityController> IdComparer { get; }

	// Methods

	// RVA: 0x1E33480 Offset: 0x1E32480 VA: 0x1E33480
	public int get_uniqueId() { }

	// RVA: 0x1E33488 Offset: 0x1E32488 VA: 0x1E33488
	public void set_uniqueId(int value) { }

	[CompilerGenerated]
	// RVA: 0x1E33490 Offset: 0x1E32490 VA: 0x1E33490
	public bool get_InSimulation() { }

	[CompilerGenerated]
	// RVA: 0x1E33498 Offset: 0x1E32498 VA: 0x1E33498
	private void set_InSimulation(bool value) { }

	// RVA: 0x1E334A4 Offset: 0x1E324A4 VA: 0x1E334A4
	public int get_wallAttackerCount() { }

	// RVA: 0x1E334AC Offset: 0x1E324AC VA: 0x1E334AC
	public void set_wallAttackerCount(int value) { }

	// RVA: 0x1E334B8 Offset: 0x1E324B8 VA: 0x1E334B8
	public bool IsRotatingShooter() { }

	// RVA: 0x1E334CC Offset: 0x1E324CC VA: 0x1E334CC
	public bool HasGridCodeExclusions() { }

	// RVA: 0x1E334F0 Offset: 0x1E324F0 VA: 0x1E334F0
	public List<Vector2> GetGridCodeExclusions(bool addEntityOffset = True) { }

	// RVA: 0x1E33580 Offset: 0x1E32580 VA: 0x1E33580
	public List<Vector2> GetGridCodeExclusions(Vector3 position, TagEnum orientation, bool addEntityOffset = True) { }

	// RVA: 0x1E338DC Offset: 0x1E328DC VA: 0x1E338DC
	public void OnDismiss() { }

	// RVA: 0x1E338F0 Offset: 0x1E328F0 VA: 0x1E338F0
	public void MapEditorSave() { }

	// RVA: 0x1E335B8 Offset: 0x1E325B8 VA: 0x1E335B8
	public List<Vector2> GetCoordinates(Vector3 position, TagEnum orientation, List<Vector2> coordinates, bool addEntityOffset = True) { }

	// RVA: 0x1E3392C Offset: 0x1E3292C VA: 0x1E3392C
	public List<Vector2> GetBlockedCoordinates() { }

	// RVA: 0x1E33904 Offset: 0x1E32904 VA: 0x1E33904
	public bool IsRotatable() { }

	// RVA: 0x1E33914 Offset: 0x1E32914 VA: 0x1E33914
	public List<Vector2> RotateCoordinates(TagEnum orientation, List<Vector2> coordinates) { }

	// RVA: 0x1E3356C Offset: 0x1E3256C VA: 0x1E3356C
	public TagEnum GetOrientation() { }

	// RVA: 0x1E33B48 Offset: 0x1E32B48 VA: 0x1E33B48
	public void SetOrientation(TagEnum orientation) { }

	// RVA: 0x1E33B5C Offset: 0x1E32B5C VA: 0x1E33B5C
	public void InitOnceOnCreateNew(TagEnum entityId) { }

	// RVA: 0x1E36458 Offset: 0x1E35458 VA: 0x1E36458
	public void Fill() { }

	// RVA: 0x1E3646C Offset: 0x1E3546C VA: 0x1E3646C
	public void FreeFill() { }

	// RVA: 0x1E36480 Offset: 0x1E35480 VA: 0x1E36480
	public int GetFillCost() { }

	// RVA: 0x1E36494 Offset: 0x1E35494 VA: 0x1E36494
	public TagEnum GetFillResource() { }

	// RVA: 0x1E364AC Offset: 0x1E354AC VA: 0x1E364AC
	public void TrackParticleEffect(TagEnum particleName) { }

	// RVA: 0x1E36550 Offset: 0x1E35550 VA: 0x1E36550
	public bool UseSixteenDirections() { }

	// RVA: 0x1E3684C Offset: 0x1E3584C VA: 0x1E3684C
	public void UntrackParticleEffect(TagEnum particleName) { }

	// RVA: 0x1E368A4 Offset: 0x1E358A4 VA: 0x1E368A4
	public int NumParticlesTracked(TagEnum particleName) { }

	// RVA: 0x1E369E4 Offset: 0x1E359E4 VA: 0x1E369E4
	public int TotalParticlesTracked() { }

	// RVA: 0x1E36A2C Offset: 0x1E35A2C VA: 0x1E36A2C
	public TroopHelper GetTroopHelper() { }

	// RVA: 0x1E36A34 Offset: 0x1E35A34 VA: 0x1E36A34
	public BuildingHelper GetBuildingHelper() { }

	// RVA: 0x1E36A3C Offset: 0x1E35A3C VA: 0x1E36A3C
	public DefenderFactoryHelper GetDefenderFactoryHelper() { }

	// RVA: 0x1E36A44 Offset: 0x1E35A44 VA: 0x1E36A44
	public WonderHelper GetWonderHelper() { }

	// RVA: 0x1E36A4C Offset: 0x1E35A4C VA: 0x1E36A4C
	public DecoyTrapHelper GetDecoyTrapHelper() { }

	// RVA: 0x1E36A54 Offset: 0x1E35A54 VA: 0x1E36A54
	public void CheckSpeedUpGuitarPick() { }

	// RVA: 0x1E36A68 Offset: 0x1E35A68 VA: 0x1E36A68
	public void OnDefenderDeath(EntityController defender) { }

	// RVA: 0x1E36A7C Offset: 0x1E35A7C VA: 0x1E36A7C
	public void SetCreatingFactory(EntityController creatingFactory) { }

	// RVA: 0x1E36A84 Offset: 0x1E35A84 VA: 0x1E36A84
	public EntityController GetCreatingFactory() { }

	// RVA: 0x1E36A8C Offset: 0x1E35A8C VA: 0x1E36A8C
	public int GetFloodSearchGridCost(GridCostParameters gridParams) { }

	// RVA: 0x1E36ADC Offset: 0x1E35ADC VA: 0x1E36ADC
	public UnitNodeCostData GetUnitNodeCostData() { }

	// RVA: 0x1E36B88 Offset: 0x1E35B88 VA: 0x1E36B88
	public int GetStoreId() { }

	// RVA: 0x1E36BA4 Offset: 0x1E35BA4 VA: 0x1E36BA4
	public void DoDebug() { }

	// RVA: 0x1E36BB8 Offset: 0x1E35BB8 VA: 0x1E36BB8
	public void OnObjectTap(EntityController ec) { }

	// RVA: 0x1E36BBC Offset: 0x1E35BBC VA: 0x1E36BBC
	public void OnTerrainTap(Vector3 isoPoint) { }

	// RVA: 0x1E36BD0 Offset: 0x1E35BD0 VA: 0x1E36BD0
	public void OpenGate(bool onOff) { }

	// RVA: 0x1E36BE8 Offset: 0x1E35BE8 VA: 0x1E36BE8
	public void AddGatesForPath(IList<EntityController> gates) { }

	// RVA: 0x1E36C04 Offset: 0x1E35C04 VA: 0x1E36C04
	public void UpdateAllAdjacentTiles() { }

	// RVA: 0x1E36C50 Offset: 0x1E35C50 VA: 0x1E36C50
	public bool IsPathBlocker() { }

	// RVA: 0x1E36C68 Offset: 0x1E35C68 VA: 0x1E36C68
	public bool IsWall() { }

	// RVA: 0x1E36C90 Offset: 0x1E35C90 VA: 0x1E36C90
	public bool IsBastion() { }

	// RVA: 0x1E36CA0 Offset: 0x1E35CA0 VA: 0x1E36CA0
	public WallType GetWallType() { }

	// RVA: 0x1E36CC8 Offset: 0x1E35CC8 VA: 0x1E36CC8
	public bool IsWallPlacementAllowed() { }

	// RVA: 0x1E36CDC Offset: 0x1E35CDC VA: 0x1E36CDC
	public bool IsBastionPlacementAllowed() { }

	// RVA: 0x1E36CF0 Offset: 0x1E35CF0 VA: 0x1E36CF0
	public bool IsGate() { }

	// RVA: 0x1E36D00 Offset: 0x1E35D00 VA: 0x1E36D00
	public bool IsGateOpen() { }

	// RVA: 0x1E36D14 Offset: 0x1E35D14 VA: 0x1E36D14
	public bool IsGatePlacementAllowed() { }

	// RVA: 0x1E36D28 Offset: 0x1E35D28 VA: 0x1E36D28
	public bool IsCitizenHome() { }

	// RVA: 0x1E36D38 Offset: 0x1E35D38 VA: 0x1E36D38
	public int NumCitizens() { }

	// RVA: 0x1E36D4C Offset: 0x1E35D4C VA: 0x1E36D4C
	public List<EntityController> GetCitizens() { }

	// RVA: 0x1E36D60 Offset: 0x1E35D60 VA: 0x1E36D60
	public List<EntityController> GetNewCitizens() { }

	// RVA: 0x1E36D74 Offset: 0x1E35D74 VA: 0x1E36D74
	public bool IsPlayerStatContainer(TagEnum statName) { }

	// RVA: 0x1E36E00 Offset: 0x1E35E00 VA: 0x1E36E00
	public void OverrideNation(TagEnum nation) { }

	// RVA: 0x1E36E10 Offset: 0x1E35E10 VA: 0x1E36E10
	public TagEnum GetNation() { }

	// RVA: 0x1E36E40 Offset: 0x1E35E40 VA: 0x1E36E40
	public bool IsNationSpecific() { }

	// RVA: 0x1E36E94 Offset: 0x1E35E94 VA: 0x1E36E94
	public bool IsUpgradable() { }

	// RVA: 0x1E36EB4 Offset: 0x1E35EB4 VA: 0x1E36EB4
	public void ForceReenterCurrentState() { }

	// RVA: 0x1E36F08 Offset: 0x1E35F08 VA: 0x1E36F08
	public int GetCurrentLevelAmount(TagEnum attribName) { }

	// RVA: 0x1E36F1C Offset: 0x1E35F1C VA: 0x1E36F1C
	public int GetNextLevelAmount(TagEnum attribName) { }

	// RVA: 0x1E36F30 Offset: 0x1E35F30 VA: 0x1E36F30
	public int GetMaxLevelAmount(TagEnum attribName) { }

	// RVA: 0x1E36F44 Offset: 0x1E35F44 VA: 0x1E36F44
	public List<KeyValuePair<TagEnum, int>> GetQuantityIncreases(int level) { }

	// RVA: 0x1E36FD0 Offset: 0x1E35FD0 VA: 0x1E36FD0
	public List<KeyValuePair<TagEnum, int>> GetNewTroops(int level) { }

	// RVA: 0x1E3705C Offset: 0x1E3605C VA: 0x1E3705C
	public List<TagEnum> GetUnlocks(int level) { }

	// RVA: 0x1E370E8 Offset: 0x1E360E8 VA: 0x1E370E8
	public List<KeyValuePair<TagEnum, int>> GetNewConsumables() { }

	// RVA: 0x1E3716C Offset: 0x1E3616C VA: 0x1E3716C
	public List<KeyValuePair<TagEnum, int>> GetLevelCapIncreases(int level) { }

	// RVA: 0x1E371F8 Offset: 0x1E361F8 VA: 0x1E371F8
	public List<string> GetRecipeUnlocks(int level) { }

	// RVA: 0x1E37284 Offset: 0x1E36284 VA: 0x1E37284
	public List<string> GetTechUnlocks(int level) { }

	// RVA: 0x1E37310 Offset: 0x1E36310 VA: 0x1E37310
	public List<ValueTuple<DirectiveConfig, int, bool>> GetDirectiveUnlocks(int level) { }

	// RVA: 0x1E3739C Offset: 0x1E3639C VA: 0x1E3739C
	public List<TagEnum> GetFabricatorUnlocks(int level) { }

	// RVA: 0x1E37428 Offset: 0x1E36428 VA: 0x1E37428
	public int GetMaxLevel() { }

	// RVA: 0x1E3743C Offset: 0x1E3643C VA: 0x1E3743C
	public bool IsTroop() { }

	// RVA: 0x1E3744C Offset: 0x1E3644C VA: 0x1E3744C
	public bool IsBuilding() { }

	// RVA: 0x1E3745C Offset: 0x1E3645C VA: 0x1E3745C
	public void Pause(bool onOff) { }

	// RVA: 0x1E3746C Offset: 0x1E3646C VA: 0x1E3746C
	public void RetargetAttackers() { }

	// RVA: 0x1E37480 Offset: 0x1E36480 VA: 0x1E37480
	public bool UsesPlaneAI() { }

	// RVA: 0x1E374A0 Offset: 0x1E364A0 VA: 0x1E374A0
	public bool IsMercenary() { }

	// RVA: 0x1E374C0 Offset: 0x1E364C0 VA: 0x1E374C0
	public bool IsParatrooper() { }

	// RVA: 0x1E374C8 Offset: 0x1E364C8 VA: 0x1E374C8
	public bool IsFallingParatrooper() { }

	// RVA: 0x1E3750C Offset: 0x1E3650C VA: 0x1E3750C
	public bool IsGeneral() { }

	// RVA: 0x1E3752C Offset: 0x1E3652C VA: 0x1E3752C
	public bool IsCommando() { }

	// RVA: 0x1E3754C Offset: 0x1E3654C VA: 0x1E3754C
	public bool IsSpy() { }

	// RVA: 0x1E3756C Offset: 0x1E3656C VA: 0x1E3756C
	public bool IsBatteringRam() { }

	// RVA: 0x1E3758C Offset: 0x1E3658C VA: 0x1E3758C
	public bool IgnoresLibraryTechs() { }

	// RVA: 0x1E375AC Offset: 0x1E365AC VA: 0x1E375AC
	public bool IsDecoy() { }

	// RVA: 0x1E375CC Offset: 0x1E365CC VA: 0x1E375CC
	public bool IsAirHoverUnit() { }

	// RVA: 0x1E375EC Offset: 0x1E365EC VA: 0x1E375EC
	public bool IsDrone() { }

	// RVA: 0x1E3760C Offset: 0x1E3660C VA: 0x1E3760C
	public bool ShouldIgnoreInCombat() { }

	// RVA: 0x1E3762C Offset: 0x1E3662C VA: 0x1E3762C
	public bool ShouldSnapToCamp() { }

	// RVA: 0x1E3764C Offset: 0x1E3664C VA: 0x1E3764C
	public bool IsConnectedToTownHall() { }

	// RVA: 0x1E3768C Offset: 0x1E3668C VA: 0x1E3768C
	public bool HasUsedBatteringRamSkill() { }

	// RVA: 0x1E376AC Offset: 0x1E366AC VA: 0x1E376AC
	public void SetUsedBatteringRamSkill() { }

	// RVA: 0x1E376C0 Offset: 0x1E366C0 VA: 0x1E376C0
	public void InvalidateCachedArtset() { }

	// RVA: 0x1E376C8 Offset: 0x1E366C8 VA: 0x1E376C8
	public TagEnum GetArtset() { }

	// RVA: 0x1E37A04 Offset: 0x1E36A04 VA: 0x1E37A04
	public void OnWallDeath() { }

	// RVA: 0x1E365A4 Offset: 0x1E355A4 VA: 0x1E365A4
	public int GetLevel() { }

	// RVA: 0x1E37A50 Offset: 0x1E36A50 VA: 0x1E37A50
	public void TryToApplyPlayerBoost() { }

	// RVA: 0x1E37A64 Offset: 0x1E36A64 VA: 0x1E37A64
	public void RemovePlayerBoost() { }

	// RVA: 0x1E37A78 Offset: 0x1E36A78 VA: 0x1E37A78
	public void SetEventPassBoostHelperId(string eventId) { }

	// RVA: 0x1E37A8C Offset: 0x1E36A8C VA: 0x1E37A8C
	public string GetBoostEventId() { }

	// RVA: 0x1E37AE0 Offset: 0x1E36AE0 VA: 0x1E37AE0
	public void TryUpdateFunkyBoosts() { }

	// RVA: 0x1E37AF4 Offset: 0x1E36AF4 VA: 0x1E37AF4
	public void CheckBoostUses() { }

	// RVA: 0x1E37B08 Offset: 0x1E36B08 VA: 0x1E37B08
	public void SetDetachment(int detachmentI) { }

	// RVA: 0x1E37B18 Offset: 0x1E36B18 VA: 0x1E37B18
	public int GetDetachment() { }

	// RVA: 0x1E37B30 Offset: 0x1E36B30 VA: 0x1E37B30
	public void SetLevel(int level) { }

	// RVA: 0x1E37BC4 Offset: 0x1E36BC4 VA: 0x1E37BC4
	public void SetBoostLevel(int level) { }

	// RVA: 0x1E37BD8 Offset: 0x1E36BD8 VA: 0x1E37BD8
	public void SetWWDotCoalitionData(bool hasParliament, bool isWWEnemy) { }

	// RVA: 0x1E37BF4 Offset: 0x1E36BF4 VA: 0x1E37BF4
	public void UpdateNonPersistentStats(bool showMessage = False) { }

	// RVA: 0x1E37C0C Offset: 0x1E36C0C VA: 0x1E37C0C
	public int NumCitizensConsumed() { }

	// RVA: 0x1E37CC0 Offset: 0x1E36CC0 VA: 0x1E37CC0
	public int GetNumCitizenUsed() { }

	// RVA: 0x1E37D74 Offset: 0x1E36D74 VA: 0x1E37D74
	public int GetOriginalNumCitizens() { }

	// RVA: 0x1E37E28 Offset: 0x1E36E28 VA: 0x1E37E28
	public int GetSelectionNumCitizensConsumed(bool useAlternateResource) { }

	// RVA: 0x1E37E40 Offset: 0x1E36E40 VA: 0x1E37E40
	public int GetSelectionOriginalUpgradeCitizenCost(bool useAlternateResource) { }

	// RVA: 0x1E37E58 Offset: 0x1E36E58 VA: 0x1E37E58
	public List<DisplayAssetConfig> GetExpansionAssetConfigs(string collectionName) { }

	// RVA: 0x1E37ED0 Offset: 0x1E36ED0 VA: 0x1E37ED0
	public void HideDefenders() { }

	// RVA: 0x1E37F04 Offset: 0x1E36F04 VA: 0x1E37F04
	public void ShowDefenders() { }

	// RVA: 0x1E37F38 Offset: 0x1E36F38 VA: 0x1E37F38
	public string GetJobType() { }

	// RVA: 0x1E38054 Offset: 0x1E37054 VA: 0x1E38054
	public bool IsUpgrading() { }

	// RVA: 0x1E38090 Offset: 0x1E37090 VA: 0x1E38090
	public void OnNewWorldWakeup() { }

	// RVA: 0x1E381F4 Offset: 0x1E371F4 VA: 0x1E381F4
	public bool IsTechProcessed() { }

	// RVA: 0x1E38214 Offset: 0x1E37214 VA: 0x1E38214
	public void ApplyLibraryDependentStats() { }

	// RVA: 0x1E38298 Offset: 0x1E37298 VA: 0x1E38298
	public void ApplyLibraryDependentWonderStats(TagEnum techId) { }

	// RVA: 0x1E382E0 Offset: 0x1E372E0 VA: 0x1E382E0
	public bool IsBeingCleared() { }

	// RVA: 0x1E38308 Offset: 0x1E37308 VA: 0x1E38308
	public int GetNumExpansionsBeingCleared() { }

	// RVA: 0x1E3831C Offset: 0x1E3731C VA: 0x1E3831C
	public double GetNextClearTime() { }

	// RVA: 0x1E38334 Offset: 0x1E37334 VA: 0x1E38334
	public void OnRoadConnectionBuffRemove(bool showMessage) { }

	// RVA: 0x1E38794 Offset: 0x1E37794 VA: 0x1E38794
	public void OnRoadConnectionBuffAdd(bool showMessage) { }

	// RVA: 0x1E38A38 Offset: 0x1E37A38 VA: 0x1E38A38
	public int GetWonderRoadBonus(bool withTechModifier = True) { }

	// RVA: 0x1E38A64 Offset: 0x1E37A64 VA: 0x1E38A64
	public int GetWonderRoadTechBonus() { }

	// RVA: 0x1E38A78 Offset: 0x1E37A78 VA: 0x1E38A78
	public Bounds GetJobSite() { }

	// RVA: 0x1E38BBC Offset: 0x1E37BBC VA: 0x1E38BBC
	public Bounds GetWorkSite() { }

	// RVA: 0x1E38C20 Offset: 0x1E37C20 VA: 0x1E38C20
	public bool IsJobComplete(JobType jobType = 0, int masterId = 0) { }

	// RVA: 0x1E38D30 Offset: 0x1E37D30 VA: 0x1E38D30
	public double GetJobTimeTotal(int rampingOffset = 0) { }

	// RVA: 0x1E38DE8 Offset: 0x1E37DE8 VA: 0x1E38DE8
	public bool IsFreeUnderFive() { }

	// RVA: 0x1E38E24 Offset: 0x1E37E24 VA: 0x1E38E24
	public bool FreeUnderFive() { }

	// RVA: 0x1E39214 Offset: 0x1E38214 VA: 0x1E39214
	public int GetTrainingBuyThroughCost() { }

	// RVA: 0x1E3923C Offset: 0x1E3823C VA: 0x1E3923C
	public bool CanTrain() { }

	// RVA: 0x1E392B4 Offset: 0x1E382B4 VA: 0x1E392B4
	public int GetJobBuyThroughCost(int numCitizens = 0) { }

	// RVA: 0x1E39410 Offset: 0x1E38410 VA: 0x1E39410
	public int GetUpgradeBuyThroughCost() { }

	// RVA: 0x1E394D8 Offset: 0x1E384D8 VA: 0x1E394D8
	public int GetJobBuyThroughCostArmory(bool firstSlot) { }

	// RVA: 0x1E395C0 Offset: 0x1E385C0 VA: 0x1E395C0
	public int GetJobBuyThroughCostLibrary(bool firstSlot) { }

	// RVA: 0x1E396A0 Offset: 0x1E386A0 VA: 0x1E396A0
	public KeyValuePair<TagEnum, int> GetRefund() { }

	// RVA: 0x1E396DC Offset: 0x1E386DC VA: 0x1E396DC
	public bool IsWonder() { }

	// RVA: 0x1E396EC Offset: 0x1E386EC VA: 0x1E396EC
	public void ResetBonusCooldown() { }

	// RVA: 0x1E3928C Offset: 0x1E3828C VA: 0x1E3928C
	public bool IsConstructed() { }

	// RVA: 0x1E36418 Offset: 0x1E35418 VA: 0x1E36418
	public bool Moves() { }

	// RVA: 0x1E39720 Offset: 0x1E38720 VA: 0x1E39720
	public int NumStages() { }

	// RVA: 0x1E39734 Offset: 0x1E38734 VA: 0x1E39734
	public int NumStagesComplete() { }

	// RVA: 0x1E39748 Offset: 0x1E38748 VA: 0x1E39748
	public Bounds GetAttackBounds() { }

	// RVA: 0x1E39788 Offset: 0x1E38788 VA: 0x1E39788
	public Bounds GetBounds() { }

	// RVA: 0x1E397C8 Offset: 0x1E387C8 VA: 0x1E397C8
	public void ForceToBeReady() { }

	// RVA: 0x1E397FC Offset: 0x1E387FC VA: 0x1E397FC
	internal void OnPlacedInMapEditor() { }

	// RVA: 0x1E39810 Offset: 0x1E38810 VA: 0x1E39810
	public void DoDoubleTapAction() { }

	// RVA: 0x1E3A3B4 Offset: 0x1E393B4 VA: 0x1E3A3B4
	private void FillMapEditorActionIconDataList(List<ActionIconData> actionIconDataList) { }

	// RVA: 0x1E3A82C Offset: 0x1E3982C VA: 0x1E3A82C
	public HashSet<EntityController> GetExpandedSelection() { }

	// RVA: 0x1E3A844 Offset: 0x1E39844 VA: 0x1E3A844
	public void FillActionIconDataList(List<ActionIconData> actionIconDataList) { }

	// RVA: 0x1E3B03C Offset: 0x1E3A03C VA: 0x1E3B03C
	public List<ActionIconData> GetActionIconDataList() { }

	// RVA: 0x1E3B0BC Offset: 0x1E3A0BC VA: 0x1E3B0BC
	public void SetSpellMarker(SpellMarkerHelper spell) { }

	// RVA: 0x1E3B0CC Offset: 0x1E3A0CC VA: 0x1E3B0CC
	public void SetShipmentName(string shipmentName) { }

	// RVA: 0x1E3B0DC Offset: 0x1E3A0DC VA: 0x1E3B0DC
	public string GetShipmentName() { }

	// RVA: 0x1E3B130 Offset: 0x1E3A130 VA: 0x1E3B130
	public void MakeEmissaryWait() { }

	// RVA: 0x1E3B144 Offset: 0x1E3A144 VA: 0x1E3B144
	public void StartEmissaryMove() { }

	// RVA: 0x1E3B158 Offset: 0x1E3A158 VA: 0x1E3B158
	public void SetEmissaryGuardWaypointDestination(Vector3 destination) { }

	// RVA: 0x1E3B16C Offset: 0x1E3A16C VA: 0x1E3B16C
	public void CancelShipment() { }

	// RVA: 0x1E3B1A0 Offset: 0x1E3A1A0 VA: 0x1E3B1A0
	public EntityController DropLoot() { }

	// RVA: 0x1E3B1B4 Offset: 0x1E3A1B4 VA: 0x1E3B1B4
	public void SetMaxHealth(int amount) { }

	// RVA: 0x1E3B1D4 Offset: 0x1E3A1D4 VA: 0x1E3B1D4
	public void InitLoot(EntityController caravan) { }

	// RVA: 0x1E3B208 Offset: 0x1E3A208 VA: 0x1E3B208
	public void ProcessLootTaken() { }

	// RVA: 0x1E3B21C Offset: 0x1E3A21C VA: 0x1E3B21C
	public bool CanHunt() { }

	// RVA: 0x1E3B230 Offset: 0x1E3A230 VA: 0x1E3B230
	public void StartHunt() { }

	// RVA: 0x1E3B264 Offset: 0x1E3A264 VA: 0x1E3B264
	public void FinishHunt() { }

	// RVA: 0x1E3B278 Offset: 0x1E3A278 VA: 0x1E3B278
	public bool HasCitizenAttackFinishedHunt(int damage) { }

	// RVA: 0x1E3B290 Offset: 0x1E3A290 VA: 0x1E3B290
	public void OnEnterJobState() { }

	// RVA: 0x1E3B2A4 Offset: 0x1E3A2A4 VA: 0x1E3B2A4
	public void RenderTick() { }

	// RVA: 0x1E3B3E4 Offset: 0x1E3A3E4 VA: 0x1E3B3E4
	public bool IsRegenerating() { }

	// RVA: 0x1E3B3EC Offset: 0x1E3A3EC VA: 0x1E3B3EC
	public void SimTick(bool forceSimulation = False) { }

	// RVA: 0x1E40038 Offset: 0x1E3F038 VA: 0x1E40038
	public bool IsHuntableSpawner() { }

	// RVA: 0x1E40048 Offset: 0x1E3F048 VA: 0x1E40048
	public void SetStagTime() { }

	// RVA: 0x1E4005C Offset: 0x1E3F05C VA: 0x1E4005C
	public int MaxAnimals() { }

	// RVA: 0x1E40070 Offset: 0x1E3F070 VA: 0x1E40070
	public void SpawnAnimal(TagEnum animal) { }

	// RVA: 0x1E385D8 Offset: 0x1E375D8 VA: 0x1E385D8
	public void AddBubbleText(string message) { }

	// RVA: 0x1E40084 Offset: 0x1E3F084 VA: 0x1E40084
	public int ApproximateCostToTarget(FloodNode node, int endX, int endY) { }

	// RVA: 0x1E40158 Offset: 0x1E3F158 VA: 0x1E40158
	public bool CanContribute() { }

	// RVA: 0x1E4016C Offset: 0x1E3F16C VA: 0x1E4016C
	public TagEnum GetContributionType() { }

	// RVA: 0x1E40180 Offset: 0x1E3F180 VA: 0x1E40180
	public int GetContributionAmount() { }

	// RVA: 0x1E40194 Offset: 0x1E3F194 VA: 0x1E40194
	public void StartConstruction() { }

	// RVA: 0x1E401A8 Offset: 0x1E3F1A8 VA: 0x1E401A8
	public void StartChange() { }

	// RVA: 0x1E401BC Offset: 0x1E3F1BC VA: 0x1E401BC
	public void StartReplace() { }

	// RVA: 0x1E401D0 Offset: 0x1E3F1D0 VA: 0x1E401D0
	public void InstantContributeResources() { }

	// RVA: 0x1E401E4 Offset: 0x1E3F1E4 VA: 0x1E401E4
	public void ContributeResources() { }

	// RVA: 0x1E401FC Offset: 0x1E3F1FC VA: 0x1E401FC
	public void SetContributedResources(int amount) { }

	// RVA: 0x1E40210 Offset: 0x1E3F210 VA: 0x1E40210
	public int GetStageXp() { }

	// RVA: 0x1E40224 Offset: 0x1E3F224 VA: 0x1E40224
	public void ReplaceWonder(TagEnum type, bool take_resources = False, bool instant = False) { }

	// RVA: 0x1E40240 Offset: 0x1E3F240 VA: 0x1E40240
	public void ProcessWonderPower(WonderHelper.IncomingData id) { }

	// RVA: 0x1E40254 Offset: 0x1E3F254 VA: 0x1E40254
	public int GetWonderHarvestBonus() { }

	// RVA: 0x1E40268 Offset: 0x1E3F268 VA: 0x1E40268
	public bool HasNextUpgradeLevel() { }

	// RVA: 0x1E4027C Offset: 0x1E3F27C VA: 0x1E4027C
	public bool CanUpgrade(bool useAlternateResource = False) { }

	// RVA: 0x1E40294 Offset: 0x1E3F294 VA: 0x1E40294
	public void InvalidateCaches() { }

	// RVA: 0x1E402B0 Offset: 0x1E3F2B0 VA: 0x1E402B0
	public bool AtMaxUpgrades() { }

	// RVA: 0x1E402C4 Offset: 0x1E3F2C4 VA: 0x1E402C4
	public void CancelUpgrade() { }

	// RVA: 0x1E402F8 Offset: 0x1E3F2F8 VA: 0x1E402F8
	public void StartInstantUpgrade(bool useAlternateResource = False) { }

	// RVA: 0x1E40390 Offset: 0x1E3F390 VA: 0x1E40390
	public void StartUpgrade(bool savePlayerState = True, bool useAlternateResource = False) { }

	// RVA: 0x1E40448 Offset: 0x1E3F448 VA: 0x1E40448
	public void StartUpgrade_WithPay(bool savePlayerState = True, bool useAlternateResource = False) { }

	// RVA: 0x1E40494 Offset: 0x1E3F494 VA: 0x1E40494
	public void StartUpgrade_WithoutPay(bool savePlayerState = True, bool useAlternateResource = False) { }

	// RVA: 0x1E404EC Offset: 0x1E3F4EC VA: 0x1E404EC
	public void UpgradeWithManuals() { }

	// RVA: 0x1E40540 Offset: 0x1E3F540 VA: 0x1E40540
	public void InstantUpgrade() { }

	// RVA: 0x1E40554 Offset: 0x1E3F554 VA: 0x1E40554
	public void ForceUpdateAdjacentTiles() { }

	// RVA: 0x1E405A8 Offset: 0x1E3F5A8 VA: 0x1E405A8
	public void ForcedUpdateDisplay() { }

	// RVA: 0x1E4061C Offset: 0x1E3F61C VA: 0x1E4061C
	public void UpdateDisplay(bool forceSimulation = False) { }

	// RVA: 0x1E408D4 Offset: 0x1E3F8D4 VA: 0x1E408D4
	public void UpdateGuitarPicks() { }

	// RVA: 0x1E408E8 Offset: 0x1E3F8E8 VA: 0x1E408E8
	public void UpdateSecondaryDisplay() { }

	// RVA: 0x1E4098C Offset: 0x1E3F98C VA: 0x1E4098C
	public string GetDisplayPrefix(bool forceSimulation = False) { }

	// RVA: 0x1E40A60 Offset: 0x1E3FA60 VA: 0x1E40A60
	public string GetUIDisplayState(bool forceSimulation = False) { }

	// RVA: 0x1E40BBC Offset: 0x1E3FBBC VA: 0x1E40BBC
	public Dictionary<Vector3, List<DisplayAssetConfig>> GetGroupUIDisplays(bool forceSimulation = False) { }

	// RVA: 0x1E41678 Offset: 0x1E40678 VA: 0x1E41678
	public string GetDestroyedState() { }

	// RVA: 0x1E41784 Offset: 0x1E40784 VA: 0x1E41784
	public void FireDestroyParticles(Vector3 location, int radius) { }

	// RVA: 0x1E41834 Offset: 0x1E40834 VA: 0x1E41834
	public Dictionary<Vector3, SpriteConfig> GetGarrisonedSoldierUIDisplays() { }

	// RVA: 0x1E41B68 Offset: 0x1E40B68 VA: 0x1E41B68
	public float GetUIScaleAdvisor() { }

	// RVA: 0x1E41B80 Offset: 0x1E40B80 VA: 0x1E41B80
	public float GetUIScaleInfo() { }

	// RVA: 0x1E41B98 Offset: 0x1E40B98 VA: 0x1E41B98
	public float GetUIScaleRecommendation() { }

	// RVA: 0x1E41BB0 Offset: 0x1E40BB0 VA: 0x1E41BB0
	public float GetUIScaleInventory() { }

	// RVA: 0x1E41BC8 Offset: 0x1E40BC8 VA: 0x1E41BC8
	public float GetUIScaleMapEditor() { }

	// RVA: 0x1E41BE0 Offset: 0x1E40BE0 VA: 0x1E41BE0
	public float GetUIScaleShowMe() { }

	// RVA: 0x1E41BFC Offset: 0x1E40BFC VA: 0x1E41BFC
	public bool UsesTKUIOffsets() { }

	// RVA: 0x1E41C10 Offset: 0x1E40C10 VA: 0x1E41C10
	public Vector2 GetTKUIOffsets() { }

	// RVA: 0x1E41C64 Offset: 0x1E40C64 VA: 0x1E41C64
	public void RemoveFromSimulation() { }

	// RVA: 0x1E41DD0 Offset: 0x1E40DD0 VA: 0x1E41DD0
	public bool IsSafeToDismiss() { }

	// RVA: 0x1E41DD8 Offset: 0x1E40DD8 VA: 0x1E41DD8
	public void RegenerateHealth() { }

	// RVA: 0x1E42154 Offset: 0x1E41154 VA: 0x1E42154
	public void FireParticleInForward(TagEnum particleName, float heightOffset) { }

	// RVA: 0x1E42160 Offset: 0x1E41160 VA: 0x1E42160
	public void FireParticleInForward(TagEnum particleName, float heightOffset, float offsetX, float offsetY) { }

	// RVA: 0x1E42500 Offset: 0x1E41500 VA: 0x1E42500
	public void TargetWithParticle(TagEnum particleName, EntityController target, float heightOffset = 0) { }

	// RVA: 0x1E4254C Offset: 0x1E4154C VA: 0x1E4254C
	public void TargetWithParticle(TagEnum particleName, Vector3 targetPosition, float heightOffset = 0) { }

	// RVA: 0x1E429FC Offset: 0x1E419FC VA: 0x1E429FC
	public void StopTargetedParticle() { }

	[IteratorStateMachine(typeof(EntityController.<_RegenerateBuilding>d__428))]
	// RVA: 0x1E420B8 Offset: 0x1E410B8 VA: 0x1E420B8
	private IEnumerator _RegenerateBuilding() { }

	// RVA: 0x1E42AA0 Offset: 0x1E41AA0 VA: 0x1E42AA0
	public bool HasParticle() { }

	// RVA: 0x1E42B00 Offset: 0x1E41B00 VA: 0x1E42B00
	public bool IsPlayingParticle(TagEnum particleType) { }

	// RVA: 0x1E42BA8 Offset: 0x1E41BA8 VA: 0x1E42BA8
	public void PlayParticle(TagEnum particleName, bool isPersistentBattleFx = False) { }

	// RVA: 0x1E42F68 Offset: 0x1E41F68 VA: 0x1E42F68
	public void DestroySimpleConfirmationOverlay() { }

	// RVA: 0x1E43058 Offset: 0x1E42058 VA: 0x1E43058
	public float GetApproximateDamageDoneToTarget_FAST(EntityController target) { }

	// RVA: 0x1E43070 Offset: 0x1E42070 VA: 0x1E43070
	public float GetBaseDamage() { }

	// RVA: 0x1E430D8 Offset: 0x1E420D8 VA: 0x1E430D8
	public DamageModifiers GetAttackModifiers(EntityController target) { }

	// RVA: 0x1E4319C Offset: 0x1E4219C VA: 0x1E4319C
	public DamageModifiers GetDefenseModifiers(EntityController attacker) { }

	// RVA: 0x1E42E68 Offset: 0x1E41E68 VA: 0x1E42E68
	public void StopParticle(TagEnum particleType = 0) { }

	// RVA: 0x1E43230 Offset: 0x1E42230 VA: 0x1E43230
	public void UpdatePersistentBattleFxParticle(TagEnum direction) { }

	// RVA: 0x1E43324 Offset: 0x1E42324 VA: 0x1E43324
	public void OnPreAddedToSimulation() { }

	// RVA: 0x1E43338 Offset: 0x1E42338 VA: 0x1E43338
	public void GarrisonDefenderInit() { }

	// RVA: 0x1E43354 Offset: 0x1E42354 VA: 0x1E43354
	public void OnAddedToSimulation() { }

	// RVA: 0x1E43B78 Offset: 0x1E42B78 VA: 0x1E43B78
	public void OnPostAddedToSimulation() { }

	// RVA: 0x1E43D2C Offset: 0x1E42D2C VA: 0x1E43D2C
	public void DoDamageEffect(float damageAmount) { }

	// RVA: 0x1E43D40 Offset: 0x1E42D40 VA: 0x1E43D40
	public double GetJobStartTime() { }

	// RVA: 0x1E43D58 Offset: 0x1E42D58 VA: 0x1E43D58
	public double GetJobTimeLeftAll() { }

	// RVA: 0x1E39490 Offset: 0x1E38490 VA: 0x1E39490
	public double GetUpgradeTimeLeft() { }

	// RVA: 0x1E38F5C Offset: 0x1E37F5C VA: 0x1E38F5C
	public double GetJobTimeLeft(JobType jobType = 0) { }

	// RVA: 0x1E43DAC Offset: 0x1E42DAC VA: 0x1E43DAC
	public bool HasTimeScale() { }

	// RVA: 0x1E43DFC Offset: 0x1E42DFC VA: 0x1E43DFC
	public bool HasArmoryTimeScale(TagEnum type) { }

	// RVA: 0x1E43E10 Offset: 0x1E42E10 VA: 0x1E43E10
	public bool HasArmoryTimeScale(bool firstSlot) { }

	// RVA: 0x1E39568 Offset: 0x1E38568 VA: 0x1E39568
	public double GetJobTimeLeftArmory(bool firstSlot) { }

	// RVA: 0x1E39650 Offset: 0x1E38650 VA: 0x1E39650
	public double GetJobTimeLeftLibrary(bool firstSlot) { }

	// RVA: 0x1E43E28 Offset: 0x1E42E28 VA: 0x1E43E28
	public double GetHarvestTimeLeft() { }

	// RVA: 0x1E43E40 Offset: 0x1E42E40 VA: 0x1E43E40
	public void UpdateJobTime(double delta) { }

	// RVA: 0x1E43EA0 Offset: 0x1E42EA0 VA: 0x1E43EA0
	public bool HasUpgradedArtifacts() { }

	// RVA: 0x1E43F14 Offset: 0x1E42F14 VA: 0x1E43F14
	public bool HasHarvestableCraftingItems(out bool componentsToHarvest) { }

	// RVA: 0x1E43F50 Offset: 0x1E42F50 VA: 0x1E43F50
	public void ApplyAllianceHelpSpeedup() { }

	// RVA: 0x1E43FE8 Offset: 0x1E42FE8 VA: 0x1E43FE8
	public void ResetTends() { }

	// RVA: 0x1E43FFC Offset: 0x1E42FFC VA: 0x1E43FFC
	public int GetNumTends() { }

	// RVA: 0x1E44010 Offset: 0x1E43010 VA: 0x1E44010
	public void AddTend() { }

	// RVA: 0x1E44024 Offset: 0x1E43024 VA: 0x1E44024
	private void DoTownHallLevelUp() { }

	// RVA: 0x1E4514C Offset: 0x1E4414C VA: 0x1E4514C
	public void RecalculateRearmCost() { }

	// RVA: 0x1E45160 Offset: 0x1E44160 VA: 0x1E45160
	public void SetAttackMode(bool onOff) { }

	// RVA: 0x1E45184 Offset: 0x1E44184 VA: 0x1E45184
	public void SetHomeMode(bool onOff) { }

	// RVA: 0x1E4519C Offset: 0x1E4419C VA: 0x1E4519C
	public void OnAttackModeInit() { }

	// RVA: 0x1E451A8 Offset: 0x1E441A8 VA: 0x1E451A8
	public void SetCanSurvive(bool canSurvive) { }

	// RVA: 0x1E451BC Offset: 0x1E441BC VA: 0x1E451BC
	public bool CanSurvive() { }

	// RVA: 0x1E451DC Offset: 0x1E441DC VA: 0x1E451DC
	public void LevelUp(bool instantUpgrade = False, bool useAlternateResource = False, bool doRebate = True, bool ignoreSelection = False, bool savePlayerState = True) { }

	// RVA: 0x1E45C5C Offset: 0x1E44C5C VA: 0x1E45C5C
	public void SetNonPersistentItemStats(int level) { }

	// RVA: 0x1E45C70 Offset: 0x1E44C70 VA: 0x1E45C70
	public float GetUpgradeTime() { }

	// RVA: 0x1E45C88 Offset: 0x1E44C88 VA: 0x1E45C88
	public float GetUpgradeTimeWithBurjBonus() { }

	// RVA: 0x1E45CA0 Offset: 0x1E44CA0 VA: 0x1E45CA0
	public TagEnum GetUpgradeType() { }

	// RVA: 0x1E45CB4 Offset: 0x1E44CB4 VA: 0x1E45CB4
	public int GetUpgradeAmount() { }

	// RVA: 0x1E45CC8 Offset: 0x1E44CC8 VA: 0x1E45CC8
	public int GetOriginalCostAmount() { }

	// RVA: 0x1E45D04 Offset: 0x1E44D04 VA: 0x1E45D04
	public int GetPercentDiscount() { }

	// RVA: 0x1E45D40 Offset: 0x1E44D40 VA: 0x1E45D40
	public TagEnum GetAlternateUpgradeType() { }

	// RVA: 0x1E45D54 Offset: 0x1E44D54 VA: 0x1E45D54
	public int GetAlternateUpgradeAmount() { }

	// RVA: 0x1E45D68 Offset: 0x1E44D68 VA: 0x1E45D68
	public int GetOriginalAlternateUpgradeAmount() { }

	// RVA: 0x1E45D7C Offset: 0x1E44D7C VA: 0x1E45D7C
	public int GetAlternatePercentDiscount() { }

	// RVA: 0x1E45D90 Offset: 0x1E44D90 VA: 0x1E45D90
	public int PSCRemove(TagEnum statName, int amount) { }

	// RVA: 0x1E45E64 Offset: 0x1E44E64 VA: 0x1E45E64
	public int PSCAdd(TagEnum statName, int amount) { }

	// RVA: 0x1E46374 Offset: 0x1E45374 VA: 0x1E46374
	public int PSCGet(TagEnum statName) { }

	// RVA: 0x1E4641C Offset: 0x1E4541C VA: 0x1E4641C
	public float GetTotalDamageDealt() { }

	// RVA: 0x1E464DC Offset: 0x1E454DC VA: 0x1E464DC
	public float GetBuildingDamageDealt() { }

	// RVA: 0x1E46534 Offset: 0x1E45534 VA: 0x1E46534
	public float GetDefenderDamageDealt() { }

	// RVA: 0x1E45130 Offset: 0x1E44130 VA: 0x1E45130
	public void DoInteruptingAnimation(TagEnum action, float time) { }

	// RVA: 0x1E465C0 Offset: 0x1E455C0 VA: 0x1E465C0
	public void DoInteruptingAnimation_FreezeAtEnd(TagEnum action, float time) { }

	// RVA: 0x1E465DC Offset: 0x1E455DC VA: 0x1E465DC
	public TagEnum GetLastDisplayStateAction() { }

	// RVA: 0x1E465F8 Offset: 0x1E455F8 VA: 0x1E465F8
	public void CancelInterruptingAnimation_EndWithCurrentAction() { }

	// RVA: 0x1E46614 Offset: 0x1E45614 VA: 0x1E46614
	public void ChangeDisplayState(TagEnum action, TagEnum rotation = 0) { }

	// RVA: 0x1E46730 Offset: 0x1E45730 VA: 0x1E46730
	public void UpdateDisplayState() { }

	// RVA: 0x1E46A0C Offset: 0x1E45A0C VA: 0x1E46A0C
	public TagEnum GetGender() { }

	// RVA: 0x1E46A24 Offset: 0x1E45A24 VA: 0x1E46A24
	public void RemovePersistentBattleFx() { }

	// RVA: 0x1E46A38 Offset: 0x1E45A38 VA: 0x1E46A38
	public void AddListenerOnRemoveFromSimulation(Action<EntityController> callback) { }

	// RVA: 0x1E46A40 Offset: 0x1E45A40 VA: 0x1E46A40
	public void OnRemovedFromSimulation() { }

	// RVA: 0x1E47150 Offset: 0x1E46150 VA: 0x1E47150
	public void OnEntityUpgraded(Dictionary<string, object> data) { }

	// RVA: 0x1E4719C Offset: 0x1E4619C VA: 0x1E4719C
	public Tag GetTagComponent() { }

	// RVA: 0x1E471A4 Offset: 0x1E461A4 VA: 0x1E471A4
	public GroupChild GetGroupChild() { }

	// RVA: 0x1E471AC Offset: 0x1E461AC VA: 0x1E471AC
	public Overlay GetOverlayComponent() { }

	// RVA: 0x1E471B4 Offset: 0x1E461B4 VA: 0x1E471B4
	public void LoadPersistenceData(object jsonEntity) { }

	// RVA: 0x1E48AE0 Offset: 0x1E47AE0 VA: 0x1E48AE0
	public void RemoveNonPersistentStats() { }

	// RVA: 0x1E48AF4 Offset: 0x1E47AF4 VA: 0x1E48AF4
	public void DeltaCitizensReadyToAttack(int val) { }

	// RVA: 0x1E48B0C Offset: 0x1E47B0C VA: 0x1E48B0C
	public void LoadUpgradeHelper(object obj) { }

	// RVA: 0x1E48BC8 Offset: 0x1E47BC8 VA: 0x1E48BC8
	public void LoadStatsHelper(object obj) { }

	// RVA: 0x1E48C84 Offset: 0x1E47C84 VA: 0x1E48C84
	public void FillInfoBarDataList(List<InfoBarData> infoBarDataList, bool includeBonuses = False, bool simple = False) { }

	// RVA: 0x1E37A28 Offset: 0x1E36A28 VA: 0x1E37A28
	public bool IsTowerTroop() { }

	// RVA: 0x1E4906C Offset: 0x1E4806C VA: 0x1E4906C
	public bool IsTower() { }

	// RVA: 0x1E4908C Offset: 0x1E4808C VA: 0x1E4908C
	public void SetIsExpandSelectionChild(bool value) { }

	// RVA: 0x1E490B0 Offset: 0x1E480B0 VA: 0x1E490B0
	public bool IsChildInExpandSelection() { }

	// RVA: 0x1E490E0 Offset: 0x1E480E0 VA: 0x1E490E0
	public bool IsParentInExpandSelection() { }

	// RVA: 0x1E49144 Offset: 0x1E48144 VA: 0x1E49144
	public bool IsRebateAvailable() { }

	// RVA: 0x1E4922C Offset: 0x1E4822C VA: 0x1E4922C
	public bool IsInUpgradeableEntitiesSelection(bool useAlternateResource) { }

	// RVA: 0x1E492B8 Offset: 0x1E482B8 VA: 0x1E492B8
	public void GiveRebate_Upgrade_SingleAndExpandedSelection(bool isUpgrade = True, int numPreviousAdded = 0, bool isLastRoad = False, bool useAlternateResource = False) { }

	// RVA: 0x1E496A8 Offset: 0x1E486A8 VA: 0x1E496A8
	private void DoRebate(EntityController ec, ref TagEnum key, ref int total_value, bool isUpdate = True, int numPreviousAdded = 0, bool useAlternateResource = False) { }

	// RVA: 0x1E49840 Offset: 0x1E48840 VA: 0x1E49840
	private void DoRebateMessage(TagEnum rebate_key, int rebate_value, bool isUpdate = True) { }

	// RVA: 0x1E49A08 Offset: 0x1E48A08 VA: 0x1E49A08
	public void Upgrade_ExpandedSelection(bool useAlternateResource) { }

	// RVA: 0x1E49C08 Offset: 0x1E48C08 VA: 0x1E49C08
	public void FillNextLevelDataList(List<InfoBarData> infoBarDataList) { }

	// RVA: 0x1E49E40 Offset: 0x1E48E40 VA: 0x1E49E40
	public void OnSoftReset() { }

	// RVA: 0x1E49E54 Offset: 0x1E48E54 VA: 0x1E49E54
	public void FillWonderPowerList(List<InfoBarData> infoBarDataList) { }

	// RVA: 0x1E49E68 Offset: 0x1E48E68 VA: 0x1E49E68
	public string GetRecipeDisplayName(string recipeName) { }

	// RVA: 0x1E49EE8 Offset: 0x1E48EE8 VA: 0x1E49EE8
	public int GetTradeLevelRequirement(string recipeName) { }

	// RVA: 0x1E49EFC Offset: 0x1E48EFC VA: 0x1E49EFC
	public List<RecipeConfig> GetRecipes() { }

	// RVA: 0x1E49F10 Offset: 0x1E48F10 VA: 0x1E49F10
	public List<TagEnum> GetMercenaryTypes() { }

	// RVA: 0x1E49F24 Offset: 0x1E48F24 VA: 0x1E49F24
	public RecipeConfig GetRecipe(string name) { }

	// RVA: 0x1E49F38 Offset: 0x1E48F38 VA: 0x1E49F38
	public void GetAndUpdateCoalitionInputs(Action OnComplete) { }

	// RVA: 0x1E49F4C Offset: 0x1E48F4C VA: 0x1E49F4C
	public int GetQueuedMercenaryCount() { }

	// RVA: 0x1E49F60 Offset: 0x1E48F60 VA: 0x1E49F60
	public int GetMercenaryCap() { }

	// RVA: 0x1E49F84 Offset: 0x1E48F84 VA: 0x1E49F84
	public TagEnum GetRewardType() { }

	// RVA: 0x1E49FAC Offset: 0x1E48FAC VA: 0x1E49FAC
	public void CancelExplore() { }

	// RVA: 0x1E49FF0 Offset: 0x1E48FF0 VA: 0x1E49FF0
	public void FinishCollect() { }

	// RVA: 0x1E4A038 Offset: 0x1E49038 VA: 0x1E4A038
	public void StartCollect() { }

	// RVA: 0x1E4A07C Offset: 0x1E4907C VA: 0x1E4A07C
	public bool CanCollect() { }

	// RVA: 0x1E4A0B8 Offset: 0x1E490B8 VA: 0x1E4A0B8
	public int GetMaxUpgradeLevel() { }

	// RVA: 0x1E4A0D0 Offset: 0x1E490D0 VA: 0x1E4A0D0
	public int GetExplorations() { }

	// RVA: 0x1E4A0F8 Offset: 0x1E490F8 VA: 0x1E4A0F8
	public bool IsCollecting() { }

	// RVA: 0x1E4A10C Offset: 0x1E4910C VA: 0x1E4A10C
	public int GetRewardAmount() { }

	// RVA: 0x1E4A170 Offset: 0x1E49170 VA: 0x1E4A170
	public float GetCooldownTime() { }

	// RVA: 0x1E4A1B0 Offset: 0x1E491B0 VA: 0x1E4A1B0
	public double GetCooldownTimeLeft() { }

	// RVA: 0x1E4A1F0 Offset: 0x1E491F0 VA: 0x1E4A1F0
	public float GetCollectTime() { }

	// RVA: 0x1E4A230 Offset: 0x1E49230 VA: 0x1E4A230
	public List<TagEnum> GetResourceTypes() { }

	// RVA: 0x1E4A2B4 Offset: 0x1E492B4 VA: 0x1E4A2B4
	public bool HasResourceType(TagEnum type) { }

	// RVA: 0x1E4A2D0 Offset: 0x1E492D0 VA: 0x1E4A2D0
	public void AddResource(TagEnum type, int amount) { }

	// RVA: 0x1E4A2EC Offset: 0x1E492EC VA: 0x1E4A2EC
	public void SetResource(TagEnum type, int amount) { }

	// RVA: 0x1E4A308 Offset: 0x1E49308 VA: 0x1E4A308
	public void RemoveResource(TagEnum type, int amount) { }

	// RVA: 0x1E45F50 Offset: 0x1E44F50 VA: 0x1E45F50
	public int GetResourceAvailable(TagEnum type) { }

	// RVA: 0x1E4A324 Offset: 0x1E49324 VA: 0x1E4A324
	public bool IsResourceGenerator() { }

	// RVA: 0x1E4A334 Offset: 0x1E49334 VA: 0x1E4A334
	public bool IsResourceContainer() { }

	// RVA: 0x1E4A344 Offset: 0x1E49344 VA: 0x1E4A344
	public void InitLootableContainerFood(int amount) { }

	// RVA: 0x1E4A358 Offset: 0x1E49358 VA: 0x1E4A358
	public void InitLootableContainerGold(int amount) { }

	// RVA: 0x1E4A36C Offset: 0x1E4936C VA: 0x1E4A36C
	public void InitLootableContainerOil(int amount) { }

	// RVA: 0x1E4A380 Offset: 0x1E49380 VA: 0x1E4A380
	public void InitLootableGeneratorFood(int amount) { }

	// RVA: 0x1E4A394 Offset: 0x1E49394 VA: 0x1E4A394
	public void InitLootableGeneratorGold(int amount) { }

	// RVA: 0x1E4A3A8 Offset: 0x1E493A8 VA: 0x1E4A3A8
	public void InitLootableGeneratorOil(int amount) { }

	// RVA: 0x1E4A3BC Offset: 0x1E493BC VA: 0x1E4A3BC
	public void InitLootableTradeGoodContainer(TagEnum key) { }

	// RVA: 0x1E4A3D0 Offset: 0x1E493D0 VA: 0x1E4A3D0
	public int GetAvailableBattleResources(TagEnum type) { }

	// RVA: 0x1E4A41C Offset: 0x1E4941C VA: 0x1E4A41C
	public int GetFoodAvailableInGenerator() { }

	// RVA: 0x1E4A480 Offset: 0x1E49480 VA: 0x1E4A480
	public float GetGeneratorFoodCap() { }

	// RVA: 0x1E4A4F0 Offset: 0x1E494F0 VA: 0x1E4A4F0
	public int GetGoldAvailableInGenerator() { }

	// RVA: 0x1E4A554 Offset: 0x1E49554 VA: 0x1E4A554
	public int GetGeneratorGoldCap() { }

	// RVA: 0x1E4A5B8 Offset: 0x1E495B8 VA: 0x1E4A5B8
	public int GetOilAvailableInGenerator() { }

	// RVA: 0x1E4A61C Offset: 0x1E4961C VA: 0x1E4A61C
	public float GetGeneratorOilCap() { }

	// RVA: 0x1E4A68C Offset: 0x1E4968C VA: 0x1E4A68C
	public int GetFragmentAvailable() { }

	// RVA: 0x1E4A6F0 Offset: 0x1E496F0 VA: 0x1E4A6F0
	public int GetFragmentCap() { }

	// RVA: 0x1E4A754 Offset: 0x1E49754 VA: 0x1E4A754
	public double GetTimeUntilCap() { }

	// RVA: 0x1E4A83C Offset: 0x1E4983C VA: 0x1E4A83C
	public int GetGoldAvailableInContainer() { }

	// RVA: 0x1E4A860 Offset: 0x1E49860 VA: 0x1E4A860
	public int GetFoodAvailableInContainer() { }

	// RVA: 0x1E4A884 Offset: 0x1E49884 VA: 0x1E4A884
	public int GetOilAvailableInContainer() { }

	// RVA: 0x1E4A8A8 Offset: 0x1E498A8 VA: 0x1E4A8A8
	public int GetAvailableBattleRoadResources(TagEnum type) { }

	// RVA: 0x1E4A8BC Offset: 0x1E498BC VA: 0x1E4A8BC
	public StatsHelper2 GetMaxStats() { }

	// RVA: 0x1E4A8C4 Offset: 0x1E498C4 VA: 0x1E4A8C4
	public StatsHelper2 GetStats() { }

	// RVA: 0x1E4A840 Offset: 0x1E49840 VA: 0x1E4A840
	public int GetGoldAvailable() { }

	// RVA: 0x1E4A864 Offset: 0x1E49864 VA: 0x1E4A864
	public int GetFoodAvailable() { }

	// RVA: 0x1E4A888 Offset: 0x1E49888 VA: 0x1E4A888
	public int GetOilAvailable() { }

	// RVA: 0x1E4A8CC Offset: 0x1E498CC VA: 0x1E4A8CC
	public TagEnum GetHarvestResourceType() { }

	// RVA: 0x1E45F6C Offset: 0x1E44F6C VA: 0x1E45F6C
	public int GetMaxResource(TagEnum type) { }

	// RVA: 0x1E4A934 Offset: 0x1E49934 VA: 0x1E4A934
	public int GetPercentFull(TagEnum type) { }

	// RVA: 0x1E4A980 Offset: 0x1E49980 VA: 0x1E4A980
	public void InitMarker(TagEnum type, int amount, double duration, float range, SpellEffectConfig se, Vector3 location, AoeManager.PossibleTargets possibleTargets) { }

	// RVA: 0x1E4A994 Offset: 0x1E49994 VA: 0x1E4A994
	public void InitDecoyMarker(TagEnum type, int amount, double duration, float range, SpellEffectConfig se, Vector3 location, AoeManager.PossibleTargets possibleTargets, TagEnum particleEffect) { }

	// RVA: 0x1E4A9A8 Offset: 0x1E499A8 VA: 0x1E4A9A8
	public void Reset() { }

	// RVA: 0x1E4A9B0 Offset: 0x1E499B0 VA: 0x1E4A9B0
	public void Reset(bool resetDisplay) { }

	// RVA: 0x1E4B144 Offset: 0x1E4A144 VA: 0x1E4B144
	public void ResetLibraryResearch() { }

	// RVA: 0x1E4B158 Offset: 0x1E4A158 VA: 0x1E4B158
	public void ClearTarget() { }

	// RVA: 0x1E4B16C Offset: 0x1E4A16C VA: 0x1E4B16C
	public void SetTroopHome(EntityController home) { }

	// RVA: 0x1E4B180 Offset: 0x1E4A180 VA: 0x1E4B180
	public void PathTo(Vector3 location, Action onComplete) { }

	// RVA: 0x1E4B194 Offset: 0x1E4A194 VA: 0x1E4B194
	public void ClearTroopPathHome() { }

	// RVA: 0x1E4B1A8 Offset: 0x1E4A1A8 VA: 0x1E4B1A8
	public void UpdatePositionAndWorldGrid(Vector3 position) { }

	// RVA: 0x1E4B1C4 Offset: 0x1E4A1C4 VA: 0x1E4B1C4
	public void ForcePosition(Vector3 position) { }

	// RVA: 0x1E4B21C Offset: 0x1E4A21C VA: 0x1E4B21C
	public void ForceDirection(Vector3 direction) { }

	// RVA: 0x1E4B294 Offset: 0x1E4A294 VA: 0x1E4B294
	public bool UpdateActiveLayout() { }

	// RVA: 0x1E4B2D8 Offset: 0x1E4A2D8 VA: 0x1E4B2D8
	public bool IsValidMultiLayout() { }

	// RVA: 0x1E4B2F4 Offset: 0x1E4A2F4 VA: 0x1E4B2F4
	public void SyncLayouts() { }

	// RVA: 0x1E4B310 Offset: 0x1E4A310 VA: 0x1E4B310
	public void CopyLayout(int fromIndex, int toIndex, bool fromIsWorldWar, bool toIsWorldWar) { }

	// RVA: 0x1E4B41C Offset: 0x1E4A41C VA: 0x1E4B41C
	public bool IsNonCritical() { }

	// RVA: 0x1E4B4E4 Offset: 0x1E4A4E4 VA: 0x1E4B4E4
	public bool UsesWonderBonus() { }

	// RVA: 0x1E4B510 Offset: 0x1E4A510 VA: 0x1E4B510
	public bool UsesBlessingBonus() { }

	// RVA: 0x1E4B53C Offset: 0x1E4A53C VA: 0x1E4B53C
	public void ResetPosition() { }

	// RVA: 0x1E4B558 Offset: 0x1E4A558 VA: 0x1E4B558
	public void SetInvalidLayouts() { }

	// RVA: 0x1E4B4C4 Offset: 0x1E4A4C4 VA: 0x1E4B4C4
	public void InvalidateLayout(int layout, bool isToWorldWar = False) { }

	// RVA: 0x1E4B574 Offset: 0x1E4A574 VA: 0x1E4B574
	public bool IsPositionValid() { }

	// RVA: 0x1E4B590 Offset: 0x1E4A590 VA: 0x1E4B590
	public bool IsLayoutValid(int layout) { }

	// RVA: 0x1E4B5AC Offset: 0x1E4A5AC VA: 0x1E4B5AC
	public bool IsWarLayoutValid(int layout) { }

	// RVA: 0x1E4B5C8 Offset: 0x1E4A5C8 VA: 0x1E4B5C8
	public Vector3 GetDroneAOO() { }

	// RVA: 0x1E4B620 Offset: 0x1E4A620 VA: 0x1E4B620
	public void SetTarget(EntityController target) { }

	// RVA: 0x1E4B68C Offset: 0x1E4A68C VA: 0x1E4B68C
	public void RallyToTarget(EntityController target, Action OnTargetFoundDead) { }

	// RVA: 0x1E4B6A0 Offset: 0x1E4A6A0 VA: 0x1E4B6A0
	public void RerouteToTarget(EntityController target) { }

	// RVA: 0x1E4B6B4 Offset: 0x1E4A6B4 VA: 0x1E4B6B4
	public bool HasRallyTarget() { }

	// RVA: 0x1E4B6C8 Offset: 0x1E4A6C8 VA: 0x1E4B6C8
	public void SetTargetLocation(Vector3 target) { }

	// RVA: 0x1E4B6DC Offset: 0x1E4A6DC VA: 0x1E4B6DC
	public bool IsRallying() { }

	// RVA: 0x1E4B6F0 Offset: 0x1E4A6F0 VA: 0x1E4B6F0
	public bool IsAttacking(TagEnum tag) { }

	// RVA: 0x1E4B710 Offset: 0x1E4A710 VA: 0x1E4B710
	public TagEnum GetSummoningEntity() { }

	// RVA: 0x1E4B728 Offset: 0x1E4A728 VA: 0x1E4B728
	public void SetSummoningEntity(TagEnum entity) { }

	// RVA: 0x1E4B738 Offset: 0x1E4A738 VA: 0x1E4B738
	public bool HasSummoningEntity() { }

	// RVA: 0x1E4B74C Offset: 0x1E4A74C VA: 0x1E4B74C
	public void SetAttackTarget(EntityController target) { }

	// RVA: 0x1E4B760 Offset: 0x1E4A760 VA: 0x1E4B760
	public void ForceAttackTarget(EntityController target) { }

	// RVA: 0x1E4B774 Offset: 0x1E4A774 VA: 0x1E4B774
	public void RallyToLocation(Vector3 target) { }

	// RVA: 0x1E4B788 Offset: 0x1E4A788 VA: 0x1E4B788
	public void RerouteToLocation(Vector3 target) { }

	// RVA: 0x1E4B79C Offset: 0x1E4A79C VA: 0x1E4B79C
	public void ShootAtTarget(EntityController target) { }

	// RVA: 0x1E4B7B0 Offset: 0x1E4A7B0 VA: 0x1E4B7B0
	public void Knockback(Vector3 sourceLocation) { }

	// RVA: 0x1E4B7C4 Offset: 0x1E4A7C4 VA: 0x1E4B7C4
	public bool IsHealer() { }

	// RVA: 0x1E4B7E4 Offset: 0x1E4A7E4 VA: 0x1E4B7E4
	public void AttackType(TagEnum pref) { }

	// RVA: 0x1E4B7F8 Offset: 0x1E4A7F8 VA: 0x1E4B7F8
	public void FireProjectile() { }

	// RVA: 0x1E4BA74 Offset: 0x1E4AA74 VA: 0x1E4BA74
	public void ReloadMissile() { }

	// RVA: 0x1E4BA88 Offset: 0x1E4AA88 VA: 0x1E4BA88
	public void CreateGarrisonedDefender() { }

	// RVA: 0x1E4BA9C Offset: 0x1E4AA9C VA: 0x1E4BA9C
	public void FillGarrisonWithDefenders() { }

	// RVA: 0x1E4BAB0 Offset: 0x1E4AAB0 VA: 0x1E4BAB0
	public void RefreshGarrisonedDefenderArt() { }

	// RVA: 0x1E4BAC4 Offset: 0x1E4AAC4 VA: 0x1E4BAC4
	public bool IsCommandoRangedUnit(EntityController target) { }

	// RVA: 0x1E4BAD8 Offset: 0x1E4AAD8 VA: 0x1E4BAD8
	public bool IsRangedUnit() { }

	// RVA: 0x1E4BAEC Offset: 0x1E4AAEC VA: 0x1E4BAEC
	public bool IsMilitaryBuilding() { }

	// RVA: 0x1E4BB44 Offset: 0x1E4AB44 VA: 0x1E4BB44
	public bool IsDefensiveBuilding() { }

	// RVA: 0x1E4BB64 Offset: 0x1E4AB64 VA: 0x1E4BB64
	public void SetHideDisabledPick(bool active) { }

	// RVA: 0x1E4BB98 Offset: 0x1E4AB98 VA: 0x1E4BB98
	public bool ShouldHarvestOnTap() { }

	// RVA: 0x1E4BE28 Offset: 0x1E4AE28 VA: 0x1E4BE28
	public void InitRangeRadiusGraphic() { }

	// RVA: 0x1E4C10C Offset: 0x1E4B10C VA: 0x1E4C10C
	public void OnRemoveFromSimulation() { }

	// RVA: 0x1E4C2A4 Offset: 0x1E4B2A4 VA: 0x1E4C2A4
	public void OnInstantUpgrade() { }

	// RVA: 0x1E4C2B8 Offset: 0x1E4B2B8 VA: 0x1E4C2B8
	public bool IsSelectingNation() { }

	// RVA: 0x1E4C2CC Offset: 0x1E4B2CC VA: 0x1E4C2CC
	public bool CanHarvest() { }

	// RVA: 0x1E4C614 Offset: 0x1E4B614 VA: 0x1E4C614
	public bool HasWorldWarLoot() { }

	// RVA: 0x1E4C628 Offset: 0x1E4B628 VA: 0x1E4C628
	public bool HasRubies() { }

	// RVA: 0x1E4C63C Offset: 0x1E4B63C VA: 0x1E4C63C
	public bool IsMusueumUpgradeFTUEReady() { }

	// RVA: 0x1E4C650 Offset: 0x1E4B650 VA: 0x1E4C650
	public bool IsFTUEReady() { }

	// RVA: 0x1E4C664 Offset: 0x1E4B664 VA: 0x1E4C664
	public void NextFTUE() { }

	// RVA: 0x1E45C48 Offset: 0x1E44C48 VA: 0x1E45C48
	public bool ReadyFTUE(string ftue = "", GuitarPick.Icon icon = 9) { }

	// RVA: 0x1E4C678 Offset: 0x1E4B678 VA: 0x1E4C678
	public bool DoesLevelHaveFTUE(int level) { }

	// RVA: 0x1E4C6F0 Offset: 0x1E4B6F0 VA: 0x1E4C6F0
	public void DisableFTUE() { }

	// RVA: 0x1E4C724 Offset: 0x1E4B724 VA: 0x1E4C724
	public void CheckSunTzuFTUE() { }

	// RVA: 0x1E4C738 Offset: 0x1E4B738 VA: 0x1E4C738
	public void DoHarvest(bool single = False, bool showParticles = True) { }

	// RVA: 0x1E4CD8C Offset: 0x1E4BD8C VA: 0x1E4CD8C
	public void OnHarvested() { }

	// RVA: 0x1E4CDA0 Offset: 0x1E4BDA0 VA: 0x1E4CDA0
	public void CompleteResearch(bool isTech) { }

	// RVA: 0x1E4CE98 Offset: 0x1E4BE98 VA: 0x1E4CE98
	public void FinishMasteryResearch(MasterySkillConfig config, bool spedUp) { }

	// RVA: 0x1E4CEF0 Offset: 0x1E4BEF0 VA: 0x1E4CEF0
	public void InstantWonderReplace() { }

	// RVA: 0x1E4CF04 Offset: 0x1E4BF04 VA: 0x1E4CF04
	public void CancelResearch(bool firstSlot = True) { }

	// RVA: 0x1E4CF60 Offset: 0x1E4BF60 VA: 0x1E4CF60
	public void CancelResearchByMaster(int masterId) { }

	// RVA: 0x1E4CF74 Offset: 0x1E4BF74 VA: 0x1E4CF74
	public void Deposit(TagEnum resourceKey, int resourceAmount) { }

	// RVA: 0x1E4CF88 Offset: 0x1E4BF88 VA: 0x1E4CF88
	public void Withdraw() { }

	// RVA: 0x1E4CF9C Offset: 0x1E4BF9C VA: 0x1E4CF9C
	public TagEnum GetStoredKey() { }

	// RVA: 0x1E4CFB4 Offset: 0x1E4BFB4 VA: 0x1E4CFB4
	public bool CanDepositFoodGold() { }

	// RVA: 0x1E4CFC8 Offset: 0x1E4BFC8 VA: 0x1E4CFC8
	public bool CanDepositOil() { }

	// RVA: 0x1E4CFDC Offset: 0x1E4BFDC VA: 0x1E4CFDC
	public int GetStoredAmount(TagEnum resourceKey) { }

	// RVA: 0x1E4CFF0 Offset: 0x1E4BFF0 VA: 0x1E4CFF0
	public int GetFoodGoldCapacity() { }

	// RVA: 0x1E4D004 Offset: 0x1E4C004 VA: 0x1E4D004
	public int GetOilCapacity() { }

	// RVA: 0x1E4D018 Offset: 0x1E4C018 VA: 0x1E4D018
	public int GetPotentialOilCapacity() { }

	// RVA: 0x1E4D02C Offset: 0x1E4C02C VA: 0x1E4D02C
	public double GetDepositTime() { }

	// RVA: 0x1E4D044 Offset: 0x1E4C044 VA: 0x1E4D044
	public void SetInvulnerable(bool onoff, double duration) { }

	// RVA: 0x1E4D05C Offset: 0x1E4C05C VA: 0x1E4D05C
	public void StartProtection(double duration, float protection) { }

	// RVA: 0x1E4D070 Offset: 0x1E4C070 VA: 0x1E4D070
	public bool CanSalute() { }

	// RVA: 0x1E4D084 Offset: 0x1E4C084 VA: 0x1E4D084
	public void Salute(bool freeze = False) { }

	// RVA: 0x1E4D09C Offset: 0x1E4C09C VA: 0x1E4D09C
	public bool HasAuraDamageBuff() { }

	// RVA: 0x1E4D0BC Offset: 0x1E4C0BC VA: 0x1E4D0BC
	public float GetAuraDamageBuffAmount() { }

	// RVA: 0x1E4D0D4 Offset: 0x1E4C0D4 VA: 0x1E4D0D4
	public bool IsEnraged() { }

	// RVA: 0x1E4D0F4 Offset: 0x1E4C0F4 VA: 0x1E4D0F4
	public float EnragedDamageBonus() { }

	// RVA: 0x1E4D10C Offset: 0x1E4C10C VA: 0x1E4D10C
	public float EnragedAttackSpeedBonus() { }

	// RVA: 0x1E4D124 Offset: 0x1E4C124 VA: 0x1E4D124
	public bool IsBerserk() { }

	// RVA: 0x1E4D144 Offset: 0x1E4C144 VA: 0x1E4D144
	public float BerserkDamageBonus() { }

	// RVA: 0x1E4D15C Offset: 0x1E4C15C VA: 0x1E4D15C
	public float BerserkAttackSpeedBonus() { }

	// RVA: 0x1E4D174 Offset: 0x1E4C174 VA: 0x1E4D174
	public void SetBerserk(bool onoff, double duration, float damageBonus, float attackSpeedBonus, bool isRally, TagEnum buffParticle) { }

	// RVA: 0x1E4D190 Offset: 0x1E4C190 VA: 0x1E4D190
	public void SetWeakened(double duration, float weakAmount) { }

	// RVA: 0x1E4D208 Offset: 0x1E4C208 VA: 0x1E4D208
	public float GetWeakenedDamageTakenBonus() { }

	// RVA: 0x1E4D248 Offset: 0x1E4C248 VA: 0x1E4D248
	public bool IsWeakened() { }

	// RVA: 0x1E4D288 Offset: 0x1E4C288 VA: 0x1E4D288
	public bool HasHealthBarColorOverride() { }

	// RVA: 0x1E4D2C8 Offset: 0x1E4C2C8 VA: 0x1E4D2C8
	public Color GetHealthBarColorOverride() { }

	// RVA: 0x1E4D3A8 Offset: 0x1E4C3A8 VA: 0x1E4D3A8
	public void SetBlockingOnAttackCounting(bool onoff, double duration) { }

	// RVA: 0x1E4D3C0 Offset: 0x1E4C3C0 VA: 0x1E4D3C0
	public float TNEDamageBonus() { }

	// RVA: 0x1E4D3D8 Offset: 0x1E4C3D8 VA: 0x1E4D3D8
	public float TNEDamageReduction() { }

	// RVA: 0x1E4D3F0 Offset: 0x1E4C3F0 VA: 0x1E4D3F0
	public bool IsUndetectable() { }

	// RVA: 0x1E4D410 Offset: 0x1E4C410 VA: 0x1E4D410
	public void SetUndetectable(bool onoff) { }

	// RVA: 0x1E4D428 Offset: 0x1E4C428 VA: 0x1E4D428
	public void SetAuraDamageBuffed(bool onoff, double duration, float damageBonus) { }

	// RVA: 0x1E4D43C Offset: 0x1E4C43C VA: 0x1E4D43C
	public void SetAlerted(double duration, float attackSpeedBonus, float movementSpeedBonus) { }

	// RVA: 0x1E4D450 Offset: 0x1E4C450 VA: 0x1E4D450
	public bool IsAlerted() { }

	// RVA: 0x1E4D470 Offset: 0x1E4C470 VA: 0x1E4D470
	public float AlertedAttackSpeedBonus() { }

	// RVA: 0x1E4D488 Offset: 0x1E4C488 VA: 0x1E4D488
	public float AlertedMovementSpeedBonus() { }

	// RVA: 0x1E4D4A0 Offset: 0x1E4C4A0 VA: 0x1E4D4A0
	public bool IsSuppressed() { }

	// RVA: 0x1E4D4E8 Offset: 0x1E4C4E8 VA: 0x1E4D4E8
	public float GetSuppressionDamage() { }

	// RVA: 0x1E4D500 Offset: 0x1E4C500 VA: 0x1E4D500
	public float GetSuppressionSpeed() { }

	// RVA: 0x1E4D518 Offset: 0x1E4C518 VA: 0x1E4D518
	public void StartSuppression(double duration, float damage, float speed) { }

	// RVA: 0x1E4D52C Offset: 0x1E4C52C VA: 0x1E4D52C
	public void StopSuppression() { }

	// RVA: 0x1E4D540 Offset: 0x1E4C540 VA: 0x1E4D540
	public bool DoConvert(float convertReductionAttackSpeed, float convertReductionHealth, float convertBonusAttack) { }

	// RVA: 0x1E4D554 Offset: 0x1E4C554 VA: 0x1E4D554
	public void SwitchToDefense() { }

	// RVA: 0x1E4D570 Offset: 0x1E4C570 VA: 0x1E4D570
	public void InitFlatHealthBonus() { }

	// RVA: 0x1E4D584 Offset: 0x1E4C584 VA: 0x1E4D584
	public bool IsQuickMarch() { }

	// RVA: 0x1E4D5A4 Offset: 0x1E4C5A4 VA: 0x1E4D5A4
	public void StartQuickMarch(double duration, float speedBonus, bool isRally, bool showEffect = True) { }

	// RVA: 0x1E4D5C0 Offset: 0x1E4C5C0 VA: 0x1E4D5C0
	public void StopQuickMarch() { }

	// RVA: 0x1E4D5D4 Offset: 0x1E4C5D4 VA: 0x1E4D5D4
	public float GetQuickMarchBonus() { }

	// RVA: 0x1E4D5EC Offset: 0x1E4C5EC VA: 0x1E4D5EC
	public float ConvertReduction() { }

	// RVA: 0x1E4D604 Offset: 0x1E4C604 VA: 0x1E4D604
	public float ConvertAttackBonus() { }

	// RVA: 0x1E4D61C Offset: 0x1E4C61C VA: 0x1E4D61C
	public bool IsLockdown() { }

	// RVA: 0x1E4D694 Offset: 0x1E4C694 VA: 0x1E4D694
	public bool CanBeLockDowned(bool ignoreDirective = False) { }

	// RVA: 0x1E4D738 Offset: 0x1E4C738 VA: 0x1E4D738
	public void SetLockdown(bool onoff, bool bIsBlitzkrieg, double duration) { }

	[IteratorStateMachine(typeof(EntityController.<EndLockdownParticleCoroutine>d__717))]
	// RVA: 0x1E4D8A4 Offset: 0x1E4C8A4 VA: 0x1E4D8A4
	private IEnumerator EndLockdownParticleCoroutine() { }

	// RVA: 0x1E4D90C Offset: 0x1E4C90C VA: 0x1E4D90C
	public void SetSuppressed(double duration, int amount) { }

	// RVA: 0x1E4D988 Offset: 0x1E4C988 VA: 0x1E4D988
	public void ClearDonatedTroops() { }

	// RVA: 0x1E4D99C Offset: 0x1E4C99C VA: 0x1E4D99C
	public bool HasDonatedTroops() { }

	// RVA: 0x1E4D9C4 Offset: 0x1E4C9C4 VA: 0x1E4D9C4
	public bool HasDeployedAllMercenaries() { }

	// RVA: 0x1E4D9D8 Offset: 0x1E4C9D8 VA: 0x1E4D9D8
	public void SetTroopMaster(EntityController master) { }

	// RVA: 0x1E4D9E8 Offset: 0x1E4C9E8 VA: 0x1E4D9E8
	public void SetIsMinionInReplayFlag(bool b) { }

	// RVA: 0x1E4D9FC Offset: 0x1E4C9FC VA: 0x1E4D9FC
	public bool IsMinionInReplay() { }

	// RVA: 0x1E4DA1C Offset: 0x1E4CA1C VA: 0x1E4DA1C
	public bool HasTroopMaster() { }

	// RVA: 0x1E4DA30 Offset: 0x1E4CA30 VA: 0x1E4DA30
	public EntityController GetTroopMaster() { }

	// RVA: 0x1E4DA48 Offset: 0x1E4CA48 VA: 0x1E4DA48
	public void OnMinionDeath(EntityController ec) { }

	// RVA: 0x1E4DA5C Offset: 0x1E4CA5C VA: 0x1E4DA5C
	public void OnMinionSpawn(EntityController ec) { }

	// RVA: 0x1E4DA70 Offset: 0x1E4CA70 VA: 0x1E4DA70
	public void DirectMinionsToAttack(EntityController ec) { }

	// RVA: 0x1E4DA84 Offset: 0x1E4CA84 VA: 0x1E4DA84
	public List<TroopModel> GetDonatedTroops(bool ignoreUseDonations) { }

	// RVA: 0x1E4DB54 Offset: 0x1E4CB54 VA: 0x1E4DB54
	public void OnDonatedTroopDeath(EntityController ec) { }

	// RVA: 0x1E4DB68 Offset: 0x1E4CB68 VA: 0x1E4DB68
	public void AddLoot(TagEnum key, int amount) { }

	// RVA: 0x1E4DB7C Offset: 0x1E4CB7C VA: 0x1E4DB7C
	public bool IsBeingAttackedByTheirTarget() { }

	// RVA: 0x1E4DC6C Offset: 0x1E4CC6C VA: 0x1E4DC6C
	public bool CanBeTaunted() { }

	// RVA: 0x1E4DC94 Offset: 0x1E4CC94 VA: 0x1E4DC94
	public void Taunt(EntityController taunter) { }

	// RVA: 0x1E4DCE0 Offset: 0x1E4CCE0 VA: 0x1E4DCE0
	public PlayerID GetOwnerPlayerId() { }

	// RVA: 0x1E4DCFC Offset: 0x1E4CCFC VA: 0x1E4DCFC
	public void AddDonatedTroop(TroopModel model) { }

	// RVA: 0x1E4DD10 Offset: 0x1E4CD10 VA: 0x1E4DD10
	public bool CanBeAttacked(EntityController attacker) { }

	// RVA: 0x1E4DE20 Offset: 0x1E4CE20 VA: 0x1E4DE20
	public bool CanBeNuked() { }

	// RVA: 0x1E4DE5C Offset: 0x1E4CE5C VA: 0x1E4DE5C
	public void RemoveAoe() { }

	// RVA: 0x1E4DE90 Offset: 0x1E4CE90 VA: 0x1E4DE90
	public float GetMinRange() { }

	// RVA: 0x1E4DEA8 Offset: 0x1E4CEA8 VA: 0x1E4DEA8
	public float GetWonderBoostRange() { }

	// RVA: 0x1E4DEC0 Offset: 0x1E4CEC0 VA: 0x1E4DEC0
	public float GetRange() { }

	// RVA: 0x1E43CCC Offset: 0x1E42CCC VA: 0x1E43CCC
	public float GetVisualRadius() { }

	// RVA: 0x1E4E048 Offset: 0x1E4D048 VA: 0x1E4E048
	public void SetActiveGeneral(TagEnum general) { }

	// RVA: 0x1E4E094 Offset: 0x1E4D094 VA: 0x1E4E094
	public void DeactivateGeneral() { }

	// RVA: 0x1E4E0C8 Offset: 0x1E4D0C8 VA: 0x1E4E0C8
	public bool IsGeneralOnCooldown(TagEnum general) { }

	// RVA: 0x1E4E0DC Offset: 0x1E4D0DC VA: 0x1E4E0DC
	public bool IsGeneralOnExpedition(TagEnum general) { }

	// RVA: 0x1E4E0F0 Offset: 0x1E4D0F0 VA: 0x1E4E0F0
	public bool IsGeneralOnActiveExpedition(TagEnum general) { }

	// RVA: 0x1E4E104 Offset: 0x1E4D104 VA: 0x1E4E104
	public bool IsGeneralOnLoadingExpedition(TagEnum general) { }

	// RVA: 0x1E4E118 Offset: 0x1E4D118 VA: 0x1E4E118
	public List<TagEnum> GetActiveGenerals() { }

	// RVA: 0x1E4E1A4 Offset: 0x1E4D1A4 VA: 0x1E4E1A4
	public List<TagEnum> GetGeneralsInCastle() { }

	// RVA: 0x1E4E240 Offset: 0x1E4D240 VA: 0x1E4E240
	public List<TagEnum> GetWorldWarGenerals() { }

	// RVA: 0x1E4E2BC Offset: 0x1E4D2BC VA: 0x1E4E2BC
	public void SetActiveWorldWarGeneral(TagEnum general) { }

	// RVA: 0x1E4E308 Offset: 0x1E4D308 VA: 0x1E4E308
	public void RemoveActiveWorldWarGeneral(TagEnum general) { }

	// RVA: 0x1E4E354 Offset: 0x1E4D354 VA: 0x1E4E354
	public double GetGeneralCooldownStartTime(TagEnum general) { }

	// RVA: 0x1E4E36C Offset: 0x1E4D36C VA: 0x1E4E36C
	public double GetGeneralMaxCooldown(TagEnum general) { }

	// RVA: 0x1E4E384 Offset: 0x1E4D384 VA: 0x1E4E384
	public double GetGeneralCooldownRemainingTime(TagEnum general) { }

	// RVA: 0x1E4E39C Offset: 0x1E4D39C VA: 0x1E4E39C
	public void StartGeneralCooldown(TagEnum general, int percent) { }

	// RVA: 0x1E4E3B0 Offset: 0x1E4D3B0 VA: 0x1E4E3B0
	public void FinishGeneralCooldown(TagEnum general) { }

	// RVA: 0x1E4E3C4 Offset: 0x1E4D3C4 VA: 0x1E4E3C4
	public void DeactivateAndRecooldownGeneral(TagEnum general, int percent, int detachmentI) { }

	// RVA: 0x1E4E41C Offset: 0x1E4D41C VA: 0x1E4E41C
	public bool ShouldGeneralDefend() { }

	// RVA: 0x1E4E43C Offset: 0x1E4D43C VA: 0x1E4E43C
	public void SetGeneralDefend(bool shouldDefend) { }

	// RVA: 0x1E4E454 Offset: 0x1E4D454 VA: 0x1E4E454
	public BoostHelper GetBoostHelper() { }

	// RVA: 0x1E4E45C Offset: 0x1E4D45C VA: 0x1E4E45C
	public int GetBoostAmount(TagEnum key, TagEnum effect = 0) { }

	// RVA: 0x1E4E498 Offset: 0x1E4D498 VA: 0x1E4E498
	public int GetLotusTempleBoostAmount(TagEnum resourceKey) { }

	// RVA: 0x1E4E4AC Offset: 0x1E4D4AC VA: 0x1E4E4AC
	public int GetPowerPlantBaseEffect(TagEnum key, TagEnum effect = 0) { }

	// RVA: 0x1E4E4C8 Offset: 0x1E4D4C8 VA: 0x1E4E4C8
	public int GetPowerPlantBoostedEffect(TagEnum key, TagEnum effect = 0) { }

	// RVA: 0x1E4E4E4 Offset: 0x1E4D4E4 VA: 0x1E4E4E4
	public bool IsBoosted() { }

	// RVA: 0x1E4E510 Offset: 0x1E4D510 VA: 0x1E4E510
	public bool IsABoost() { }

	// RVA: 0x1E4E520 Offset: 0x1E4D520 VA: 0x1E4E520
	public bool IsAnEventPassBoost() { }

	// RVA: 0x1E4E530 Offset: 0x1E4D530 VA: 0x1E4E530
	public bool HasEventBoost(TagEnum boost) { }

	// RVA: 0x1E4E544 Offset: 0x1E4D544 VA: 0x1E4E544
	public double GetBoostTimeRemaining() { }

	// RVA: 0x1E4E570 Offset: 0x1E4D570 VA: 0x1E4E570
	public bool HasDefenseUsesRemaining() { }

	// RVA: 0x1E4E584 Offset: 0x1E4D584 VA: 0x1E4E584
	public void UpdateDefenseUses() { }

	// RVA: 0x1E4E5B8 Offset: 0x1E4D5B8 VA: 0x1E4E5B8
	public void UpdateAttackUses() { }

	// RVA: 0x1E4921C Offset: 0x1E4821C VA: 0x1E4921C
	public bool IsEventBuilding() { }

	// RVA: 0x1E4E5CC Offset: 0x1E4D5CC VA: 0x1E4E5CC
	public bool ShouldGetDurationFromEventBuilding() { }

	// RVA: 0x1E379E4 Offset: 0x1E369E4 VA: 0x1E379E4
	public bool IsEventTroop() { }

	// RVA: 0x1E4E640 Offset: 0x1E4D640 VA: 0x1E4E640
	public bool IsTravelingMerchant() { }

	// RVA: 0x1E4E650 Offset: 0x1E4D650 VA: 0x1E4E650
	public int GetEventExtraDonationCap() { }

	// RVA: 0x1E4E664 Offset: 0x1E4D664 VA: 0x1E4E664
	public int GetHarvestCapUnBoosted() { }

	// RVA: 0x1E4E6B4 Offset: 0x1E4D6B4 VA: 0x1E4E6B4
	public float GetHarvestTimeUnBoosted() { }

	// RVA: 0x1E4E708 Offset: 0x1E4D708 VA: 0x1E4E708
	public int GetHarvestAmount() { }

	// RVA: 0x1E4E758 Offset: 0x1E4D758 VA: 0x1E4E758
	public float GetHarvestTime() { }

	// RVA: 0x1E4E7AC Offset: 0x1E4D7AC VA: 0x1E4E7AC
	public double GetDuration() { }

	// RVA: 0x1E4E7D8 Offset: 0x1E4D7D8 VA: 0x1E4E7D8
	public double GetEventBuildingDuration() { }

	// RVA: 0x1E4E7F0 Offset: 0x1E4D7F0 VA: 0x1E4E7F0
	public double GetOriginalBoostDuration() { }

	// RVA: 0x1E4E808 Offset: 0x1E4D808 VA: 0x1E4E808
	public void SetDuration(double durationMinutes, bool isGlobalExpiration = False) { }

	// RVA: 0x1E4E86C Offset: 0x1E4D86C VA: 0x1E4E86C
	public void AddDuration(double duration) { }

	// RVA: 0x1E4E880 Offset: 0x1E4D880 VA: 0x1E4E880
	public void SetEventStats(int uses, double duration) { }

	// RVA: 0x1E4E894 Offset: 0x1E4D894 VA: 0x1E4E894
	public void AddEventStats(int uses, double duration) { }

	// RVA: 0x1E4E8A8 Offset: 0x1E4D8A8 VA: 0x1E4E8A8
	public bool IsDamaged() { }

	// RVA: 0x1E4EC20 Offset: 0x1E4DC20 VA: 0x1E4EC20
	public bool IsDead() { }

	// RVA: 0x1E4EE24 Offset: 0x1E4DE24 VA: 0x1E4EE24
	public bool IsStealth() { }

	// RVA: 0x1E4EE38 Offset: 0x1E4DE38 VA: 0x1E4EE38
	public bool HasTargetImmunity() { }

	// RVA: 0x1E4EE4C Offset: 0x1E4DE4C VA: 0x1E4EE4C
	public bool KillTroop() { }

	// RVA: 0x1E4EF00 Offset: 0x1E4DF00 VA: 0x1E4EF00
	public bool DoesTrapIgnoreReveal() { }

	// RVA: 0x1E4EF14 Offset: 0x1E4DF14 VA: 0x1E4EF14
	public bool IsTrapSpent() { }

	// RVA: 0x1E4EF28 Offset: 0x1E4DF28 VA: 0x1E4EF28
	public bool IsTrapFired() { }

	// RVA: 0x1E4EF3C Offset: 0x1E4DF3C VA: 0x1E4EF3C
	public bool IsTrapDisarmed() { }

	// RVA: 0x1E4EF5C Offset: 0x1E4DF5C VA: 0x1E4EF5C
	public void DisarmTrap() { }

	// RVA: 0x1E4EF70 Offset: 0x1E4DF70 VA: 0x1E4EF70
	public void SleepTrap(bool onOff) { }

	// RVA: 0x1E4EF84 Offset: 0x1E4DF84 VA: 0x1E4EF84
	public void GiveHighlight(string glowType) { }

	// RVA: 0x1E4EF98 Offset: 0x1E4DF98 VA: 0x1E4EF98
	public void HideHighlight() { }

	// RVA: 0x1E4EFAC Offset: 0x1E4DFAC VA: 0x1E4EFAC
	public bool HasHighlight() { }

	// RVA: 0x1E4EFC0 Offset: 0x1E4DFC0 VA: 0x1E4EFC0
	public void GiveDisarmHighlight(string glowType) { }

	// RVA: 0x1E4EFD4 Offset: 0x1E4DFD4 VA: 0x1E4EFD4
	public bool HasDisarmHighlight() { }

	// RVA: 0x1E4EFE8 Offset: 0x1E4DFE8 VA: 0x1E4EFE8
	public int GetSlow() { }

	// RVA: 0x1E4F024 Offset: 0x1E4E024 VA: 0x1E4F024
	public int GetWallLevel() { }

	// RVA: 0x1E4F03C Offset: 0x1E4E03C VA: 0x1E4F03C
	public int GetDamageReduction() { }

	// RVA: 0x1E4F050 Offset: 0x1E4E050 VA: 0x1E4F050
	public bool HasTroopDamageReduction() { }

	// RVA: 0x1E4F060 Offset: 0x1E4E060 VA: 0x1E4F060
	public float GetPercentReduction(TagEnum troop) { }

	// RVA: 0x1E4F078 Offset: 0x1E4E078 VA: 0x1E4F078
	public FabricatorEntityBuffs GetFabricatorBuffs() { }

	// RVA: 0x1E4F0F8 Offset: 0x1E4E0F8 VA: 0x1E4F0F8
	public void OnDeath() { }

	// RVA: 0x1E4F278 Offset: 0x1E4E278 VA: 0x1E4F278
	public void NotifyDecoyKilled() { }

	// RVA: 0x1E4F294 Offset: 0x1E4E294 VA: 0x1E4F294
	public bool ShouldSlow() { }

	// RVA: 0x1E4F2D4 Offset: 0x1E4E2D4 VA: 0x1E4F2D4
	public bool ShouldDamageReduction() { }

	// RVA: 0x1E4F2F4 Offset: 0x1E4E2F4 VA: 0x1E4F2F4
	public int GetRearmCost() { }

	// RVA: 0x1E4F308 Offset: 0x1E4E308 VA: 0x1E4F308
	public TagEnum GetRearmKey() { }

	// RVA: 0x1E4F368 Offset: 0x1E4E368 VA: 0x1E4F368
	public void RearmTrap() { }

	// RVA: 0x1E4F37C Offset: 0x1E4E37C VA: 0x1E4F37C
	public void FreeRearmTrap() { }

	// RVA: 0x1E4F390 Offset: 0x1E4E390 VA: 0x1E4F390
	public int GetNumberBattlesSinceTriggered() { }

	// RVA: 0x1E4F3A8 Offset: 0x1E4E3A8 VA: 0x1E4F3A8
	public void ResetRearmCost() { }

	// RVA: 0x1E4F3BC Offset: 0x1E4E3BC VA: 0x1E4F3BC
	public bool IsFleeing() { }

	// RVA: 0x1E4F3D0 Offset: 0x1E4E3D0 VA: 0x1E4F3D0
	public void SwitchTarget(EntityController newTarget) { }

	// RVA: 0x1E4DC58 Offset: 0x1E4CC58 VA: 0x1E4DC58
	public EntityController GetCurrentTarget() { }

	// RVA: 0x1E4F3E4 Offset: 0x1E4E3E4 VA: 0x1E4F3E4
	public int GetInitialBattleResourcesAvailable(TagEnum type) { }

	// RVA: 0x1E4F420 Offset: 0x1E4E420 VA: 0x1E4F420
	public int GetInitialBattleRoadResourcesAvailable(TagEnum type) { }

	// RVA: 0x1E4F434 Offset: 0x1E4E434 VA: 0x1E4F434
	public int GetFillPercent() { }

	// RVA: 0x1E4F448 Offset: 0x1E4E448 VA: 0x1E4F448
	public float ApplyAttackDamage(float delta, PlayerModel playerDoingDamage) { }

	// RVA: 0x1E4FE98 Offset: 0x1E4EE98 VA: 0x1E4FE98
	private void StealResource(TagEnum type, float delta, PlayerModel playerStealingResources) { }

	// RVA: 0x1E5007C Offset: 0x1E4F07C VA: 0x1E5007C
	private void StealRoadResource(TagEnum type, float delta, PlayerModel playerDoingStealing) { }

	// RVA: 0x1E50384 Offset: 0x1E4F384 VA: 0x1E50384
	private void PlayResourceStolen(TagEnum type, float delta) { }

	// RVA: 0x1E50218 Offset: 0x1E4F218 VA: 0x1E50218
	private void StealTradeGood(TagEnum type, PlayerModel playerStealingResources) { }

	// RVA: 0x1E503DC Offset: 0x1E4F3DC VA: 0x1E503DC
	public void UpdateLoot(TagEnum type, float delta) { }

	// RVA: 0x1E50408 Offset: 0x1E4F408 VA: 0x1E50408
	public void UpdateAmmo(float percentage) { }

	// RVA: 0x1E5020C Offset: 0x1E4F20C VA: 0x1E5020C
	public void UpdateHealth(float delta) { }

	// RVA: 0x1E5041C Offset: 0x1E4F41C VA: 0x1E5041C
	public void UpdateHealth(float delta, bool fireEffects, bool hideHealthBar = False) { }

	// RVA: 0x1E42120 Offset: 0x1E41120 VA: 0x1E42120
	public void FillHealth() { }

	// RVA: 0x1E4EA4C Offset: 0x1E4DA4C VA: 0x1E4EA4C
	public float GetHealth() { }

	// RVA: 0x1E505AC Offset: 0x1E4F5AC VA: 0x1E505AC
	public void AttackLimiterStartAttack(EntityController target) { }

	// RVA: 0x1E506C4 Offset: 0x1E4F6C4 VA: 0x1E506C4
	public bool AttackLimiterShouldAttack(EntityController target) { }

	// RVA: 0x1E50768 Offset: 0x1E4F768 VA: 0x1E50768
	public void AttackLimiterReset() { }

	// RVA: 0x1E5080C Offset: 0x1E4F80C VA: 0x1E5080C
	public void AttackLimiterEndAttack() { }

	// RVA: 0x1E4EAC4 Offset: 0x1E4DAC4 VA: 0x1E4EAC4
	public float GetMaxHealth() { }

	// RVA: 0x1E508B8 Offset: 0x1E4F8B8 VA: 0x1E508B8
	public int GetUnboostedMaxHealth() { }

	// RVA: 0x1E508CC Offset: 0x1E4F8CC VA: 0x1E508CC
	public void SetStartingDamagePercent(float percent) { }

	// RVA: 0x1E501D0 Offset: 0x1E4F1D0 VA: 0x1E501D0
	public float GetClampedHealth() { }

	// RVA: 0x1E508E0 Offset: 0x1E4F8E0 VA: 0x1E508E0
	public void SetDragger(Vector3 anchor) { }

	// RVA: 0x1E508F4 Offset: 0x1E4F8F4 VA: 0x1E508F4
	public string CanMakeDraggablePurchase(ConfirmationOverlay overlay) { }

	// RVA: 0x1E50908 Offset: 0x1E4F908 VA: 0x1E50908
	public bool IsDragging() { }

	// RVA: 0x1E50928 Offset: 0x1E4F928 VA: 0x1E50928
	public Vector3 GetNextAnchorPositionForDraggable() { }

	// RVA: 0x1E50990 Offset: 0x1E4F990 VA: 0x1E50990
	public void SetSelectedInfoWin(SelectedInfoWinController controller) { }

	// RVA: 0x1E509A4 Offset: 0x1E4F9A4 VA: 0x1E509A4
	public HashSet<Vector3> GetPreviouslyPurchasedThisUpdate() { }

	// RVA: 0x1E50A28 Offset: 0x1E4FA28 VA: 0x1E50A28
	public float GetRoadBuffModePlaybackPosition() { }

	// RVA: 0x1E50A40 Offset: 0x1E4FA40 VA: 0x1E50A40
	public void StartRoadBuffMode(float time) { }

	// RVA: 0x1E50AB0 Offset: 0x1E4FAB0 VA: 0x1E50AB0
	public void EndRoadBuffMode() { }

	// RVA: 0x1E50B08 Offset: 0x1E4FB08 VA: 0x1E50B08
	public bool IsPurchaseMode() { }

	// RVA: 0x1E50B28 Offset: 0x1E4FB28 VA: 0x1E50B28
	public void StartPurchaseMode() { }

	// RVA: 0x1E50B7C Offset: 0x1E4FB7C VA: 0x1E50B7C
	public void EndPurchaseMode() { }

	// RVA: 0x1E50B90 Offset: 0x1E4FB90 VA: 0x1E50B90
	public void CancelPurchase() { }

	// RVA: 0x1E50BC4 Offset: 0x1E4FBC4 VA: 0x1E50BC4
	public void SetPurchaser(ItemPurchaser purchaser) { }

	// RVA: 0x1E50BD8 Offset: 0x1E4FBD8 VA: 0x1E50BD8
	public void RemovePurchaser() { }

	// RVA: 0x1E50BEC Offset: 0x1E4FBEC VA: 0x1E50BEC
	public void SetConfirmationAction(Action action) { }

	// RVA: 0x1E50C00 Offset: 0x1E4FC00 VA: 0x1E50C00
	public void SetCancelAction(Action action) { }

	// RVA: 0x1E50C10 Offset: 0x1E4FC10 VA: 0x1E50C10
	public void GenerateLeagueRewards(List<KeyValuePair<string, object>> rewards) { }

	// RVA: 0x1E50C24 Offset: 0x1E4FC24 VA: 0x1E50C24
	public bool CanBeInExpandedSelection() { }

	// RVA: 0x1E50C44 Offset: 0x1E4FC44 VA: 0x1E50C44
	public bool IsSelectionExpanded() { }

	// RVA: 0x1E50C64 Offset: 0x1E4FC64 VA: 0x1E50C64
	public bool IsInExpandedSelection(EntityController entity) { }

	// RVA: 0x1E50C78 Offset: 0x1E4FC78 VA: 0x1E50C78
	public int GetLowestLevelInSelection() { }

	// RVA: 0x1E50C8C Offset: 0x1E4FC8C VA: 0x1E50C8C
	public List<EntityController> GetUpgradeableEntitiesInSelection(bool useAlternateResource) { }

	// RVA: 0x1E50CA4 Offset: 0x1E4FCA4 VA: 0x1E50CA4
	public List<EntityController> GetEntitiesInSelection() { }

	// RVA: 0x1E50CB8 Offset: 0x1E4FCB8 VA: 0x1E50CB8
	public int GetUpgradeSelectionAmount(bool useAlternateResource = False) { }

	// RVA: 0x1E50CD0 Offset: 0x1E4FCD0 VA: 0x1E50CD0
	public Dictionary<Consumable, int> GetUpgradeSelectionManualCost(bool useAlternateResource) { }

	// RVA: 0x1E50CE8 Offset: 0x1E4FCE8 VA: 0x1E50CE8
	public bool CanUpgradeSelection(bool useAlternateResource = False) { }

	// RVA: 0x1E50D00 Offset: 0x1E4FD00 VA: 0x1E50D00
	public void MoveSelection(Vector3 start, Vector3 end) { }

	// RVA: 0x1E50D14 Offset: 0x1E4FD14 VA: 0x1E50D14
	public void MoveSelectionBack() { }

	// RVA: 0x1E50D48 Offset: 0x1E4FD48 VA: 0x1E50D48
	public bool CanPlaceSelection() { }

	// RVA: 0x1E50D5C Offset: 0x1E4FD5C VA: 0x1E50D5C
	public void UpdateSelectedWorldLocations(Vector3 size) { }

	// RVA: 0x1E50D70 Offset: 0x1E4FD70 VA: 0x1E50D70
	public void Rotate() { }

	// RVA: 0x1E50D84 Offset: 0x1E4FD84 VA: 0x1E50D84
	public string GetDisplayNamePlural() { }

	// RVA: 0x1E499E8 Offset: 0x1E489E8 VA: 0x1E499E8
	public string GetDisplayName() { }

	// RVA: 0x1E50DA0 Offset: 0x1E4FDA0 VA: 0x1E50DA0
	public string GetDescription() { }

	// RVA: 0x1E50DC0 Offset: 0x1E4FDC0 VA: 0x1E50DC0
	public float GetBuildTime() { }

	// RVA: 0x1E50DDC Offset: 0x1E4FDDC VA: 0x1E50DDC
	public int GetResourceAmount() { }

	// RVA: 0x1E50DF8 Offset: 0x1E4FDF8 VA: 0x1E50DF8
	public TagEnum GetResourceKey() { }

	// RVA: 0x1E50E14 Offset: 0x1E4FE14 VA: 0x1E50E14
	public TagEnum GetCostType() { }

	// RVA: 0x1E50E30 Offset: 0x1E4FE30 VA: 0x1E50E30
	public int GetCostAmount() { }

	// RVA: 0x1E513B4 Offset: 0x1E503B4 VA: 0x1E513B4
	public TagEnum GetBuildingType() { }

	// RVA: 0x1E513C8 Offset: 0x1E503C8 VA: 0x1E513C8
	public bool IsDraggablePurchase() { }

	// RVA: 0x1E513E8 Offset: 0x1E503E8 VA: 0x1E513E8
	public void StartInventoryClaim() { }

	// RVA: 0x1E513FC Offset: 0x1E503FC VA: 0x1E513FC
	public void FinishInventoryClaim() { }

	// RVA: 0x1E51410 Offset: 0x1E50410 VA: 0x1E51410
	public int GetNumRequiredCitizens() { }

	// RVA: 0x1E5142C Offset: 0x1E5042C VA: 0x1E5142C
	public TagEnum GetRampingCostType() { }

	// RVA: 0x1E51440 Offset: 0x1E50440 VA: 0x1E51440
	public int GetRampingCostAmount() { }

	// RVA: 0x1E51454 Offset: 0x1E50454 VA: 0x1E51454
	public bool CanClear() { }

	// RVA: 0x1E51468 Offset: 0x1E50468 VA: 0x1E51468
	public void StartInstantClear() { }

	// RVA: 0x1E5147C Offset: 0x1E5047C VA: 0x1E5147C
	public void StartClear() { }

	// RVA: 0x1E37A34 Offset: 0x1E36A34 VA: 0x1E37A34
	public int GetAge() { }

	// RVA: 0x1E51494 Offset: 0x1E50494 VA: 0x1E51494
	public void SetBuyRemaining(int amount) { }

	// RVA: 0x1E514A8 Offset: 0x1E504A8 VA: 0x1E514A8
	public void OnCompletePurchase(bool fromStash = False, int numPreviouslyAdded = 0, bool savePlayerState = True, int numCoalesced = 0, bool specialDeal = False, bool doRebate = True) { }

	// RVA: 0x1E39AE0 Offset: 0x1E38AE0 VA: 0x1E39AE0
	public bool CanBeSelected() { }

	// RVA: 0x1E52C2C Offset: 0x1E51C2C VA: 0x1E52C2C
	public void CancelConversation() { }

	// RVA: 0x1E52C40 Offset: 0x1E51C40 VA: 0x1E52C40
	public void InConversation(bool yesNo) { }

	// RVA: 0x1E52C54 Offset: 0x1E51C54 VA: 0x1E52C54
	public bool IsInConversation() { }

	// RVA: 0x1E52C74 Offset: 0x1E51C74 VA: 0x1E52C74
	public void StartCitizenJob(CitizenJob citizenJob) { }

	// RVA: 0x1E52C88 Offset: 0x1E51C88 VA: 0x1E52C88
	public void StopCitizenJob(EntityController target) { }

	// RVA: 0x1E52C9C Offset: 0x1E51C9C VA: 0x1E52C9C
	public void ContinueCitizenJob(CitizenJob job) { }

	// RVA: 0x1E52CB0 Offset: 0x1E51CB0 VA: 0x1E52CB0
	public void SetCitizenHome(EntityController home) { }

	// RVA: 0x1E52CC0 Offset: 0x1E51CC0 VA: 0x1E52CC0
	public EntityController GetCitizenHome() { }

	// RVA: 0x1E52CD8 Offset: 0x1E51CD8 VA: 0x1E52CD8
	public void RemoveCitizen(EntityController citizen) { }

	// RVA: 0x1E52CEC Offset: 0x1E51CEC VA: 0x1E52CEC
	public void AddCitizen(EntityController citizen) { }

	// RVA: 0x1E52D00 Offset: 0x1E51D00 VA: 0x1E52D00
	public void AcceptCitizenTransfer(List<EntityController> citizens) { }

	// RVA: 0x1E52D1C Offset: 0x1E51D1C VA: 0x1E52D1C
	public void TransferCitizens(EntityController newHome) { }

	// RVA: 0x1E52D5C Offset: 0x1E51D5C VA: 0x1E52D5C
	public void WaitWhileJobTargetMoving() { }

	// RVA: 0x1E52D70 Offset: 0x1E51D70 VA: 0x1E52D70
	public void RushToMovedJobTarget() { }

	// RVA: 0x1E52D84 Offset: 0x1E51D84 VA: 0x1E52D84
	public void ReplaceWith(TagEnum replacementName) { }

	// RVA: 0x1E52FB8 Offset: 0x1E51FB8 VA: 0x1E52FB8
	public void CheckTownHallConnections(bool showMessages) { }

	// RVA: 0x1E52FD4 Offset: 0x1E51FD4 VA: 0x1E52FD4
	public void CheckAndApplyWonderRoadBonus(EntityController wonder) { }

	// RVA: 0x1E52FE8 Offset: 0x1E51FE8 VA: 0x1E52FE8
	public void StartTalking() { }

	// RVA: 0x1E52FFC Offset: 0x1E51FFC VA: 0x1E52FFC
	public void RequestConversation(EntityController source, float talkTime) { }

	// RVA: 0x1E53010 Offset: 0x1E52010 VA: 0x1E53010
	public List<InfoBarData> GetInfoBarDataList(bool includeBonuses = False, bool simple = False) { }

	// RVA: 0x1E53034 Offset: 0x1E52034 VA: 0x1E53034
	public List<InfoBarData> GetNextLevelDataList() { }

	// RVA: 0x1E53050 Offset: 0x1E52050 VA: 0x1E53050
	public bool IsSelected() { }

	// RVA: 0x1E5306C Offset: 0x1E5206C VA: 0x1E5306C
	public int GetFootprintSize() { }

	// RVA: 0x1E530A0 Offset: 0x1E520A0 VA: 0x1E530A0
	public bool IsMovable() { }

	// RVA: 0x1E530AC Offset: 0x1E520AC VA: 0x1E530AC
	public void Select(bool reselect = False) { }

	// RVA: 0x1E539D4 Offset: 0x1E529D4 VA: 0x1E539D4
	public bool get_IsFruitTree() { }

	// RVA: 0x1E539E4 Offset: 0x1E529E4 VA: 0x1E539E4
	public bool get_IsGoldMine() { }

	// RVA: 0x1E4C0FC Offset: 0x1E4B0FC VA: 0x1E4C0FC
	public bool get_IsMill() { }

	// RVA: 0x1E4C0EC Offset: 0x1E4B0EC VA: 0x1E4C0EC
	public bool get_IsMarket() { }

	// RVA: 0x1E539FC Offset: 0x1E529FC VA: 0x1E539FC
	public void DoWave() { }

	// RVA: 0x1E52920 Offset: 0x1E51920 VA: 0x1E52920
	public void Unselect(bool reselect = False) { }

	// RVA: 0x1E53A10 Offset: 0x1E52A10 VA: 0x1E53A10
	public bool IsOccupied() { }

	// RVA: 0x1E53A30 Offset: 0x1E52A30 VA: 0x1E53A30
	public void SetOccupied(bool occupied) { }

	// RVA: 0x1E53A44 Offset: 0x1E52A44 VA: 0x1E53A44
	public bool IsInAttackMode() { }

	// RVA: 0x1E53A4C Offset: 0x1E52A4C VA: 0x1E53A4C
	public float GetAttackRadius(EntityController target) { }

	// RVA: 0x1E53A64 Offset: 0x1E52A64 VA: 0x1E53A64
	public float GetSpreadRadius(EntityController target) { }

	// RVA: 0x1E53A7C Offset: 0x1E52A7C VA: 0x1E53A7C
	public TagEnum GetPreferredTarget() { }

	// RVA: 0x1E53A90 Offset: 0x1E52A90 VA: 0x1E53A90
	public TagEnum GetSuperPreferredTarget() { }

	// RVA: 0x1E53AA4 Offset: 0x1E52AA4 VA: 0x1E53AA4
	public int GetResearchCount() { }

	// RVA: 0x1E53B10 Offset: 0x1E52B10 VA: 0x1E53B10
	public void ExitDraggablePlacementMode() { }

	// RVA: 0x1E53B24 Offset: 0x1E52B24 VA: 0x1E53B24
	public void InitDraggablePlacementMode(int numDraggables, Action<DraggablePlacementHelper> OnOkClick, Action<DraggablePlacementHelper> OnCancelClick) { }

	// RVA: 0x1E53B38 Offset: 0x1E52B38 VA: 0x1E53B38
	public void OnMoving(Vector3 endPosition, TagEnum endOrientation = 0) { }

	// RVA: 0x1E53C14 Offset: 0x1E52C14 VA: 0x1E53C14
	public void OnMoveStart(bool showRoadCircuit = True, TagEnum endOrientation = 0) { }

	// RVA: 0x1E5236C Offset: 0x1E5136C VA: 0x1E5236C
	public void OnMoveComplete(bool hideRoadCircuit = True, bool saveEntity = True, bool showMessages = True, bool playSFX = True, TroopCampHelper.MethodToFormation methodToFormation = 1) { }

	// RVA: 0x1E53DC8 Offset: 0x1E52DC8 VA: 0x1E53DC8
	public void SetTileset(string tileset) { }

	// RVA: 0x1E53DD8 Offset: 0x1E52DD8 VA: 0x1E53DD8
	public object GetSaveObject() { }

	// RVA: 0x1E474AC Offset: 0x1E464AC VA: 0x1E474AC
	public void LoadObject(object obj) { }

	// RVA: 0x1E54AD8 Offset: 0x1E53AD8 VA: 0x1E54AD8
	public bool EveryoneReadyToAttack() { }

	// RVA: 0x1E54AEC Offset: 0x1E53AEC VA: 0x1E54AEC
	public void Die() { }

	// RVA: 0x1E54B00 Offset: 0x1E53B00 VA: 0x1E54B00
	public void Flee() { }

	// RVA: 0x1E54B14 Offset: 0x1E53B14 VA: 0x1E54B14
	public List<TagEnum> GetTroopTypes() { }

	// RVA: 0x1E54B70 Offset: 0x1E53B70 VA: 0x1E54B70
	public List<TagEnum> GetSpellTypes() { }

	// RVA: 0x1E54B98 Offset: 0x1E53B98 VA: 0x1E54B98
	public List<TagEnum> GetGeneralTypes() { }

	// RVA: 0x1E54BAC Offset: 0x1E53BAC VA: 0x1E54BAC
	public List<TagEnum> GetFactoryTypes() { }

	// RVA: 0x1E54BC0 Offset: 0x1E53BC0 VA: 0x1E54BC0
	public List<TagEnum> GetAircraftTypes() { }

	// RVA: 0x1E54BD4 Offset: 0x1E53BD4 VA: 0x1E54BD4
	public List<TagEnum> GetCoalitionTypes() { }

	// RVA: 0x1E54BE8 Offset: 0x1E53BE8 VA: 0x1E54BE8
	public void OpenCoalitionsWin() { }

	// RVA: 0x1E43D70 Offset: 0x1E42D70 VA: 0x1E43D70
	public bool IsResearching() { }

	// RVA: 0x1E54BFC Offset: 0x1E53BFC VA: 0x1E54BFC
	public bool IsArmoryResearching(bool firstSlot) { }

	// RVA: 0x1E54C14 Offset: 0x1E53C14 VA: 0x1E54C14
	public bool IsLibraryResearching(bool firstSlot) { }

	// RVA: 0x1E54C2C Offset: 0x1E53C2C VA: 0x1E54C2C
	public int CanBuySecondArmorySlot() { }

	// RVA: 0x1E54C44 Offset: 0x1E53C44 VA: 0x1E54C44
	public int CanBuySecondLibrarySlot() { }

	// RVA: 0x1E54C5C Offset: 0x1E53C5C VA: 0x1E54C5C
	public int CanBuySecondDirectiveResearchSlot() { }

	// RVA: 0x1E54C74 Offset: 0x1E53C74 VA: 0x1E54C74
	public int GetSecondArmorySlotCost() { }

	// RVA: 0x1E54C8C Offset: 0x1E53C8C VA: 0x1E54C8C
	public int GetSecondDirectiveResearchSlotCost() { }

	// RVA: 0x1E54CA4 Offset: 0x1E53CA4 VA: 0x1E54CA4
	public int GetSecondLibrarySlotCost() { }

	// RVA: 0x1E54CBC Offset: 0x1E53CBC VA: 0x1E54CBC
	public TagEnum GetSecondArmorySlotCostType() { }

	// RVA: 0x1E54CD0 Offset: 0x1E53CD0 VA: 0x1E54CD0
	public TagEnum GetSecondLibrarySlotCostType() { }

	// RVA: 0x1E54CE4 Offset: 0x1E53CE4 VA: 0x1E54CE4
	public TagEnum GetSecondDirectiveResearchSlotCostType() { }

	// RVA: 0x1E54CF8 Offset: 0x1E53CF8 VA: 0x1E54CF8
	public void DoBuySecondArmorySlot() { }

	// RVA: 0x1E54D0C Offset: 0x1E53D0C VA: 0x1E54D0C
	public void DoBuySecondLibrarySlot() { }

	// RVA: 0x1E54D20 Offset: 0x1E53D20 VA: 0x1E54D20
	public void TryBuySecondDirectiveResearchSlot() { }

	// RVA: 0x1E54D34 Offset: 0x1E53D34 VA: 0x1E54D34
	public void DoBuySecondDirectiveResearchSlot() { }

	// RVA: 0x1E54D48 Offset: 0x1E53D48 VA: 0x1E54D48
	public bool HasSecondArmorySlotUnlocked() { }

	// RVA: 0x1E54D68 Offset: 0x1E53D68 VA: 0x1E54D68
	public bool HasSecondLibrarySlotUnlocked() { }

	// RVA: 0x1E54D88 Offset: 0x1E53D88 VA: 0x1E54D88
	public bool HasSecondDirectiveResearchSlotUnlocked() { }

	// RVA: 0x1E54DA8 Offset: 0x1E53DA8 VA: 0x1E54DA8
	public bool IsResearchItemFirstSlot(TagEnum item) { }

	// RVA: 0x1E54DBC Offset: 0x1E53DBC VA: 0x1E54DBC
	public bool HasOpenResearchSlot() { }

	// RVA: 0x1E54DE4 Offset: 0x1E53DE4 VA: 0x1E54DE4
	public List<EntityController> GetFlags() { }

	// RVA: 0x1E54DFC Offset: 0x1E53DFC VA: 0x1E54DFC
	public void RefreshFlags() { }

	// RVA: 0x1E54E10 Offset: 0x1E53E10 VA: 0x1E54E10
	public void RemoveCoalition(TagEnum coalition) { }

	// RVA: 0x1E54E24 Offset: 0x1E53E24 VA: 0x1E54E24
	public bool HasCoalition(TagEnum coalition) { }

	// RVA: 0x1E54E38 Offset: 0x1E53E38 VA: 0x1E54E38
	public void AddCoalition(TagEnum coalition, int level) { }

	// RVA: 0x1E54E4C Offset: 0x1E53E4C VA: 0x1E54E4C
	public void UpdateCoalition(TagEnum coalition, int oldLevel, int newLevel) { }

	// RVA: 0x1E54E60 Offset: 0x1E53E60 VA: 0x1E54E60
	public void ClearCoalitions() { }

	// RVA: 0x1E54E74 Offset: 0x1E53E74 VA: 0x1E54E74
	public int GetMaxActiveCoalitions() { }

	// RVA: 0x1E54E88 Offset: 0x1E53E88 VA: 0x1E54E88
	public int GetNumActiveCoalitions() { }

	// RVA: 0x1E54E9C Offset: 0x1E53E9C VA: 0x1E54E9C
	public List<TagEnum> GetActiveCoalitions() { }

	// RVA: 0x1E54F20 Offset: 0x1E53F20 VA: 0x1E54F20
	public Dictionary<TagEnum, int> GetActiveCoalitionsAndLevels() { }

	// RVA: 0x1E54FA4 Offset: 0x1E53FA4 VA: 0x1E54FA4
	public void StartRecipeDelivery(string recipeName, bool isEventReward = False, bool isTempleBattlePrep = False) { }

	// RVA: 0x1E54FF8 Offset: 0x1E53FF8 VA: 0x1E54FF8
	public void DeliverFreeMercenary(string recipeName) { }

	// RVA: 0x1E5500C Offset: 0x1E5400C VA: 0x1E5500C
	public bool CanCreateRecipe(string recipeName) { }

	// RVA: 0x1E55020 Offset: 0x1E54020 VA: 0x1E55020
	public bool RecipeHasCrownCost(string recipeName) { }

	// RVA: 0x1E55034 Offset: 0x1E54034 VA: 0x1E55034
	public int GetRecipeCrownCost(string recipeName) { }

	// RVA: 0x1E55048 Offset: 0x1E54048 VA: 0x1E55048
	public bool RecipeComponentCheck(string recipeName) { }

	// RVA: 0x1E55070 Offset: 0x1E54070 VA: 0x1E55070
	public List<KeyValuePair<TagEnum, int>> GetMissingComponents(string recipe) { }

	// RVA: 0x1E55098 Offset: 0x1E54098 VA: 0x1E55098
	public bool MercenaryCapCheck(string recipeName) { }

	// RVA: 0x1E550AC Offset: 0x1E540AC VA: 0x1E550AC
	public bool MaxLevelCheck(string recipeName) { }

	// RVA: 0x1E550C0 Offset: 0x1E540C0 VA: 0x1E550C0
	public bool MinLevelCheck(string recipeName) { }

	// RVA: 0x1E550D4 Offset: 0x1E540D4 VA: 0x1E550D4
	public void ShowMercInfo() { }

	// RVA: 0x1E550E8 Offset: 0x1E540E8 VA: 0x1E550E8
	public bool BoostCountCheck(string recipeName) { }

	// RVA: 0x1E550FC Offset: 0x1E540FC VA: 0x1E550FC
	public bool CoalitionCountCheck(string recipeName) { }

	// RVA: 0x1E55110 Offset: 0x1E54110 VA: 0x1E55110
	public bool CoalitionCapCheck(string recipeName) { }

	// RVA: 0x1E55124 Offset: 0x1E54124 VA: 0x1E55124
	public TagEnum GetMercenaryName(string recipeName) { }

	// RVA: 0x1E55138 Offset: 0x1E54138 VA: 0x1E55138
	public TagEnum GetRecipeType(string recipeName) { }

	// RVA: 0x1E5514C Offset: 0x1E5414C VA: 0x1E5514C
	public TagEnum GetRecipeOutput(string recipeName) { }

	// RVA: 0x1E55160 Offset: 0x1E54160 VA: 0x1E55160
	public RecipeConfig GetMercenaryRecipe(TagEnum key) { }

	// RVA: 0x1E55174 Offset: 0x1E54174 VA: 0x1E55174
	public float GetResearchTimeLeft() { }

	// RVA: 0x1E551CC Offset: 0x1E541CC VA: 0x1E551CC
	public float GetArmoryResearchTimeLeft(TagEnum tag) { }

	// RVA: 0x1E551E4 Offset: 0x1E541E4 VA: 0x1E551E4
	public double GetLibraryResearchTimeLeft(LibraryTech tech) { }

	// RVA: 0x1E551FC Offset: 0x1E541FC VA: 0x1E551FC
	public float GetArmoryResearchTimeLeft(bool firstSlot) { }

	// RVA: 0x1E55218 Offset: 0x1E54218 VA: 0x1E55218
	public double GetLibraryResearchTimeLeft(bool firstSlot) { }

	// RVA: 0x1E55234 Offset: 0x1E54234 VA: 0x1E55234
	public double GetMasteryResearchTime(MasterySkillConfig config) { }

	// RVA: 0x1E5524C Offset: 0x1E5424C VA: 0x1E5524C
	public double GetMasteryResearchTimeLeft(MasterySkillConfig config) { }

	// RVA: 0x1E55264 Offset: 0x1E54264 VA: 0x1E55264
	public bool IsItemBeingResearched(TagEnum item) { }

	// RVA: 0x1E55278 Offset: 0x1E54278 VA: 0x1E55278
	public TagEnum GetItemBeingResearched(bool firstSlot) { }

	// RVA: 0x1E55290 Offset: 0x1E54290 VA: 0x1E55290
	public string GetItemBeingResearchedDisplayName(bool firstSlot = True) { }

	// RVA: 0x1E55310 Offset: 0x1E54310 VA: 0x1E55310
	public bool IsTroop(TagEnum type) { }

	// RVA: 0x1E55324 Offset: 0x1E54324 VA: 0x1E55324
	public bool IsSpell(TagEnum type) { }

	// RVA: 0x1E55338 Offset: 0x1E54338 VA: 0x1E55338
	public bool IsCoalition(TagEnum type) { }

	// RVA: 0x1E5534C Offset: 0x1E5434C VA: 0x1E5534C
	public bool IsGeneral(TagEnum type) { }

	// RVA: 0x1E55360 Offset: 0x1E54360 VA: 0x1E55360
	public bool IsFactory(TagEnum type) { }

	// RVA: 0x1E55374 Offset: 0x1E54374 VA: 0x1E55374
	public bool IsAircraft(TagEnum type) { }

	// RVA: 0x1E55388 Offset: 0x1E54388 VA: 0x1E55388
	public bool IsAirUnit() { }

	// RVA: 0x1E55390 Offset: 0x1E54390 VA: 0x1E55390
	public bool IsSingleBattleTypeUnit() { }

	// RVA: 0x1E55398 Offset: 0x1E54398 VA: 0x1E55398
	public bool IsFighterTypeUnit() { }

	// RVA: 0x1E553A0 Offset: 0x1E543A0 VA: 0x1E553A0
	public bool IsBomberTypeUnit() { }

	// RVA: 0x1E553A8 Offset: 0x1E543A8 VA: 0x1E553A8
	public bool IsTransportTypeUnit() { }

	// RVA: 0x1E553B0 Offset: 0x1E543B0 VA: 0x1E553B0
	public bool IsScoutTypeUnit() { }

	// RVA: 0x1E553B8 Offset: 0x1E543B8 VA: 0x1E553B8
	public bool IsSupportHelicopter() { }

	// RVA: 0x1E553D8 Offset: 0x1E543D8 VA: 0x1E553D8
	public bool IsHiddenInAttackHistory() { }

	// RVA: 0x1E553E0 Offset: 0x1E543E0 VA: 0x1E553E0
	public bool DoesSneakThroughGates() { }

	// RVA: 0x1E55400 Offset: 0x1E54400 VA: 0x1E55400
	public bool IsVulnerable() { }

	// RVA: 0x1E5542C Offset: 0x1E5442C VA: 0x1E5542C
	public double GetResearchTime(bool firstSlot) { }

	// RVA: 0x1E55460 Offset: 0x1E54460 VA: 0x1E55460
	public double GetResearchTime(TagEnum item) { }

	// RVA: 0x1E55478 Offset: 0x1E54478 VA: 0x1E55478
	public void StartInstantResearch(TagEnum troopType, bool deductResearchCost = True) { }

	// RVA: 0x1E55490 Offset: 0x1E54490 VA: 0x1E55490
	public void StartResearch(TagEnum troopType) { }

	// RVA: 0x1E554AC Offset: 0x1E544AC VA: 0x1E554AC
	public bool CanBuyThroughUpgrade() { }

	// RVA: 0x1E554E8 Offset: 0x1E544E8 VA: 0x1E554E8
	public bool CanBuyThroughTraining() { }

	// RVA: 0x1E55510 Offset: 0x1E54510 VA: 0x1E55510
	public bool CanBuyThrough() { }

	// RVA: 0x1E5574C Offset: 0x1E5474C VA: 0x1E5574C
	public bool CanBuyThroughArmoryAll() { }

	// RVA: 0x1E55794 Offset: 0x1E54794 VA: 0x1E55794
	public bool CanBuyThroughArmory(bool firstSlot) { }

	// RVA: 0x1E557F4 Offset: 0x1E547F4 VA: 0x1E557F4
	public bool CanBuyThroughArmoryItem(TagEnum item) { }

	// RVA: 0x1E5586C Offset: 0x1E5486C VA: 0x1E5586C
	public bool CanBuyThroughLibrary(bool firstSlot) { }

	// RVA: 0x1E558C8 Offset: 0x1E548C8 VA: 0x1E558C8
	public bool CanBuyThroughByMaster(int masterId) { }

	// RVA: 0x1E558DC Offset: 0x1E548DC VA: 0x1E558DC
	public bool CanBuyThroughAll() { }

	// RVA: 0x1E558F0 Offset: 0x1E548F0 VA: 0x1E558F0
	public int GetWorkerBuyThroughCost(JobType jobType, int masterId = 0) { }

	// RVA: 0x1E55A0C Offset: 0x1E54A0C VA: 0x1E55A0C
	public int GetBuyThroughCost() { }

	// RVA: 0x1E55AA8 Offset: 0x1E54AA8 VA: 0x1E55AA8
	public int GetArmoryBuyThroughCost(bool firstSlot) { }

	// RVA: 0x1E55B00 Offset: 0x1E54B00 VA: 0x1E55B00
	public int GetLibraryBuyThroughCost(bool firstSlot) { }

	// RVA: 0x1E55B54 Offset: 0x1E54B54 VA: 0x1E55B54
	public int GetBuyThroughCost(MasterySkillConfig config) { }

	// RVA: 0x1E3643C Offset: 0x1E3543C VA: 0x1E3643C
	public bool HasTag(TagEnum tag) { }

	// RVA: 0x1E55BAC Offset: 0x1E54BAC VA: 0x1E55BAC
	public void DoBuyThroughUpgrade() { }

	// RVA: 0x1E56134 Offset: 0x1E55134 VA: 0x1E56134
	public void DoBuyThroughTraining() { }

	// RVA: 0x1E56478 Offset: 0x1E55478 VA: 0x1E56478
	public void DoBuyThrough() { }

	// RVA: 0x1E56890 Offset: 0x1E55890 VA: 0x1E56890
	public void DoBuyThroughArmory(bool firstSlot, Action callback) { }

	// RVA: 0x1E56D74 Offset: 0x1E55D74 VA: 0x1E56D74
	public void DoBuyThroughLibrary(bool firstSlot) { }

	// RVA: 0x1E57224 Offset: 0x1E56224 VA: 0x1E57224
	public void DoBuyThroughWithCallback(string title, string body, Action callback) { }

	// RVA: 0x1E57520 Offset: 0x1E56520 VA: 0x1E57520
	public void DoBuyThroughArmoryWithCallback(string title, string body, bool firstSlot, Action callback) { }

	// RVA: 0x1E57858 Offset: 0x1E56858 VA: 0x1E57858
	public List<int> GetCurrentSkillsBeingResearched() { }

	// RVA: 0x1E5786C Offset: 0x1E5686C VA: 0x1E5786C
	public List<LibraryTech> GetResearchingLibraryTechs() { }

	// RVA: 0x1E57880 Offset: 0x1E56880 VA: 0x1E57880
	public List<TagEnum> GetResearchingArmoryTags() { }

	// RVA: 0x1E57894 Offset: 0x1E56894 VA: 0x1E57894
	public void DoBuyThroughAll() { }

	// RVA: 0x1E57BD0 Offset: 0x1E56BD0 VA: 0x1E57BD0
	public int GetAllJobBuyThroughCost() { }

	// RVA: 0x1E57BE4 Offset: 0x1E56BE4 VA: 0x1E57BE4
	public int GetAllCivilianCost() { }

	// RVA: 0x1E57BF8 Offset: 0x1E56BF8 VA: 0x1E57BF8
	public void DoBuyThroughByMaster(int masterId, bool shouldShowPopup = True) { }

	// RVA: 0x1E57F34 Offset: 0x1E56F34 VA: 0x1E57F34
	public int GetUniversityBuyThroughCostByMaster(int masterId) { }

	// RVA: 0x1E57F48 Offset: 0x1E56F48 VA: 0x1E57F48
	public double GetUniversityJobTimeLeftByMaster(int masterId) { }

	// RVA: 0x1E57F60 Offset: 0x1E56F60 VA: 0x1E57F60
	public void FinalFactoryCall() { }

	// RVA: 0x1E5829C Offset: 0x1E5729C VA: 0x1E5829C
	public void ForceBuyThrough(JobType jobType = 0, int masterId = 0) { }

	// RVA: 0x1E582C0 Offset: 0x1E572C0 VA: 0x1E582C0
	public HarvestHelper GetHarvestHelper() { }

	// RVA: 0x1E582C8 Offset: 0x1E572C8 VA: 0x1E582C8
	public TroopFactoryHelper GetTroopFactoryHelper() { }

	// RVA: 0x1E582D0 Offset: 0x1E572D0 VA: 0x1E582D0
	public SpellFactoryHelper GetSpellFactoryHelper() { }

	// RVA: 0x1E582D8 Offset: 0x1E572D8 VA: 0x1E582D8
	public EventSpellFactoryHelper GetEventSpellFactoryHelper() { }

	// RVA: 0x1E582E0 Offset: 0x1E572E0 VA: 0x1E582E0
	public LibraryHelper GetLibraryHelper() { }

	// RVA: 0x1E582E8 Offset: 0x1E572E8 VA: 0x1E582E8
	public bool CanHoldTroop(TagEnum type) { }

	// RVA: 0x1E582FC Offset: 0x1E572FC VA: 0x1E582FC
	public void AddTroopTrainLifetimeStats(TagEnum type) { }

	// RVA: 0x1E55EA8 Offset: 0x1E54EA8 VA: 0x1E55EA8
	private void _DoBuyThrough() { }

	// RVA: 0x1E56D08 Offset: 0x1E55D08 VA: 0x1E56D08
	private void _DoBuyThroughArmory(bool firstSlot) { }

	// RVA: 0x1E571CC Offset: 0x1E561CC VA: 0x1E571CC
	private void _DoBuyThroughLibrary(bool firstSlot) { }

	// RVA: 0x1E58310 Offset: 0x1E57310 VA: 0x1E58310
	public void DoTrainingBuyThrough(int buyoutCost) { }

	// RVA: 0x1E585B0 Offset: 0x1E575B0 VA: 0x1E585B0
	private void _DoTrainingBuyThrough() { }

	// RVA: 0x1E58644 Offset: 0x1E57644 VA: 0x1E58644
	public void DoResearchBuyThrough(int buyoutCost, Action noCallback) { }

	// RVA: 0x1E588CC Offset: 0x1E578CC VA: 0x1E588CC
	private void _DoResearchBuyThrough() { }

	// RVA: 0x1E58928 Offset: 0x1E57928 VA: 0x1E58928
	public void DoFreeBuyThrough(bool doRebate = True) { }

	// RVA: 0x1E589E0 Offset: 0x1E579E0 VA: 0x1E589E0
	public void ApplySpeedUp(Dictionary<Consumable, int> consumables, Action onComplete) { }

	// RVA: 0x1E58A58 Offset: 0x1E57A58 VA: 0x1E58A58
	public void ApplyUpgradeManual(Dictionary<Consumable, int> consumables, Action<int> onComplete, Action onFailure) { }

	// RVA: 0x1E58A6C Offset: 0x1E57A6C VA: 0x1E58A6C
	public void ApplySpeedUpExpedition(Dictionary<Consumable, int> consumables, int expeditionIdx, Action onComplete) { }

	// RVA: 0x1E58ADC Offset: 0x1E57ADC VA: 0x1E58ADC
	public void ApplySpeedUpResearch(Dictionary<Consumable, int> consumables, TagEnum tag, Action onComplete) { }

	// RVA: 0x1E58B4C Offset: 0x1E57B4C VA: 0x1E58B4C
	public void ApplySpeedUpResearch(Dictionary<Consumable, int> consumables, string techId, Action onComplete) { }

	// RVA: 0x1E58C18 Offset: 0x1E57C18 VA: 0x1E58C18
	public void ApplySpeedUpResearch(Dictionary<Consumable, int> consumables, MasterySkillConfig config, Action onComplete) { }

	// RVA: 0x1E58CD0 Offset: 0x1E57CD0 VA: 0x1E58CD0
	public void SetSpeedUpInFlight(bool inFlight, bool toggleSpinner) { }

	// RVA: 0x1E58CEC Offset: 0x1E57CEC VA: 0x1E58CEC
	public void SetSpeedUpInFlightResearch(string techId, bool inFlight, bool toggleSpinner) { }

	// RVA: 0x1E58D08 Offset: 0x1E57D08 VA: 0x1E58D08
	public void SetSpeedUpInFlightMastery(int skillId, bool inFlight, bool toggleSpinner) { }

	// RVA: 0x1E58D24 Offset: 0x1E57D24 VA: 0x1E58D24
	public bool IsSpeedUpInFlight() { }

	// RVA: 0x1E58D68 Offset: 0x1E57D68 VA: 0x1E58D68
	public bool IsUpgradeManualInFlight() { }

	// RVA: 0x1E58D88 Offset: 0x1E57D88 VA: 0x1E58D88
	public bool IsUpgradeManualInFlightForSelection(bool useAlternateResource) { }

	// RVA: 0x1E58DA0 Offset: 0x1E57DA0 VA: 0x1E58DA0
	public bool IsSpeedUpInFlightResearch() { }

	// RVA: 0x1E58DC8 Offset: 0x1E57DC8 VA: 0x1E58DC8
	public bool IsSpeedUpInFlightResearch(bool firstSlot) { }

	// RVA: 0x1E58DF8 Offset: 0x1E57DF8 VA: 0x1E58DF8
	public bool IsSpeedUpInFlightResearch(TagEnum tag) { }

	// RVA: 0x1E58E0C Offset: 0x1E57E0C VA: 0x1E58E0C
	public bool IsSpeedUpInFlightResearch(LibraryTech tech) { }

	// RVA: 0x1E58E20 Offset: 0x1E57E20 VA: 0x1E58E20
	public bool IsSpeedUpInFlightMastery(MasterySkillConfig config) { }

	// RVA: 0x1E58E34 Offset: 0x1E57E34 VA: 0x1E58E34
	public void ScaleTrainingTime() { }

	// RVA: 0x1E58E48 Offset: 0x1E57E48 VA: 0x1E58E48
	public LibraryTech GetTechBeingResearched() { }

	// RVA: 0x1E58E5C Offset: 0x1E57E5C VA: 0x1E58E5C
	public LibraryTech GetTechBeingResearched(bool firstSlot) { }

	// RVA: 0x1E58E74 Offset: 0x1E57E74 VA: 0x1E58E74
	public bool CanStartLibraryResearch(TechConfig tech) { }

	// RVA: 0x1E58E88 Offset: 0x1E57E88 VA: 0x1E58E88
	public bool CanStartAnyLibraryResearch(LibraryTreeConfig config) { }

	// RVA: 0x1E58E9C Offset: 0x1E57E9C VA: 0x1E58E9C
	public ValueTuple<int, int> GetLibraryTreeProgress(LibraryTreeConfig config) { }

	// RVA: 0x1E58F20 Offset: 0x1E57F20 VA: 0x1E58F20
	public bool CanResearchCapstone(TechConfig tech) { }

	// RVA: 0x1E58F34 Offset: 0x1E57F34 VA: 0x1E58F34
	public HashSet<string> GetResearchedTechs() { }

	// RVA: 0x1E58F48 Offset: 0x1E57F48 VA: 0x1E58F48
	public bool HasResearchedTech(TechConfig tech) { }

	// RVA: 0x1E58F5C Offset: 0x1E57F5C VA: 0x1E58F5C
	public bool HasResearchedTech(string tech) { }

	// RVA: 0x1E58F70 Offset: 0x1E57F70 VA: 0x1E58F70
	public List<string> GetSpoilsOfWarTechs() { }

	// RVA: 0x1E58FF4 Offset: 0x1E57FF4 VA: 0x1E58FF4
	public void RemoveTech(string techName) { }

	// RVA: 0x1E59008 Offset: 0x1E58008 VA: 0x1E59008
	public bool GetLibraryEventStatus(string key) { }

	// RVA: 0x1E5901C Offset: 0x1E5801C VA: 0x1E5901C
	public void SetLibraryEventStatus(string key, bool status) { }

	// RVA: 0x1E59034 Offset: 0x1E58034 VA: 0x1E59034
	public void StartLibraryResearch(TechConfig tech, TechConfig levelOneTech) { }

	// RVA: 0x1E59048 Offset: 0x1E58048 VA: 0x1E59048
	public void InstantResearch(TechConfig tech, bool paidCrowns = False) { }

	// RVA: 0x1E59060 Offset: 0x1E58060 VA: 0x1E59060
	public int GetNumCurrentSkillsBeingResearched() { }

	// RVA: 0x1E59074 Offset: 0x1E58074 VA: 0x1E59074
	public bool CanResearchWithoutCheckingResource(MasterySkillConfig Config) { }

	// RVA: 0x1E59088 Offset: 0x1E58088 VA: 0x1E59088
	public void StartMasterySkillResearch(MasterySkillConfig config) { }

	// RVA: 0x1E590A0 Offset: 0x1E580A0 VA: 0x1E590A0
	public bool HasResearchedMasterySkill(MasterySkillConfig config) { }

	// RVA: 0x1E590B4 Offset: 0x1E580B4 VA: 0x1E590B4
	public bool HasCompletedMasterySkill(MasterySkillConfig config) { }

	// RVA: 0x1E590C8 Offset: 0x1E580C8 VA: 0x1E590C8
	public bool HasCompletedSkillTree(int masterId) { }

	// RVA: 0x1E590DC Offset: 0x1E580DC VA: 0x1E590DC
	public int GetResearchedMasterySkillLevel(MasterySkillConfig config) { }

	// RVA: 0x1E590F0 Offset: 0x1E580F0 VA: 0x1E590F0
	public bool CheckMasterySkillRequirement(MasterySkillConfig config) { }

	// RVA: 0x1E59104 Offset: 0x1E58104 VA: 0x1E59104
	public bool CheckMasterySkillRequirement(MasterySkillConfig config, ref string output) { }

	// RVA: 0x1E59118 Offset: 0x1E58118 VA: 0x1E59118
	public MasterySkillConfig GetShortestResearchSkill() { }

	// RVA: 0x1E5912C Offset: 0x1E5812C VA: 0x1E5912C
	public MasterySkillConfig GetCurrentResearchByMaster(int masterId) { }

	// RVA: 0x1E58C84 Offset: 0x1E57C84 VA: 0x1E58C84
	public bool IsResearchingSkill(MasterySkillConfig config) { }

	// RVA: 0x1E59140 Offset: 0x1E58140 VA: 0x1E59140
	public bool IsResearchingUniversityMaster(int masterId) { }

	// RVA: 0x1E59154 Offset: 0x1E58154 VA: 0x1E59154
	public void UpdateLeaderResearchedCount() { }

	// RVA: 0x1E5918C Offset: 0x1E5818C VA: 0x1E5918C
	public void SetCurMasterySkillCountByMaster(int count, int masterId) { }

	// RVA: 0x1E591A0 Offset: 0x1E581A0 VA: 0x1E591A0
	public void SetMaxMasterySkillCountByMaster(int count, int masterId) { }

	// RVA: 0x1E591B4 Offset: 0x1E581B4 VA: 0x1E591B4
	public int GetCurMasterySkillCountByMaster(int masterId) { }

	// RVA: 0x1E591C8 Offset: 0x1E581C8 VA: 0x1E591C8
	public int GetMaxMasterySkillCountByMaster(int masterId) { }

	// RVA: 0x1E591DC Offset: 0x1E581DC VA: 0x1E591DC
	public bool MasterySkillAtMaxLevel(MasterySkillConfig config) { }

	// RVA: 0x1E59210 Offset: 0x1E58210 VA: 0x1E59210
	public void StartInstantMasterySkillResearch(MasterySkillConfig config) { }

	// RVA: 0x1E59224 Offset: 0x1E58224 VA: 0x1E59224
	public void GetMasterySkillRequirementDataList(MasterySkillConfig config, List<InfoBarData> infoBarDataList) { }

	// RVA: 0x1E59238 Offset: 0x1E58238 VA: 0x1E59238
	public void StartExpedition(int idx) { }

	// RVA: 0x1E5924C Offset: 0x1E5824C VA: 0x1E5924C
	public void ClaimExpedition(int idx, bool instantFinish) { }

	// RVA: 0x1E59264 Offset: 0x1E58264 VA: 0x1E59264
	public void StartDepart() { }

	// RVA: 0x1E59298 Offset: 0x1E58298 VA: 0x1E59298
	public void OnBoatIsAtDock() { }

	// RVA: 0x1E592CC Offset: 0x1E582CC VA: 0x1E592CC
	public void DismissGuitarPick() { }

	// RVA: 0x1E592E0 Offset: 0x1E582E0 VA: 0x1E592E0
	public void ShowGuitarPickV2() { }

	// RVA: 0x1E592F8 Offset: 0x1E582F8 VA: 0x1E592F8
	public bool IsOnExpedition() { }

	// RVA: 0x1E5930C Offset: 0x1E5830C VA: 0x1E5930C
	public int GetBuyThroughExpeditionCost(int idx) { }

	// RVA: 0x1E59320 Offset: 0x1E58320 VA: 0x1E59320
	public int GetForceCycleCost(int idx) { }

	// RVA: 0x1E59380 Offset: 0x1E58380 VA: 0x1E59380
	public double GetUnavailableExpeditionTime() { }

	// RVA: 0x1E59398 Offset: 0x1E58398 VA: 0x1E59398
	public int GetUnavailableExpeditionCost() { }

	// RVA: 0x1E593F8 Offset: 0x1E583F8 VA: 0x1E593F8
	public bool CanBuyThroughExpedition(int idx) { }

	// RVA: 0x1E5940C Offset: 0x1E5840C VA: 0x1E5940C
	public bool CanForceCycleExpedition(int idx) { }

	// RVA: 0x1E59420 Offset: 0x1E58420 VA: 0x1E59420
	public bool CanActivateUnavailableExpedition() { }

	// RVA: 0x1E59434 Offset: 0x1E58434 VA: 0x1E59434
	public void DoBuyThroughExpedition(int idx) { }

	// RVA: 0x1E59448 Offset: 0x1E58448 VA: 0x1E59448
	public void DoForceCycleExpedition(int idx) { }

	// RVA: 0x1E5945C Offset: 0x1E5845C VA: 0x1E5945C
	public void ActivateUnavailableExpeditionSlot(int idx) { }

	// RVA: 0x1E59470 Offset: 0x1E58470 VA: 0x1E59470
	public int GetShortestExpedition() { }

	// RVA: 0x1E59488 Offset: 0x1E58488 VA: 0x1E59488
	public double GetExpeditionTime(int idx) { }

	// RVA: 0x1E594A0 Offset: 0x1E584A0 VA: 0x1E594A0
	public double GetExpeditionTimeLeft(int idx) { }

	// RVA: 0x1E594B8 Offset: 0x1E584B8 VA: 0x1E594B8
	public double GetExpeditionTimeLeftAll() { }

	// RVA: 0x1E52358 Offset: 0x1E51358 VA: 0x1E52358
	public void ReleaseGroupChildren() { }

	// RVA: 0x1E594D0 Offset: 0x1E584D0 VA: 0x1E594D0
	public void AddBlips(ref GameObjectGroup gog) { }

	// RVA: 0x1E594E4 Offset: 0x1E584E4 VA: 0x1E594E4
	public List<EntityController> GetGroupObjects() { }

	// RVA: 0x1E59560 Offset: 0x1E58560 VA: 0x1E59560
	public void ShowGroupObjects(string groupId) { }

	// RVA: 0x1E59574 Offset: 0x1E58574 VA: 0x1E59574
	public void HideGroupObjects(string groupId) { }

	// RVA: 0x1E59588 Offset: 0x1E58588 VA: 0x1E59588
	public Dictionary<EntityController, Vector3> GetGroupPositions() { }

	// RVA: 0x1E595A0 Offset: 0x1E585A0 VA: 0x1E595A0
	public void UpdateGroupPositions() { }

	// RVA: 0x1E595D4 Offset: 0x1E585D4 VA: 0x1E595D4
	public void ChangeGateDisplaysToOpen() { }

	// RVA: 0x1E595E8 Offset: 0x1E585E8 VA: 0x1E595E8
	public void ChangeGateDisplaysToClose() { }

	// RVA: 0x1E595FC Offset: 0x1E585FC VA: 0x1E595FC
	public bool HasGroupId(string groupId) { }

	// RVA: 0x1E59610 Offset: 0x1E58610 VA: 0x1E59610
	public void AddTroop(TagEnum type) { }

	// RVA: 0x1E59624 Offset: 0x1E58624 VA: 0x1E59624
	public bool HasActivation() { }

	// RVA: 0x1E59634 Offset: 0x1E58634 VA: 0x1E59634
	public int GetFillLevel() { }

	// RVA: 0x1E59648 Offset: 0x1E58648 VA: 0x1E59648
	public void ChargeUse() { }

	// RVA: 0x1E5965C Offset: 0x1E5865C VA: 0x1E5965C
	public void ChargeBattleUse() { }

	// RVA: 0x1E59670 Offset: 0x1E58670 VA: 0x1E59670
	public int GetBattleFill() { }

	// RVA: 0x1E59684 Offset: 0x1E58684 VA: 0x1E59684
	public void DoTroopDonation(TagEnum troopType, int detachmentI = -1) { }

	// RVA: 0x1E596D0 Offset: 0x1E586D0 VA: 0x1E596D0
	public void DoTroopSpellDonation(TagEnum spellType) { }

	// RVA: 0x1E596E4 Offset: 0x1E586E4 VA: 0x1E596E4
	public List<TroopModel> GetUnspawnedDefenders() { }

	// RVA: 0x1E597C8 Offset: 0x1E587C8 VA: 0x1E597C8
	public bool HasTroop(TagEnum type) { }

	// RVA: 0x1E597DC Offset: 0x1E587DC VA: 0x1E597DC
	public void RemoveTroop(TagEnum type, int detachmentI = -1) { }

	// RVA: 0x1E5986C Offset: 0x1E5886C VA: 0x1E5986C
	public void UpdateGeneralLevels() { }

	// RVA: 0x1E598A0 Offset: 0x1E588A0 VA: 0x1E598A0
	public void RemoveTroopType(TagEnum type) { }

	// RVA: 0x1E598EC Offset: 0x1E588EC VA: 0x1E598EC
	public List<EntityController> GetTroops() { }

	// RVA: 0x1E59904 Offset: 0x1E58904 VA: 0x1E59904
	public List<TroopData> GetTroopData() { }

	// RVA: 0x1E5991C Offset: 0x1E5891C VA: 0x1E5991C
	public List<TagEnum> GetTroopsByTagEnum() { }

	// RVA: 0x1E59930 Offset: 0x1E58930 VA: 0x1E59930
	public void SpawnTroops() { }

	// RVA: 0x1E59944 Offset: 0x1E58944 VA: 0x1E59944
	public void RemoveAllTroops() { }

	// RVA: 0x1E59988 Offset: 0x1E58988 VA: 0x1E59988
	public void RemoveAllGarrisonedDefenders() { }

	// RVA: 0x1E5999C Offset: 0x1E5899C VA: 0x1E5999C
	public void RemoveAllTroopFromSimulation() { }

	// RVA: 0x1E599B0 Offset: 0x1E589B0 VA: 0x1E599B0
	public void RemoveGarrisonedGeneral(TagEnum key) { }

	// RVA: 0x1E599C4 Offset: 0x1E589C4 VA: 0x1E599C4
	public int GetTroopCount() { }

	// RVA: 0x1E599D8 Offset: 0x1E589D8 VA: 0x1E599D8
	public int GetTroopCount(TagEnum type) { }

	// RVA: 0x1E599EC Offset: 0x1E589EC VA: 0x1E599EC
	public int GetTroopPopulation() { }

	// RVA: 0x1E59A00 Offset: 0x1E58A00 VA: 0x1E59A00
	public int GetTroopCapacity() { }

	// RVA: 0x1E59A28 Offset: 0x1E58A28 VA: 0x1E59A28
	public int GetAvailableCountByTroopSpell(TagEnum spellType) { }

	// RVA: 0x1E59A3C Offset: 0x1E58A3C VA: 0x1E59A3C
	public void SortTroopSpellBuckets(int sortType, bool ascending) { }

	// RVA: 0x1E59A54 Offset: 0x1E58A54 VA: 0x1E59A54
	public int GetTroopSpellCapacity(bool includeBonus = True) { }

	// RVA: 0x1E59A6C Offset: 0x1E58A6C VA: 0x1E59A6C
	public int GetTroopSpellCapacity(bool includeBonus, out bool foundBonus) { }

	// RVA: 0x1E59A88 Offset: 0x1E58A88 VA: 0x1E59A88
	public bool IsAtTroopCapacity() { }

	// RVA: 0x1E59A9C Offset: 0x1E58A9C VA: 0x1E59A9C
	public bool IsAtSpellCapacity() { }

	// RVA: 0x1E59AC4 Offset: 0x1E58AC4 VA: 0x1E59AC4
	public void AddTerraCottaArmy() { }

	// RVA: 0x1E59AD8 Offset: 0x1E58AD8 VA: 0x1E59AD8
	public void RemoveTerraCottaArmy() { }

	// RVA: 0x1E59AEC Offset: 0x1E58AEC VA: 0x1E59AEC
	public void SetArmyInfo(FuzzingCoefficients fc, List<TagEnum> keys, List<FuzzedInt> amounts, List<FuzzedInt> levels, List<TagEnum> nations) { }

	// RVA: 0x1E59B04 Offset: 0x1E58B04 VA: 0x1E59B04
	public bool IsBusy() { }

	// RVA: 0x1E59B40 Offset: 0x1E58B40 VA: 0x1E59B40
	public bool IsFull() { }

	// RVA: 0x1E5A2A8 Offset: 0x1E592A8 VA: 0x1E5A2A8
	public void EnqueueTroopProduction(TagEnum troopType) { }

	// RVA: 0x1E5A2BC Offset: 0x1E592BC VA: 0x1E5A2BC
	public void RequeueGeneral(TagEnum troopType, int healthPercent) { }

	// RVA: 0x1E5A26C Offset: 0x1E5926C VA: 0x1E5A26C
	public TagEnum CapStat() { }

	// RVA: 0x1E5A2D0 Offset: 0x1E592D0 VA: 0x1E5A2D0
	public void ForcePersistIfDirty() { }

	// RVA: 0x1E5A2E4 Offset: 0x1E592E4 VA: 0x1E5A2E4
	public void DequeueTroopProduction(TagEnum troopType) { }

	// RVA: 0x1E5A2F8 Offset: 0x1E592F8 VA: 0x1E5A2F8
	public int GetEnqueuedTroopPopulation() { }

	// RVA: 0x1E5A30C Offset: 0x1E5930C VA: 0x1E5A30C
	public void EnqueueSpell(TagEnum spellType) { }

	// RVA: 0x1E5A320 Offset: 0x1E59320 VA: 0x1E5A320
	public void DequeueSpell(TagEnum spellType) { }

	// RVA: 0x1E5A294 Offset: 0x1E59294 VA: 0x1E5A294
	public int GetEnqueuedSpellCount() { }

	// RVA: 0x1E5A334 Offset: 0x1E59334 VA: 0x1E5A334
	public bool CanActivateTroopSpells() { }

	// RVA: 0x1E5A348 Offset: 0x1E59348 VA: 0x1E5A348
	public void ActivateTroopSpell(TagEnum spellType, Action OnComplete) { }

	// RVA: 0x1E5A360 Offset: 0x1E59360 VA: 0x1E5A360
	public void DeactivateTroopSpell(TagEnum spellType, Action OnComplete) { }

	// RVA: 0x1E5A378 Offset: 0x1E59378 VA: 0x1E5A378
	public void ClearActiveTroopSpellCounts() { }

	// RVA: 0x1E5A38C Offset: 0x1E5938C VA: 0x1E5A38C
	public void CheckActiveTroopSpellCount(TagEnum spellType, int count) { }

	// RVA: 0x1E5A3A0 Offset: 0x1E593A0 VA: 0x1E5A3A0
	public void CheckTroopSpellDesyncs() { }

	// RVA: 0x1E5A3B4 Offset: 0x1E593B4 VA: 0x1E5A3B4
	public void AddActiveAirMercToStronghold(TagEnum troopType) { }

	// RVA: 0x1E5A3C8 Offset: 0x1E593C8 VA: 0x1E5A3C8
	public void AssignTroopSpellToStronghold(TagEnum spellType, int amount) { }

	// RVA: 0x1E5A3DC Offset: 0x1E593DC VA: 0x1E5A3DC
	public void UpdateTroopSpellsInStronghold() { }

	// RVA: 0x1E5A3F0 Offset: 0x1E593F0 VA: 0x1E5A3F0
	public int GetQueueCapacity() { }

	// RVA: 0x1E5A418 Offset: 0x1E59418 VA: 0x1E5A418
	public double GetQueueTime() { }

	// RVA: 0x1E5A444 Offset: 0x1E59444 VA: 0x1E5A444
	public double GetRemainingTimeInQueue() { }

	// RVA: 0x1E5A470 Offset: 0x1E59470 VA: 0x1E5A470
	public double GetRemainingTimeInQueuesTilCapacity() { }

	// RVA: 0x1E5A49C Offset: 0x1E5949C VA: 0x1E5A49C
	public double GetRemainingTimeInQueues() { }

	// RVA: 0x1E5A4C8 Offset: 0x1E594C8 VA: 0x1E5A4C8
	public List<TroopBucket> GetTroopBuckets() { }

	// RVA: 0x1E5A4E0 Offset: 0x1E594E0 VA: 0x1E5A4E0
	public List<SpellBucket> GetSpellBuckets() { }

	// RVA: 0x1E5A50C Offset: 0x1E5950C VA: 0x1E5A50C
	public List<SpellBucket> GetActiveTroopSpells() { }

	// RVA: 0x1E5A524 Offset: 0x1E59524 VA: 0x1E5A524
	public int GetActiveTroopSpellCount() { }

	// RVA: 0x1E5A538 Offset: 0x1E59538 VA: 0x1E5A538
	public bool IsGeneralQueued() { }

	// RVA: 0x1E5A54C Offset: 0x1E5954C VA: 0x1E5A54C
	public bool IsTroopAvailable(TagEnum type) { }

	// RVA: 0x1E5A560 Offset: 0x1E59560 VA: 0x1E5A560
	public bool IsTroopUnlocked(TagEnum type) { }

	// RVA: 0x1E5A574 Offset: 0x1E59574 VA: 0x1E5A574
	public bool IsTroopAvailableHere(TagEnum type) { }

	// RVA: 0x1E5A588 Offset: 0x1E59588 VA: 0x1E5A588
	public bool IsSpellUnlocked(TagEnum type) { }

	// RVA: 0x1E5A59C Offset: 0x1E5959C VA: 0x1E5A59C
	public void RefreshProgressBar() { }

	// RVA: 0x1E5A5B0 Offset: 0x1E595B0 VA: 0x1E5A5B0
	public void MoveTroopToFormation(EntityController troop, TroopCampHelper.MethodToFormation methodToFormation) { }

	// RVA: 0x1E5A5C4 Offset: 0x1E595C4 VA: 0x1E5A5C4
	public void AddTroopToCamp(EntityController troop, bool autoMove) { }

	// RVA: 0x1E5A65C Offset: 0x1E5965C VA: 0x1E5A65C
	public void AddTroopToCamp(EntityController troop) { }

	// RVA: 0x1E43B64 Offset: 0x1E42B64 VA: 0x1E43B64
	public void SetEntityColor(Color color) { }

	// RVA: 0x1E5A664 Offset: 0x1E59664 VA: 0x1E5A664
	public Color GetEntityColor() { }

	// RVA: 0x1E5A694 Offset: 0x1E59694 VA: 0x1E5A694
	public string GetDebugTooltip() { }

	// RVA: 0x1E5A9FC Offset: 0x1E599FC VA: 0x1E5A9FC
	public List<ExpansionDisplayHelper.ExpansionDisplayLayer> GetExpansionDisplayLayers() { }

	// RVA: 0x1E5AA14 Offset: 0x1E59A14 VA: 0x1E5AA14
	public bool IsBlocked() { }

	// RVA: 0x1E5AA28 Offset: 0x1E59A28 VA: 0x1E5AA28
	public int GetAgeLimit(int age) { }

	// RVA: 0x1E5AA3C Offset: 0x1E59A3C VA: 0x1E5AA3C
	public void HideOuterLayer() { }

	// RVA: 0x1E5AA50 Offset: 0x1E59A50 VA: 0x1E5AA50
	public void ShowOuterLayer() { }

	// RVA: 0x1E5AA64 Offset: 0x1E59A64 VA: 0x1E5AA64
	public void ResetGarrisonedDefenders() { }

	// RVA: 0x1E5AAAC Offset: 0x1E59AAC VA: 0x1E5AAAC
	public static List<object> GetSaveObjectList(ICollection<EntityController> gameObjects) { }

	// RVA: 0x1E5AEA4 Offset: 0x1E59EA4 VA: 0x1E5AEA4
	public bool IsHuntable() { }

	// RVA: 0x1E5AEB4 Offset: 0x1E59EB4 VA: 0x1E5AEB4
	public bool ShouldWarpIn() { }

	// RVA: 0x1E4395C Offset: 0x1E4295C VA: 0x1E4395C
	public static void AddCountByType(TagEnum type) { }

	// RVA: 0x1E46F9C Offset: 0x1E45F9C VA: 0x1E46F9C
	public static void RemoveCountByType(TagEnum type) { }

	// RVA: 0x1E5AFB4 Offset: 0x1E59FB4 VA: 0x1E5AFB4
	public static int GetCountByType(TagEnum type) { }

	// RVA: 0x1E5B0C4 Offset: 0x1E5A0C4 VA: 0x1E5B0C4
	public static void ResetCountByType() { }

	// RVA: 0x1E5B1E4 Offset: 0x1E5A1E4 VA: 0x1E5B1E4
	public void ShowWonderCancelPopup() { }

	// RVA: 0x1E5B1F8 Offset: 0x1E5A1F8 VA: 0x1E5B1F8
	public void EmptyEventFillableBuilding() { }

	// RVA: 0x1E5B230 Offset: 0x1E5A230 VA: 0x1E5B230
	public void FinishRegenerating() { }

	// RVA: 0x1E5B244 Offset: 0x1E5A244 VA: 0x1E5B244
	public void SyncEmbassy(Dictionary<TagEnum, int> coalitionsAndLevels) { }

	// RVA: 0x1E5B258 Offset: 0x1E5A258 VA: 0x1E5B258
	public void ModifyRemainingPopCap(int delta) { }

	// RVA: 0x1E5B26C Offset: 0x1E5A26C VA: 0x1E5B26C
	public int GetRemainingPopCap() { }

	// RVA: 0x1E5B284 Offset: 0x1E5A284 VA: 0x1E5B284
	public void ShowRangeRadiusGraphic(Vector3 displacmentOverride) { }

	// RVA: 0x1E5B3C8 Offset: 0x1E5A3C8 VA: 0x1E5B3C8
	public void HideRangeRadiusGraphic() { }

	// RVA: 0x1E5B450 Offset: 0x1E5A450 VA: 0x1E5B450
	public void FindAndUpdateThreatLevel_Troop(EntityController troop) { }

	// RVA: 0x1E5B464 Offset: 0x1E5A464 VA: 0x1E5B464
	public void FindAndUpdateThreatLevel_Spell(TagEnum spell) { }

	// RVA: 0x1E5B478 Offset: 0x1E5A478 VA: 0x1E5B478
	public void SetSupplyDropDetails(SupplyDropLoot supplyDrop, SupplyDropEventConfig eventConfig) { }

	// RVA: 0x1E5B48C Offset: 0x1E5A48C VA: 0x1E5B48C
	public void SetSupplyDropDetails(SupplyDropLoot supplyDrop, string actionIcon) { }

	// RVA: 0x1E5B4A0 Offset: 0x1E5A4A0 VA: 0x1E5B4A0
	public bool IsEventSupplyDrop() { }

	// RVA: 0x1E5B4B4 Offset: 0x1E5A4B4 VA: 0x1E5B4B4
	public SupplyDropLoot GetSupplyDropLoot() { }

	// RVA: 0x1E5B4CC Offset: 0x1E5A4CC VA: 0x1E5B4CC
	public void DisplayDestroyPopup(string title, string body, string okLabel, Action onComplete) { }

	// RVA: 0x1E4E60C Offset: 0x1E4D60C VA: 0x1E4E60C
	public bool IsGlobalExpiration() { }

	// RVA: 0x1E5B4E0 Offset: 0x1E5A4E0 VA: 0x1E5B4E0
	public List<LootItem> GetAgeUpRewards(bool currentAge, Action onGot) { }

	// RVA: 0x1E5B4F8 Offset: 0x1E5A4F8 VA: 0x1E5B4F8
	public void ClearAgeUpRewards() { }

	// RVA: 0x1E5B50C Offset: 0x1E5A50C VA: 0x1E5B50C
	public List<EntityController> SpawnExtraDefenders(int count) { }

	// RVA: 0x1E5B598 Offset: 0x1E5A598 VA: 0x1E5B598
	public void ActivateDecoy(EntityController parent) { }

	// RVA: 0x1E5B5AC Offset: 0x1E5A5AC VA: 0x1E5B5AC
	public void PerformSpellAttack(SpellConfig spell, int spellLevel, Vector3 targetLocation, PlayerModel casterModel) { }

	// RVA: 0x1E5B5C0 Offset: 0x1E5A5C0 VA: 0x1E5B5C0
	public bool CanUseConsumable(TagEnum consumable, int level) { }

	// RVA: 0x1E5B5D4 Offset: 0x1E5A5D4 VA: 0x1E5B5D4
	public bool TrySetUpgradeStartTime(double startTime) { }

	// RVA: 0x1E5B5EC Offset: 0x1E5A5EC VA: 0x1E5B5EC
	public bool TrySetWonderStartTime(double startTime) { }

	// RVA: 0x1E5B600 Offset: 0x1E5A600 VA: 0x1E5B600
	public bool TrySetResearchStartTime(string id, double startTime) { }

	// RVA: 0x1E5B768 Offset: 0x1E5A768 VA: 0x1E5B768
	public void UpdateOffScreenNotifications() { }

	// RVA: 0x1E5B7A8 Offset: 0x1E5A7A8 VA: 0x1E5B7A8
	public bool IsTreasurePassMissedFinalClaim() { }

	// RVA: 0x1E5B7C8 Offset: 0x1E5A7C8 VA: 0x1E5B7C8
	public void HandleTreasurePassMissedFinalClaim() { }

	// RVA: 0x1E5B7DC Offset: 0x1E5A7DC VA: 0x1E5B7DC
	public void SetAoeHandler(Action<IList<EntityController>> handler) { }

	// RVA: 0x1E5B7EC Offset: 0x1E5A7EC VA: 0x1E5B7EC
	public static IEqualityComparer<EntityController> get_IdComparer() { }

	// RVA: 0x1E5B844 Offset: 0x1E5A844 VA: 0x1E5B844
	public object GetUpgradeHelperData() { }

	// RVA: 0x1E5B860 Offset: 0x1E5A860 VA: 0x1E5B860
	public void SetUpgradeHelperData(object obj) { }

	// RVA: 0x1E5B87C Offset: 0x1E5A87C VA: 0x1E5B87C
	public float GetDamageWithBonuses() { }

	// RVA: 0x1E5B894 Offset: 0x1E5A894 VA: 0x1E5B894
	public void LinkToDroneFactory(DroneFactoryHelper droneFactory) { }

	// RVA: 0x1E5B8B8 Offset: 0x1E5A8B8 VA: 0x1E5B8B8
	public bool ShouldHideGuitarPickOnSelect() { }

	// RVA: 0x1E5B8C8 Offset: 0x1E5A8C8 VA: 0x1E5B8C8
	public void ResetBatchHelpers() { }

	// RVA: 0x1E5B99C Offset: 0x1E5A99C VA: 0x1E5B99C
	public Nullable<double> GetUpgradeBatchHelperSecondsSpedUp() { }

	// RVA: 0x1E5B9B8 Offset: 0x1E5A9B8 VA: 0x1E5B9B8
	public void SetUpgradeBatchHelperSecondsSpedUp(Nullable<double> value) { }

	// RVA: 0x1E5B9C8 Offset: 0x1E5A9C8 VA: 0x1E5B9C8
	public DetachmentMarkerHelper GetDetachmentMarkerHelper() { }

	// RVA: 0x1E5B9D0 Offset: 0x1E5A9D0 VA: 0x1E5B9D0
	public void ToggleDetachmentSelection(bool toggle) { }

	// RVA: 0x1E5B9E8 Offset: 0x1E5A9E8 VA: 0x1E5B9E8
	public void .ctor() { }

	// RVA: 0x1E5BA08 Offset: 0x1E5AA08 VA: 0x1E5BA08
	private static void .cctor() { }

	[CompilerGenerated]
	// RVA: 0x1E5BAB4 Offset: 0x1E5AAB4 VA: 0x1E5BAB4
	private void <FillMapEditorActionIconDataList>b__342_0() { }

	[CompilerGenerated]
	// RVA: 0x1E5BEBC Offset: 0x1E5AEBC VA: 0x1E5BEBC
	private void <RemoveFromSimulation>b__416_0() { }

	[CompilerGenerated]
	// RVA: 0x1E5BEC8 Offset: 0x1E5AEC8 VA: 0x1E5BEC8
	private void <LevelUp>b__471_0(bool success) { }

	[CompilerGenerated]
	// RVA: 0x1E5BF58 Offset: 0x1E5AF58 VA: 0x1E5BF58
	private void <DoBuyThroughUpgrade>b__1069_0() { }

	[CompilerGenerated]
	// RVA: 0x1E5BF5C Offset: 0x1E5AF5C VA: 0x1E5BF5C
	private void <DoBuyThroughTraining>b__1070_0() { }

	[CompilerGenerated]
	// RVA: 0x1E5BFB0 Offset: 0x1E5AFB0 VA: 0x1E5BFB0
	private void <DoBuyThrough>b__1071_0() { }
}