/**
 * Ruin Selling Tracer - Comprehensive execution tracing for manual ruin selling operations
 * 
 * This tracer monitors the interaction with the SellRuins() method from the GoodyHutHelper class
 * (Token 0x6002B93, Address RVA "0x209DE3C") and provides detailed logging and performance metrics.
 */

export interface TracingConfig {
    enableTimingMetrics: boolean;
    enableStateTracking: boolean;
    enableParameterLogging: boolean;
    enableReturnValueLogging: boolean;
    enableErrorTracking: boolean;
    maxTraceEntries: number;
    logLevel: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
}

export interface MethodParameter {
    type: string;
    value: any;
    name?: string;
}

export interface InstanceState {
    [key: string]: any;
}

export interface TraceError {
    message: string;
    stack?: string;
    type: string;
}

export interface TraceEntry {
    traceId: string;
    timestamp: number;
    endTimestamp?: number;
    methodName: string;
    methodHandle?: string;
    instanceHandle: string;
    instanceType: string;
    parameters: MethodParameter[];
    returnValue?: MethodParameter;
    preCallState?: InstanceState;
    postCallState?: InstanceState;
    executionTimeMs?: number;
    status: 'STARTED' | 'COMPLETED' | 'ERROR';
    error?: TraceError;
    sessionId: string;
    entityIndex?: number;
}

export interface PerformanceSummary {
    totalCalls: number;
    successfulCalls: number;
    failedCalls: number;
    averageExecutionTimeMs: number;
    minExecutionTimeMs: number;
    maxExecutionTimeMs: number;
    totalExecutionTimeMs: number;
}

export class RuinSellingTracer {
    private isTracingActive: boolean = false;
    private traceHistory: TraceEntry[] = [];
    private config: TracingConfig;
    private activeTraces: Map<string, TraceEntry> = new Map();

    constructor(config?: Partial<TracingConfig>) {
        this.config = {
            enableTimingMetrics: true,
            enableStateTracking: true,
            enableParameterLogging: true,
            enableReturnValueLogging: true,
            enableErrorTracking: true,
            maxTraceEntries: 1000,
            logLevel: 'INFO',
            ...config
        };
    }

    /**
     * Start tracing ruin selling operations
     */
    startTracing(): void {
        this.isTracingActive = true;
        this.log('INFO', '🔍 Ruin selling tracer started');
    }

    /**
     * Stop tracing operations
     */
    stopTracing(): void {
        this.isTracingActive = false;
        this.activeTraces.clear();
        this.log('INFO', '⏹️ Ruin selling tracer stopped');
    }

    /**
     * Check if tracing is currently active
     */
    isTracing(): boolean {
        return this.isTracingActive;
    }

    /**
     * Update tracer configuration
     */
    updateConfiguration(newConfig: Partial<TracingConfig>): void {
        this.config = { ...this.config, ...newConfig };
        this.log('DEBUG', `🔧 Tracer configuration updated: ${JSON.stringify(newConfig)}`);
    }

    /**
     * Get current configuration
     */
    getConfiguration(): TracingConfig {
        return { ...this.config };
    }

    /**
     * Trace the start of a method call
     */
    traceMethodCall(
        methodName: string,
        instance: any,
        parameters: MethodParameter[],
        sessionId: string,
        entityIndex?: number
    ): string {
        if (!this.isTracingActive) {
            return '';
        }

        const traceId = this.generateTraceId();
        const timestamp = Date.now();

        const entry: TraceEntry = {
            traceId,
            timestamp,
            methodName,
            instanceHandle: this.extractInstanceHandle(instance),
            instanceType: this.extractInstanceType(instance),
            parameters: this.config.enableParameterLogging ? parameters : [],
            status: 'STARTED',
            sessionId,
            entityIndex
        };

        // Capture pre-call state if enabled
        if (this.config.enableStateTracking) {
            entry.preCallState = this.captureInstanceState(instance);
        }

        // Extract method handle if available
        try {
            const method = instance.method?.(methodName);
            if (method?.handle) {
                entry.methodHandle = method.handle.toString();
            }
        } catch (error) {
            this.log('DEBUG', `Could not extract method handle for ${methodName}: ${error}`);
        }

        this.activeTraces.set(traceId, entry);
        this.addToHistory(entry);

        this.log('DEBUG', `📞 Method call traced: ${methodName} (${traceId})`);
        return traceId;
    }

    /**
     * Trace the completion of a method call
     */
    traceMethodCompletion(traceId: string, returnValue?: MethodParameter): void {
        if (!this.isTracingActive || !traceId) {
            return;
        }

        const entry = this.activeTraces.get(traceId);
        if (!entry) {
            this.log('WARN', `⚠️ No active trace found for ID: ${traceId}`);
            return;
        }

        const endTimestamp = Date.now();
        entry.endTimestamp = endTimestamp;
        entry.status = 'COMPLETED';

        if (this.config.enableTimingMetrics) {
            entry.executionTimeMs = endTimestamp - entry.timestamp;
        }

        if (this.config.enableReturnValueLogging && returnValue) {
            entry.returnValue = returnValue;
        }

        // Capture post-call state if enabled
        if (this.config.enableStateTracking) {
            try {
                // We need to get the instance from somewhere - this is a limitation
                // In practice, this would be passed or stored
                this.log('DEBUG', 'Post-call state capture requires instance reference');
            } catch (error) {
                this.log('DEBUG', `Could not capture post-call state: ${error}`);
            }
        }

        this.activeTraces.delete(traceId);
        this.updateHistoryEntry(entry);

        this.log('DEBUG', `✅ Method completion traced: ${entry.methodName} (${traceId}) - ${entry.executionTimeMs}ms`);
    }

    /**
     * Trace a method error
     */
    traceMethodError(traceId: string, error: Error): void {
        if (!this.isTracingActive || !traceId) {
            return;
        }

        const entry = this.activeTraces.get(traceId);
        if (!entry) {
            this.log('WARN', `⚠️ No active trace found for error in ID: ${traceId}`);
            return;
        }

        const endTimestamp = Date.now();
        entry.endTimestamp = endTimestamp;
        entry.status = 'ERROR';

        if (this.config.enableTimingMetrics) {
            entry.executionTimeMs = endTimestamp - entry.timestamp;
        }

        if (this.config.enableErrorTracking) {
            entry.error = {
                message: error.message,
                stack: error.stack,
                type: error.constructor.name
            };
        }

        this.activeTraces.delete(traceId);
        this.updateHistoryEntry(entry);

        this.log('ERROR', `❌ Method error traced: ${entry.methodName} (${traceId}) - ${error.message}`);
    }

    /**
     * Get trace history
     */
    getTraceHistory(): TraceEntry[] {
        return [...this.traceHistory];
    }

    /**
     * Clear trace history
     */
    clearTraceHistory(): void {
        this.traceHistory = [];
        this.log('INFO', '🗑️ Trace history cleared');
    }

    /**
     * Get performance summary
     */
    getPerformanceSummary(): PerformanceSummary {
        const completedTraces = this.traceHistory.filter(t => t.status === 'COMPLETED' && t.executionTimeMs !== undefined);
        const errorTraces = this.traceHistory.filter(t => t.status === 'ERROR');

        if (completedTraces.length === 0) {
            return {
                totalCalls: this.traceHistory.length,
                successfulCalls: 0,
                failedCalls: errorTraces.length,
                averageExecutionTimeMs: 0,
                minExecutionTimeMs: 0,
                maxExecutionTimeMs: 0,
                totalExecutionTimeMs: 0
            };
        }

        const executionTimes = completedTraces.map(t => t.executionTimeMs!);
        const totalExecutionTime = executionTimes.reduce((sum, time) => sum + time, 0);

        return {
            totalCalls: this.traceHistory.length,
            successfulCalls: completedTraces.length,
            failedCalls: errorTraces.length,
            averageExecutionTimeMs: totalExecutionTime / completedTraces.length,
            minExecutionTimeMs: Math.min(...executionTimes),
            maxExecutionTimeMs: Math.max(...executionTimes),
            totalExecutionTimeMs: totalExecutionTime
        };
    }

    /**
     * Generate structured log output
     */
    generateStructuredLog(): string {
        const summary = this.getPerformanceSummary();
        const lines: string[] = [];

        lines.push('=== RUIN_SELLING_TRACE_REPORT ===');
        lines.push(`Timestamp: ${new Date().toISOString()}`);
        lines.push(`Total Calls: ${summary.totalCalls}`);
        lines.push(`Successful: ${summary.successfulCalls}`);
        lines.push(`Failed: ${summary.failedCalls}`);
        lines.push(`Average Execution Time: ${summary.averageExecutionTimeMs.toFixed(2)}ms`);
        lines.push('');

        this.traceHistory.forEach(entry => {
            lines.push(`[${new Date(entry.timestamp).toISOString()}] ${entry.methodName}`);
            lines.push(`  TraceID: ${entry.traceId}`);
            lines.push(`  Session: ${entry.sessionId}`);
            lines.push(`  Instance: ${entry.instanceHandle} (${entry.instanceType})`);
            lines.push(`  Status: ${entry.status}`);
            
            if (entry.executionTimeMs !== undefined) {
                lines.push(`  Duration: ${entry.executionTimeMs}ms`);
            }

            if (entry.error) {
                lines.push(`  Error: ${entry.error.message}`);
            }

            if (entry.parameters.length > 0) {
                lines.push(`  Parameters: ${JSON.stringify(entry.parameters)}`);
            }

            if (entry.returnValue) {
                lines.push(`  Return: ${JSON.stringify(entry.returnValue)}`);
            }

            lines.push('');
        });

        return lines.join('\n');
    }

    // Private helper methods

    private generateTraceId(): string {
        return `trace_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    }

    private extractInstanceHandle(instance: any): string {
        try {
            return instance?.handle?.toString() || 'unknown';
        } catch {
            return 'unknown';
        }
    }

    private extractInstanceType(instance: any): string {
        try {
            return instance?.class?.name || instance?.constructor?.name || 'unknown';
        } catch {
            return 'unknown';
        }
    }

    private captureInstanceState(instance: any): InstanceState {
        const state: InstanceState = {};
        
        try {
            // Common GoodyHut-related fields to capture
            const fieldsToCapture = [
                'm_goodyHut', 'goodyHutState', 'rewardType', 'rewardAmount',
                'collectTime', 'isCompleted', 'hasRuins', 'canSell'
            ];

            fieldsToCapture.forEach(fieldName => {
                try {
                    const field = instance.field?.(fieldName);
                    if (field) {
                        state[fieldName] = field.value;
                    }
                } catch {
                    // Field doesn't exist or can't be accessed
                }
            });
        } catch (error) {
            this.log('DEBUG', `Error capturing instance state: ${error}`);
        }

        return state;
    }

    private addToHistory(entry: TraceEntry): void {
        this.traceHistory.unshift(entry);
        
        // Limit history size
        if (this.traceHistory.length > this.config.maxTraceEntries) {
            this.traceHistory = this.traceHistory.slice(0, this.config.maxTraceEntries);
        }
    }

    private updateHistoryEntry(updatedEntry: TraceEntry): void {
        const index = this.traceHistory.findIndex(e => e.traceId === updatedEntry.traceId);
        if (index !== -1) {
            this.traceHistory[index] = updatedEntry;
        }
    }

    private log(level: string, message: string): void {
        const levels = ['DEBUG', 'INFO', 'WARN', 'ERROR'];
        const configLevel = levels.indexOf(this.config.logLevel);
        const messageLevel = levels.indexOf(level);

        if (messageLevel >= configLevel) {
            console.log(`[RUIN_TRACER:${level}] ${message}`);
        }
    }
}

/**
 * Integration helper for connecting the tracer with the GoodyHut collection system
 */
export class RuinSellingTracerIntegration {
    private tracer: RuinSellingTracer;
    private originalExecuteRuinSelling?: Function;

    constructor(tracer: RuinSellingTracer) {
        this.tracer = tracer;
    }

    /**
     * Integrate tracer with the robust instance handler
     */
    integrateWithInstanceHandler(instanceHandler: any): void {
        if (!instanceHandler || typeof instanceHandler.executeRuinSelling !== 'function') {
            console.log('❌ Cannot integrate tracer: executeRuinSelling method not found');
            return;
        }

        // Store original method
        this.originalExecuteRuinSelling = instanceHandler.executeRuinSelling.bind(instanceHandler);

        // Replace with traced version
        instanceHandler.executeRuinSelling = this.createTracedExecuteRuinSelling(instanceHandler);

        console.log('✅ Ruin selling tracer integrated with instance handler');
    }

    /**
     * Remove tracer integration
     */
    removeIntegration(instanceHandler: any): void {
        if (this.originalExecuteRuinSelling && instanceHandler) {
            instanceHandler.executeRuinSelling = this.originalExecuteRuinSelling;
            this.originalExecuteRuinSelling = undefined;
            console.log('✅ Ruin selling tracer integration removed');
        }
    }

    /**
     * Create a traced version of executeRuinSelling method
     */
    private createTracedExecuteRuinSelling(instanceHandler: any): Function {
        return (validation: any, ruinInfo: any): boolean => {
            if (!this.tracer.isTracing()) {
                return this.originalExecuteRuinSelling!(validation, ruinInfo);
            }

            const sessionId = `manual-ruin-${Date.now()}`;
            const parameters = [
                { type: 'ValidationResult', value: validation, name: 'validation' },
                { type: 'RuinInfo', value: ruinInfo, name: 'ruinInfo' }
            ];

            const traceId = this.tracer.traceMethodCall(
                'SellRuins',
                validation.instance,
                parameters,
                sessionId,
                validation.entityIndex
            );

            try {
                const result = this.originalExecuteRuinSelling!(validation, ruinInfo);

                this.tracer.traceMethodCompletion(traceId, {
                    type: 'boolean',
                    value: result,
                    name: 'success'
                });

                return result;
            } catch (error) {
                this.tracer.traceMethodError(traceId, error as Error);
                throw error;
            }
        };
    }

    /**
     * Get tracer instance
     */
    getTracer(): RuinSellingTracer {
        return this.tracer;
    }
}

/**
 * Global tracer instance for easy access
 */
export const globalRuinSellingTracer = new RuinSellingTracer({
    logLevel: 'INFO',
    maxTraceEntries: 500
});

/**
 * Global integration instance
 */
export const globalTracerIntegration = new RuinSellingTracerIntegration(globalRuinSellingTracer);
