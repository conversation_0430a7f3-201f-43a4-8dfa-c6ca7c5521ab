📦
247540 /src/robust-instance-handler.js
✄
var An=Object.defineProperty;var ol=Object.getOwnPropertyDescriptor;var sl=Object.getOwnPropertyNames;var ll=Object.prototype.hasOwnProperty;var M=(e,t)=>()=>(e&&(t=e(e=0)),t);var tt=(e,t)=>{for(var n in t)An(e,n,{get:t[n],enumerable:!0})},al=(e,t,n,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of sl(t))!ll.call(e,i)&&i!==n&&An(e,i,{get:()=>t[i],enumerable:!(r=ol(t,i))||r.enumerable});return e};var me=e=>al(An({},"__esModule",{value:!0}),e);function cl(e){let t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");let n=e.indexOf("=");n===-1&&(n=t);let r=n===t?0:4-n%4;return[n,r]}function ul(e,t,n){return(t+n)*3/4-n}function ui(e){let t=cl(e),n=t[0],r=t[1],i=new Uint8Array(ul(e,n,r)),o=0,s=r>0?n-4:n,l;for(l=0;l<s;l+=4){let c=ue[e.charCodeAt(l)]<<18|ue[e.charCodeAt(l+1)]<<12|ue[e.charCodeAt(l+2)]<<6|ue[e.charCodeAt(l+3)];i[o++]=c>>16&255,i[o++]=c>>8&255,i[o++]=c&255}if(r===2){let c=ue[e.charCodeAt(l)]<<2|ue[e.charCodeAt(l+1)]>>4;i[o++]=c&255}if(r===1){let c=ue[e.charCodeAt(l)]<<10|ue[e.charCodeAt(l+1)]<<4|ue[e.charCodeAt(l+2)]>>2;i[o++]=c>>8&255,i[o++]=c&255}return i}function fl(e){return ye[e>>18&63]+ye[e>>12&63]+ye[e>>6&63]+ye[e&63]}function dl(e,t,n){let r=[];for(let i=t;i<n;i+=3){let o=(e[i]<<16&16711680)+(e[i+1]<<8&65280)+(e[i+2]&255);r.push(fl(o))}return r.join("")}function vn(e){let t=e.length,n=t%3,r=[],i=16383;for(let o=0,s=t-n;o<s;o+=i)r.push(dl(e,o,o+i>s?s:o+i));if(n===1){let o=e[t-1];r.push(ye[o>>2]+ye[o<<4&63]+"==")}else if(n===2){let o=(e[t-2]<<8)+e[t-1];r.push(ye[o>>10]+ye[o>>4&63]+ye[o<<2&63]+"=")}return r.join("")}var ye,ue,Rn,fi=M(()=>{"use strict";T();ye=[],ue=[],Rn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(let e=0,t=Rn.length;e<t;++e)ye[e]=Rn[e],ue[Rn.charCodeAt(e)]=e;ue[45]=62;ue[95]=63});function bt(e,t,n,r,i){let o,s,l=i*8-r-1,c=(1<<l)-1,f=c>>1,h=-7,a=n?i-1:0,u=n?-1:1,d=e[t+a];for(a+=u,o=d&(1<<-h)-1,d>>=-h,h+=l;h>0;)o=o*256+e[t+a],a+=u,h-=8;for(s=o&(1<<-h)-1,o>>=-h,h+=r;h>0;)s=s*256+e[t+a],a+=u,h-=8;if(o===0)o=1-f;else{if(o===c)return s?NaN:(d?-1:1)*(1/0);s=s+Math.pow(2,r),o=o-f}return(d?-1:1)*s*Math.pow(2,o-r)}function Nn(e,t,n,r,i,o){let s,l,c,f=o*8-i-1,h=(1<<f)-1,a=h>>1,u=i===23?Math.pow(2,-24)-Math.pow(2,-77):0,d=r?0:o-1,p=r?1:-1,m=t<0||t===0&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(l=isNaN(t)?1:0,s=h):(s=Math.floor(Math.log(t)/Math.LN2),t*(c=Math.pow(2,-s))<1&&(s--,c*=2),s+a>=1?t+=u/c:t+=u*Math.pow(2,1-a),t*c>=2&&(s++,c/=2),s+a>=h?(l=0,s=h):s+a>=1?(l=(t*c-1)*Math.pow(2,i),s=s+a):(l=t*Math.pow(2,a-1)*Math.pow(2,i),s=0));i>=8;)e[n+d]=l&255,d+=p,l/=256,i-=8;for(s=s<<i|l,f+=i;f>0;)e[n+d]=s&255,d+=p,s/=256,f-=8;e[n+d-p]|=m*128}var di=M(()=>{"use strict";T();});function we(e){if(e>kn)throw new RangeError('The value "'+e+'" is invalid for option "size"');let t=new Uint8Array(e);return Object.setPrototypeOf(t,g.prototype),t}function g(e,t,n){if(typeof e=="number"){if(typeof t=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return Ln(e)}return mi(e,t,n)}function mi(e,t,n){if(typeof e=="string")return yl(e,t);if(ArrayBuffer.isView(e))return bl(e);if(e==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(e instanceof ArrayBuffer||e&&e.buffer instanceof ArrayBuffer||e instanceof SharedArrayBuffer||e&&e.buffer instanceof SharedArrayBuffer)return On(e,t,n);if(typeof e=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');let r=e.valueOf&&e.valueOf();if(r!=null&&r!==e)return g.from(r,t,n);let i=Sl(e);if(i)return i;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof e[Symbol.toPrimitive]=="function")return g.from(e[Symbol.toPrimitive]("string"),t,n);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function yi(e){if(typeof e!="number")throw new TypeError('"size" argument must be of type number');if(e<0)throw new RangeError('The value "'+e+'" is invalid for option "size"')}function ml(e,t,n){return yi(e),e<=0?we(e):t!==void 0?typeof n=="string"?we(e).fill(t,n):we(e).fill(t):we(e)}function Ln(e){return yi(e),we(e<0?0:$n(e)|0)}function yl(e,t){if((typeof t!="string"||t==="")&&(t="utf8"),!g.isEncoding(t))throw new TypeError("Unknown encoding: "+t);let n=bi(e,t)|0,r=we(n),i=r.write(e,t);return i!==n&&(r=r.slice(0,i)),r}function Mn(e){let t=e.length<0?0:$n(e.length)|0,n=we(t);for(let r=0;r<t;r+=1)n[r]=e[r]&255;return n}function bl(e){if(e instanceof Uint8Array){let t=new Uint8Array(e);return On(t.buffer,t.byteOffset,t.byteLength)}return Mn(e)}function On(e,t,n){if(t<0||e.byteLength<t)throw new RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(n||0))throw new RangeError('"length" is outside of buffer bounds');let r;return t===void 0&&n===void 0?r=new Uint8Array(e):n===void 0?r=new Uint8Array(e,t):r=new Uint8Array(e,t,n),Object.setPrototypeOf(r,g.prototype),r}function Sl(e){if(g.isBuffer(e)){let t=$n(e.length)|0,n=we(t);return n.length===0||e.copy(n,0,0,t),n}if(e.length!==void 0)return typeof e.length!="number"||Number.isNaN(e.length)?we(0):Mn(e);if(e.type==="Buffer"&&Array.isArray(e.data))return Mn(e.data)}function $n(e){if(e>=kn)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+kn.toString(16)+" bytes");return e|0}function bi(e,t){if(g.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||e instanceof ArrayBuffer)return e.byteLength;if(typeof e!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);let n=e.length,r=arguments.length>2&&arguments[2]===!0;if(!r&&n===0)return 0;let i=!1;for(;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":return Cn(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return n*2;case"hex":return n>>>1;case"base64":return vi(e).length;default:if(i)return r?-1:Cn(e).length;t=(""+t).toLowerCase(),i=!0}}function wl(e,t,n){let r=!1;if((t===void 0||t<0)&&(t=0),t>this.length||((n===void 0||n>this.length)&&(n=this.length),n<=0)||(n>>>=0,t>>>=0,n<=t))return"";for(e||(e="utf8");;)switch(e){case"hex":return Ml(this,t,n);case"utf8":case"utf-8":return wi(this,t,n);case"ascii":return Nl(this,t,n);case"latin1":case"binary":return kl(this,t,n);case"base64":return Rl(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return Ol(this,t,n);default:if(r)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),r=!0}}function De(e,t,n){let r=e[t];e[t]=e[n],e[n]=r}function Si(e,t,n,r,i){if(e.length===0)return-1;if(typeof n=="string"?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,Number.isNaN(n)&&(n=i?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(i)return-1;n=e.length-1}else if(n<0)if(i)n=0;else return-1;if(typeof t=="string"&&(t=g.from(t,r)),g.isBuffer(t))return t.length===0?-1:hi(e,t,n,r,i);if(typeof t=="number")return t=t&255,typeof Uint8Array.prototype.indexOf=="function"?i?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):hi(e,[t],n,r,i);throw new TypeError("val must be string, number or Buffer")}function hi(e,t,n,r,i){let o=1,s=e.length,l=t.length;if(r!==void 0&&(r=String(r).toLowerCase(),r==="ucs2"||r==="ucs-2"||r==="utf16le"||r==="utf-16le")){if(e.length<2||t.length<2)return-1;o=2,s/=2,l/=2,n/=2}function c(h,a){return o===1?h[a]:h.readUInt16BE(a*o)}let f;if(i){let h=-1;for(f=n;f<s;f++)if(c(e,f)===c(t,h===-1?0:f-h)){if(h===-1&&(h=f),f-h+1===l)return h*o}else h!==-1&&(f-=f-h),h=-1}else for(n+l>s&&(n=s-l),f=n;f>=0;f--){let h=!0;for(let a=0;a<l;a++)if(c(e,f+a)!==c(t,a)){h=!1;break}if(h)return f}return-1}function _l(e,t,n,r){n=Number(n)||0;let i=e.length-n;r?(r=Number(r),r>i&&(r=i)):r=i;let o=t.length;r>o/2&&(r=o/2);let s;for(s=0;s<r;++s){let l=parseInt(t.substr(s*2,2),16);if(Number.isNaN(l))return s;e[n+s]=l}return s}function El(e,t,n,r){return jt(Cn(t,e.length-n),e,n,r)}function xl(e,t,n,r){return jt(Pl(t),e,n,r)}function Tl(e,t,n,r){return jt(vi(t),e,n,r)}function Al(e,t,n,r){return jt(Fl(t,e.length-n),e,n,r)}function Rl(e,t,n){return t===0&&n===e.length?vn(e):vn(e.slice(t,n))}function wi(e,t,n){n=Math.min(e.length,n);let r=[],i=t;for(;i<n;){let o=e[i],s=null,l=o>239?4:o>223?3:o>191?2:1;if(i+l<=n){let c,f,h,a;switch(l){case 1:o<128&&(s=o);break;case 2:c=e[i+1],(c&192)===128&&(a=(o&31)<<6|c&63,a>127&&(s=a));break;case 3:c=e[i+1],f=e[i+2],(c&192)===128&&(f&192)===128&&(a=(o&15)<<12|(c&63)<<6|f&63,a>2047&&(a<55296||a>57343)&&(s=a));break;case 4:c=e[i+1],f=e[i+2],h=e[i+3],(c&192)===128&&(f&192)===128&&(h&192)===128&&(a=(o&15)<<18|(c&63)<<12|(f&63)<<6|h&63,a>65535&&a<1114112&&(s=a))}}s===null?(s=65533,l=1):s>65535&&(s-=65536,r.push(s>>>10&1023|55296),s=56320|s&1023),r.push(s),i+=l}return vl(r)}function vl(e){let t=e.length;if(t<=gi)return String.fromCharCode.apply(String,e);let n="",r=0;for(;r<t;)n+=String.fromCharCode.apply(String,e.slice(r,r+=gi));return n}function Nl(e,t,n){let r="";n=Math.min(e.length,n);for(let i=t;i<n;++i)r+=String.fromCharCode(e[i]&127);return r}function kl(e,t,n){let r="";n=Math.min(e.length,n);for(let i=t;i<n;++i)r+=String.fromCharCode(e[i]);return r}function Ml(e,t,n){let r=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>r)&&(n=r);let i="";for(let o=t;o<n;++o)i+=Bl[e[o]];return i}function Ol(e,t,n){let r=e.slice(t,n),i="";for(let o=0;o<r.length-1;o+=2)i+=String.fromCharCode(r[o]+r[o+1]*256);return i}function j(e,t,n){if(e%1!==0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function ne(e,t,n,r,i,o){if(!g.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(n+r>e.length)throw new RangeError("Index out of range")}function _i(e,t,n,r,i){Ri(t,r,i,e,n,7);let o=Number(t&BigInt(4294967295));e[n++]=o,o=o>>8,e[n++]=o,o=o>>8,e[n++]=o,o=o>>8,e[n++]=o;let s=Number(t>>BigInt(32)&BigInt(4294967295));return e[n++]=s,s=s>>8,e[n++]=s,s=s>>8,e[n++]=s,s=s>>8,e[n++]=s,n}function Ei(e,t,n,r,i){Ri(t,r,i,e,n,7);let o=Number(t&BigInt(4294967295));e[n+7]=o,o=o>>8,e[n+6]=o,o=o>>8,e[n+5]=o,o=o>>8,e[n+4]=o;let s=Number(t>>BigInt(32)&BigInt(4294967295));return e[n+3]=s,s=s>>8,e[n+2]=s,s=s>>8,e[n+1]=s,s=s>>8,e[n]=s,n+8}function xi(e,t,n,r,i,o){if(n+r>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function Ti(e,t,n,r,i){return t=+t,n=n>>>0,i||xi(e,t,n,4,34028234663852886e22,-34028234663852886e22),Nn(e,t,n,r,23,4),n+4}function Ai(e,t,n,r,i){return t=+t,n=n>>>0,i||xi(e,t,n,8,17976931348623157e292,-17976931348623157e292),Nn(e,t,n,r,52,8),n+8}function Pn(e,t,n){nt[e]=class extends n{constructor(){super(),Object.defineProperty(this,"message",{value:t.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${e}]`,this.stack,delete this.name}get code(){return e}set code(i){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:i,writable:!0})}toString(){return`${this.name} [${e}]: ${this.message}`}}}function pi(e){let t="",n=e.length,r=e[0]==="-"?1:0;for(;n>=r+4;n-=3)t=`_${e.slice(n-3,n)}${t}`;return`${e.slice(0,n)}${t}`}function Cl(e,t,n){rt(t,"offset"),(e[t]===void 0||e[t+n]===void 0)&&St(t,e.length-(n+1))}function Ri(e,t,n,r,i,o){if(e>n||e<t){let s=typeof t=="bigint"?"n":"",l;throw o>3?t===0||t===BigInt(0)?l=`>= 0${s} and < 2${s} ** ${(o+1)*8}${s}`:l=`>= -(2${s} ** ${(o+1)*8-1}${s}) and < 2 ** ${(o+1)*8-1}${s}`:l=`>= ${t}${s} and <= ${n}${s}`,new nt.ERR_OUT_OF_RANGE("value",l,e)}Cl(r,i,o)}function rt(e,t){if(typeof e!="number")throw new nt.ERR_INVALID_ARG_TYPE(t,"number",e)}function St(e,t,n){throw Math.floor(e)!==e?(rt(e,n),new nt.ERR_OUT_OF_RANGE(n||"offset","an integer",e)):t<0?new nt.ERR_BUFFER_OUT_OF_BOUNDS:new nt.ERR_OUT_OF_RANGE(n||"offset",`>= ${n?1:0} and <= ${t}`,e)}function $l(e){if(e=e.split("=")[0],e=e.trim().replace(Ll,""),e.length<2)return"";for(;e.length%4!==0;)e=e+"=";return e}function Cn(e,t){t=t||1/0;let n,r=e.length,i=null,o=[];for(let s=0;s<r;++s){if(n=e.charCodeAt(s),n>55295&&n<57344){if(!i){if(n>56319){(t-=3)>-1&&o.push(239,191,189);continue}else if(s+1===r){(t-=3)>-1&&o.push(239,191,189);continue}i=n;continue}if(n<56320){(t-=3)>-1&&o.push(239,191,189),i=n;continue}n=(i-55296<<10|n-56320)+65536}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,n<128){if((t-=1)<0)break;o.push(n)}else if(n<2048){if((t-=2)<0)break;o.push(n>>6|192,n&63|128)}else if(n<65536){if((t-=3)<0)break;o.push(n>>12|224,n>>6&63|128,n&63|128)}else if(n<1114112){if((t-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,n&63|128)}else throw new Error("Invalid code point")}return o}function Pl(e){let t=[];for(let n=0;n<e.length;++n)t.push(e.charCodeAt(n)&255);return t}function Fl(e,t){let n,r,i,o=[];for(let s=0;s<e.length&&!((t-=2)<0);++s)n=e.charCodeAt(s),r=n>>8,i=n%256,o.push(i),o.push(r);return o}function vi(e){return ui($l(e))}function jt(e,t,n,r){let i;for(i=0;i<r&&!(i+n>=t.length||i>=e.length);++i)t[i+n]=e[i];return i}var pl,kn,gi,nt,Ll,Bl,ke=M(()=>{"use strict";T();fi();di();pl={INSPECT_MAX_BYTES:50},kn=2147483647;g.TYPED_ARRAY_SUPPORT=!0;Object.defineProperty(g.prototype,"parent",{enumerable:!0,get:function(){if(g.isBuffer(this))return this.buffer}});Object.defineProperty(g.prototype,"offset",{enumerable:!0,get:function(){if(g.isBuffer(this))return this.byteOffset}});g.poolSize=8192;g.from=function(e,t,n){return mi(e,t,n)};Object.setPrototypeOf(g.prototype,Uint8Array.prototype);Object.setPrototypeOf(g,Uint8Array);g.alloc=function(e,t,n){return ml(e,t,n)};g.allocUnsafe=function(e){return Ln(e)};g.allocUnsafeSlow=function(e){return Ln(e)};g.isBuffer=function(t){return t!=null&&t._isBuffer===!0&&t!==g.prototype};g.compare=function(t,n){if(t instanceof Uint8Array&&(t=g.from(t,t.offset,t.byteLength)),n instanceof Uint8Array&&(n=g.from(n,n.offset,n.byteLength)),!g.isBuffer(t)||!g.isBuffer(n))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===n)return 0;let r=t.length,i=n.length;for(let o=0,s=Math.min(r,i);o<s;++o)if(t[o]!==n[o]){r=t[o],i=n[o];break}return r<i?-1:i<r?1:0};g.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}};g.concat=function(t,n){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(t.length===0)return g.alloc(0);let r;if(n===void 0)for(n=0,r=0;r<t.length;++r)n+=t[r].length;let i=g.allocUnsafe(n),o=0;for(r=0;r<t.length;++r){let s=t[r];if(s instanceof Uint8Array)o+s.length>i.length?(g.isBuffer(s)||(s=g.from(s.buffer,s.byteOffset,s.byteLength)),s.copy(i,o)):Uint8Array.prototype.set.call(i,s,o);else if(g.isBuffer(s))s.copy(i,o);else throw new TypeError('"list" argument must be an Array of Buffers');o+=s.length}return i};g.byteLength=bi;g.prototype._isBuffer=!0;g.prototype.swap16=function(){let t=this.length;if(t%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let n=0;n<t;n+=2)De(this,n,n+1);return this};g.prototype.swap32=function(){let t=this.length;if(t%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let n=0;n<t;n+=4)De(this,n,n+3),De(this,n+1,n+2);return this};g.prototype.swap64=function(){let t=this.length;if(t%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let n=0;n<t;n+=8)De(this,n,n+7),De(this,n+1,n+6),De(this,n+2,n+5),De(this,n+3,n+4);return this};g.prototype.toString=function(){let t=this.length;return t===0?"":arguments.length===0?wi(this,0,t):wl.apply(this,arguments)};g.prototype.toLocaleString=g.prototype.toString;g.prototype.equals=function(t){if(!g.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t?!0:g.compare(this,t)===0};g.prototype.inspect=function(){let t="",n=pl.INSPECT_MAX_BYTES;return t=this.toString("hex",0,n).replace(/(.{2})/g,"$1 ").trim(),this.length>n&&(t+=" ... "),"<Buffer "+t+">"};g.prototype[Symbol.for("nodejs.util.inspect.custom")]=g.prototype.inspect;g.prototype.compare=function(t,n,r,i,o){if(t instanceof Uint8Array&&(t=g.from(t,t.offset,t.byteLength)),!g.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(n===void 0&&(n=0),r===void 0&&(r=t?t.length:0),i===void 0&&(i=0),o===void 0&&(o=this.length),n<0||r>t.length||i<0||o>this.length)throw new RangeError("out of range index");if(i>=o&&n>=r)return 0;if(i>=o)return-1;if(n>=r)return 1;if(n>>>=0,r>>>=0,i>>>=0,o>>>=0,this===t)return 0;let s=o-i,l=r-n,c=Math.min(s,l),f=this.slice(i,o),h=t.slice(n,r);for(let a=0;a<c;++a)if(f[a]!==h[a]){s=f[a],l=h[a];break}return s<l?-1:l<s?1:0};g.prototype.includes=function(t,n,r){return this.indexOf(t,n,r)!==-1};g.prototype.indexOf=function(t,n,r){return Si(this,t,n,r,!0)};g.prototype.lastIndexOf=function(t,n,r){return Si(this,t,n,r,!1)};g.prototype.write=function(t,n,r,i){if(n===void 0)i="utf8",r=this.length,n=0;else if(r===void 0&&typeof n=="string")i=n,r=this.length,n=0;else if(isFinite(n))n=n>>>0,isFinite(r)?(r=r>>>0,i===void 0&&(i="utf8")):(i=r,r=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");let o=this.length-n;if((r===void 0||r>o)&&(r=o),t.length>0&&(r<0||n<0)||n>this.length)throw new RangeError("Attempt to write outside buffer bounds");i||(i="utf8");let s=!1;for(;;)switch(i){case"hex":return _l(this,t,n,r);case"utf8":case"utf-8":return El(this,t,n,r);case"ascii":case"latin1":case"binary":return xl(this,t,n,r);case"base64":return Tl(this,t,n,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return Al(this,t,n,r);default:if(s)throw new TypeError("Unknown encoding: "+i);i=(""+i).toLowerCase(),s=!0}};g.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};gi=4096;g.prototype.slice=function(t,n){let r=this.length;t=~~t,n=n===void 0?r:~~n,t<0?(t+=r,t<0&&(t=0)):t>r&&(t=r),n<0?(n+=r,n<0&&(n=0)):n>r&&(n=r),n<t&&(n=t);let i=this.subarray(t,n);return Object.setPrototypeOf(i,g.prototype),i};g.prototype.readUintLE=g.prototype.readUIntLE=function(t,n,r){t=t>>>0,n=n>>>0,r||j(t,n,this.length);let i=this[t],o=1,s=0;for(;++s<n&&(o*=256);)i+=this[t+s]*o;return i};g.prototype.readUintBE=g.prototype.readUIntBE=function(t,n,r){t=t>>>0,n=n>>>0,r||j(t,n,this.length);let i=this[t+--n],o=1;for(;n>0&&(o*=256);)i+=this[t+--n]*o;return i};g.prototype.readUint8=g.prototype.readUInt8=function(t,n){return t=t>>>0,n||j(t,1,this.length),this[t]};g.prototype.readUint16LE=g.prototype.readUInt16LE=function(t,n){return t=t>>>0,n||j(t,2,this.length),this[t]|this[t+1]<<8};g.prototype.readUint16BE=g.prototype.readUInt16BE=function(t,n){return t=t>>>0,n||j(t,2,this.length),this[t]<<8|this[t+1]};g.prototype.readUint32LE=g.prototype.readUInt32LE=function(t,n){return t=t>>>0,n||j(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+this[t+3]*16777216};g.prototype.readUint32BE=g.prototype.readUInt32BE=function(t,n){return t=t>>>0,n||j(t,4,this.length),this[t]*16777216+(this[t+1]<<16|this[t+2]<<8|this[t+3])};g.prototype.readBigUInt64LE=function(t){t=t>>>0,rt(t,"offset");let n=this[t],r=this[t+7];(n===void 0||r===void 0)&&St(t,this.length-8);let i=n+this[++t]*2**8+this[++t]*2**16+this[++t]*2**24,o=this[++t]+this[++t]*2**8+this[++t]*2**16+r*2**24;return BigInt(i)+(BigInt(o)<<BigInt(32))};g.prototype.readBigUInt64BE=function(t){t=t>>>0,rt(t,"offset");let n=this[t],r=this[t+7];(n===void 0||r===void 0)&&St(t,this.length-8);let i=n*2**24+this[++t]*2**16+this[++t]*2**8+this[++t],o=this[++t]*2**24+this[++t]*2**16+this[++t]*2**8+r;return(BigInt(i)<<BigInt(32))+BigInt(o)};g.prototype.readIntLE=function(t,n,r){t=t>>>0,n=n>>>0,r||j(t,n,this.length);let i=this[t],o=1,s=0;for(;++s<n&&(o*=256);)i+=this[t+s]*o;return o*=128,i>=o&&(i-=Math.pow(2,8*n)),i};g.prototype.readIntBE=function(t,n,r){t=t>>>0,n=n>>>0,r||j(t,n,this.length);let i=n,o=1,s=this[t+--i];for(;i>0&&(o*=256);)s+=this[t+--i]*o;return o*=128,s>=o&&(s-=Math.pow(2,8*n)),s};g.prototype.readInt8=function(t,n){return t=t>>>0,n||j(t,1,this.length),this[t]&128?(255-this[t]+1)*-1:this[t]};g.prototype.readInt16LE=function(t,n){t=t>>>0,n||j(t,2,this.length);let r=this[t]|this[t+1]<<8;return r&32768?r|4294901760:r};g.prototype.readInt16BE=function(t,n){t=t>>>0,n||j(t,2,this.length);let r=this[t+1]|this[t]<<8;return r&32768?r|4294901760:r};g.prototype.readInt32LE=function(t,n){return t=t>>>0,n||j(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24};g.prototype.readInt32BE=function(t,n){return t=t>>>0,n||j(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]};g.prototype.readBigInt64LE=function(t){t=t>>>0,rt(t,"offset");let n=this[t],r=this[t+7];(n===void 0||r===void 0)&&St(t,this.length-8);let i=this[t+4]+this[t+5]*2**8+this[t+6]*2**16+(r<<24);return(BigInt(i)<<BigInt(32))+BigInt(n+this[++t]*2**8+this[++t]*2**16+this[++t]*2**24)};g.prototype.readBigInt64BE=function(t){t=t>>>0,rt(t,"offset");let n=this[t],r=this[t+7];(n===void 0||r===void 0)&&St(t,this.length-8);let i=(n<<24)+this[++t]*2**16+this[++t]*2**8+this[++t];return(BigInt(i)<<BigInt(32))+BigInt(this[++t]*2**24+this[++t]*2**16+this[++t]*2**8+r)};g.prototype.readFloatLE=function(t,n){return t=t>>>0,n||j(t,4,this.length),bt(this,t,!0,23,4)};g.prototype.readFloatBE=function(t,n){return t=t>>>0,n||j(t,4,this.length),bt(this,t,!1,23,4)};g.prototype.readDoubleLE=function(t,n){return t=t>>>0,n||j(t,8,this.length),bt(this,t,!0,52,8)};g.prototype.readDoubleBE=function(t,n){return t=t>>>0,n||j(t,8,this.length),bt(this,t,!1,52,8)};g.prototype.writeUintLE=g.prototype.writeUIntLE=function(t,n,r,i){if(t=+t,n=n>>>0,r=r>>>0,!i){let l=Math.pow(2,8*r)-1;ne(this,t,n,r,l,0)}let o=1,s=0;for(this[n]=t&255;++s<r&&(o*=256);)this[n+s]=t/o&255;return n+r};g.prototype.writeUintBE=g.prototype.writeUIntBE=function(t,n,r,i){if(t=+t,n=n>>>0,r=r>>>0,!i){let l=Math.pow(2,8*r)-1;ne(this,t,n,r,l,0)}let o=r-1,s=1;for(this[n+o]=t&255;--o>=0&&(s*=256);)this[n+o]=t/s&255;return n+r};g.prototype.writeUint8=g.prototype.writeUInt8=function(t,n,r){return t=+t,n=n>>>0,r||ne(this,t,n,1,255,0),this[n]=t&255,n+1};g.prototype.writeUint16LE=g.prototype.writeUInt16LE=function(t,n,r){return t=+t,n=n>>>0,r||ne(this,t,n,2,65535,0),this[n]=t&255,this[n+1]=t>>>8,n+2};g.prototype.writeUint16BE=g.prototype.writeUInt16BE=function(t,n,r){return t=+t,n=n>>>0,r||ne(this,t,n,2,65535,0),this[n]=t>>>8,this[n+1]=t&255,n+2};g.prototype.writeUint32LE=g.prototype.writeUInt32LE=function(t,n,r){return t=+t,n=n>>>0,r||ne(this,t,n,4,4294967295,0),this[n+3]=t>>>24,this[n+2]=t>>>16,this[n+1]=t>>>8,this[n]=t&255,n+4};g.prototype.writeUint32BE=g.prototype.writeUInt32BE=function(t,n,r){return t=+t,n=n>>>0,r||ne(this,t,n,4,4294967295,0),this[n]=t>>>24,this[n+1]=t>>>16,this[n+2]=t>>>8,this[n+3]=t&255,n+4};g.prototype.writeBigUInt64LE=function(t,n=0){return _i(this,t,n,BigInt(0),BigInt("0xffffffffffffffff"))};g.prototype.writeBigUInt64BE=function(t,n=0){return Ei(this,t,n,BigInt(0),BigInt("0xffffffffffffffff"))};g.prototype.writeIntLE=function(t,n,r,i){if(t=+t,n=n>>>0,!i){let c=Math.pow(2,8*r-1);ne(this,t,n,r,c-1,-c)}let o=0,s=1,l=0;for(this[n]=t&255;++o<r&&(s*=256);)t<0&&l===0&&this[n+o-1]!==0&&(l=1),this[n+o]=(t/s>>0)-l&255;return n+r};g.prototype.writeIntBE=function(t,n,r,i){if(t=+t,n=n>>>0,!i){let c=Math.pow(2,8*r-1);ne(this,t,n,r,c-1,-c)}let o=r-1,s=1,l=0;for(this[n+o]=t&255;--o>=0&&(s*=256);)t<0&&l===0&&this[n+o+1]!==0&&(l=1),this[n+o]=(t/s>>0)-l&255;return n+r};g.prototype.writeInt8=function(t,n,r){return t=+t,n=n>>>0,r||ne(this,t,n,1,127,-128),t<0&&(t=255+t+1),this[n]=t&255,n+1};g.prototype.writeInt16LE=function(t,n,r){return t=+t,n=n>>>0,r||ne(this,t,n,2,32767,-32768),this[n]=t&255,this[n+1]=t>>>8,n+2};g.prototype.writeInt16BE=function(t,n,r){return t=+t,n=n>>>0,r||ne(this,t,n,2,32767,-32768),this[n]=t>>>8,this[n+1]=t&255,n+2};g.prototype.writeInt32LE=function(t,n,r){return t=+t,n=n>>>0,r||ne(this,t,n,4,2147483647,-2147483648),this[n]=t&255,this[n+1]=t>>>8,this[n+2]=t>>>16,this[n+3]=t>>>24,n+4};g.prototype.writeInt32BE=function(t,n,r){return t=+t,n=n>>>0,r||ne(this,t,n,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[n]=t>>>24,this[n+1]=t>>>16,this[n+2]=t>>>8,this[n+3]=t&255,n+4};g.prototype.writeBigInt64LE=function(t,n=0){return _i(this,t,n,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))};g.prototype.writeBigInt64BE=function(t,n=0){return Ei(this,t,n,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))};g.prototype.writeFloatLE=function(t,n,r){return Ti(this,t,n,!0,r)};g.prototype.writeFloatBE=function(t,n,r){return Ti(this,t,n,!1,r)};g.prototype.writeDoubleLE=function(t,n,r){return Ai(this,t,n,!0,r)};g.prototype.writeDoubleBE=function(t,n,r){return Ai(this,t,n,!1,r)};g.prototype.copy=function(t,n,r,i){if(!g.isBuffer(t))throw new TypeError("argument should be a Buffer");if(r||(r=0),!i&&i!==0&&(i=this.length),n>=t.length&&(n=t.length),n||(n=0),i>0&&i<r&&(i=r),i===r||t.length===0||this.length===0)return 0;if(n<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("Index out of range");if(i<0)throw new RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),t.length-n<i-r&&(i=t.length-n+r);let o=i-r;return this===t?this.copyWithin(n,r,i):Uint8Array.prototype.set.call(t,this.subarray(r,i),n),o};g.prototype.fill=function(t,n,r,i){if(typeof t=="string"){if(typeof n=="string"?(i=n,n=0,r=this.length):typeof r=="string"&&(i=r,r=this.length),i!==void 0&&typeof i!="string")throw new TypeError("encoding must be a string");if(typeof i=="string"&&!g.isEncoding(i))throw new TypeError("Unknown encoding: "+i);if(t.length===1){let s=t.charCodeAt(0);(i==="utf8"&&s<128||i==="latin1")&&(t=s)}}else typeof t=="number"?t=t&255:typeof t=="boolean"&&(t=Number(t));if(n<0||this.length<n||this.length<r)throw new RangeError("Out of range index");if(r<=n)return this;n=n>>>0,r=r===void 0?this.length:r>>>0,t||(t=0);let o;if(typeof t=="number")for(o=n;o<r;++o)this[o]=t;else{let s=g.isBuffer(t)?t:g.from(t,i),l=s.length;if(l===0)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(o=0;o<r-n;++o)this[o+n]=s[o%l]}return this};nt={};Pn("ERR_BUFFER_OUT_OF_BOUNDS",function(e){return e?`${e} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError);Pn("ERR_INVALID_ARG_TYPE",function(e,t){return`The "${e}" argument must be of type number. Received type ${typeof t}`},TypeError);Pn("ERR_OUT_OF_RANGE",function(e,t,n){let r=`The value of "${e}" is out of range.`,i=n;return Number.isInteger(n)&&Math.abs(n)>2**32?i=pi(String(n)):typeof n=="bigint"&&(i=String(n),(n>BigInt(2)**BigInt(32)||n<-(BigInt(2)**BigInt(32)))&&(i=pi(i)),i+="n"),r+=` It must be ${t}. Received ${i}`,r},RangeError);Ll=/[^+/0-9A-Za-z-_]/g;Bl=function(){let e="0123456789abcdef",t=new Array(256);for(let n=0;n<16;++n){let r=n*16;for(let i=0;i<16;++i)t[r+i]=e[n]+e[i]}return t}()});function Ul(e,...t){Script.nextTick(e,...t)}function _e(){}function ra(e){throw new Error("process.binding is not supported")}function ia(){return Process.platform==="windows"?"C:\\":"/"}function oa(e){throw new Error("process.chdir is not supported")}function sa(){return 0}function la(){let e=Process.platform;return e==="windows"?"win32":e}var Dl,Il,Gl,jl,Hl,Wl,zl,Vl,Yl,ql,Jl,Kl,Xl,Zl,Ql,ea,ta,na,y,re=M(()=>{"use strict";T();Dl="Frida",Il=!1,Gl=la(),jl=Process.id,Hl={FRIDA_COMPILE:"1"},Wl=[],zl=Frida.version,Vl={};Yl=_e,ql=_e,Jl=_e,Kl=_e,Xl=_e,Zl=_e,Ql=_e,ea=_e,ta=_e,na=function(e){return[]};y={nextTick:Ul,title:Dl,browser:Il,platform:Gl,pid:jl,env:Hl,argv:Wl,version:zl,versions:Vl,on:Yl,addListener:ql,once:Jl,off:Kl,removeListener:Xl,removeAllListeners:Zl,emit:Ql,prependListener:ea,prependOnceListener:ta,listeners:na,binding:ra,cwd:ia,chdir:oa,umask:sa}});var T=M(()=>{"use strict";re()});var Tt={};tt(Tt,{EOL:()=>Wi,arch:()=>Gi,cpus:()=>Fi,default:()=>fa,endianness:()=>Mi,freemem:()=>$i,getNetworkInterfaces:()=>Ii,homedir:()=>zi,hostname:()=>Oi,loadavg:()=>Ci,networkInterfaces:()=>Di,platform:()=>ji,release:()=>Ui,tmpdir:()=>Hi,totalmem:()=>Pi,type:()=>Bi,uptime:()=>Li});function Mi(){let e=Memory.alloc(4);return e.writeU32(1),e.readU8()===1?"LE":"BE"}function Oi(){return""}function Ci(){return[0,0,0]}function Li(){return 0}function $i(){return Number.MAX_VALUE}function Pi(){return Number.MAX_VALUE}function Fi(){return[]}function Bi(){let e=Process.platform;return e==="windows"?"Windows_NT":e[0].toUpperCase()+e.substr(1)}function Ui(){return""}function Di(){return{}}function Ii(){return{}}function Gi(){return Process.arch}function ji(){let e=Process.platform;return e==="windows"?"win32":e}function Hi(){return Process.getTmpDir()}function zi(){return Process.getHomeDir()}var Wi,fa,At=M(()=>{"use strict";T();Wi=Process.platform==="windows"?`\r
`:`
`;fa={endianness:Mi,hostname:Oi,loadavg:Ci,uptime:Li,freemem:$i,totalmem:Pi,cpus:Fi,type:Bi,release:Ui,networkInterfaces:Di,getNetworkInterfaces:Ii,arch:Gi,platform:ji,tmpdir:Hi,EOL:Wi,homedir:zi}});var Ji={};tt(Ji,{basename:()=>Aa,default:()=>Dn,delimiter:()=>Ma,dirname:()=>Ta,extname:()=>Ra,format:()=>va,isAbsolute:()=>wa,join:()=>_a,normalize:()=>Sa,parse:()=>Na,posix:()=>Ca,relative:()=>Ea,resolve:()=>ba,sep:()=>ka,toNamespacedPath:()=>xa,win32:()=>Oa});function v(e){return e===W||e===ie}function Un(e){return e===W}function Oe(e){return e>=da&&e<=ga||e>=ha&&e<=pa}function Wt(e,t,n,r){let i="",o=0,s=-1,l=0,c=0;for(let f=0;f<=e.length;++f){if(f<e.length)c=e.charCodeAt(f);else{if(r(c))break;c=W}if(r(c)){if(!(s===f-1||l===1))if(l===2){if(i.length<2||o!==2||i.charCodeAt(i.length-1)!==Le||i.charCodeAt(i.length-2)!==Le){if(i.length>2){let h=i.lastIndexOf(n);h===-1?(i="",o=0):(i=i.slice(0,h),o=i.length-1-i.lastIndexOf(n)),s=f,l=0;continue}else if(i.length!==0){i="",o=0,s=f,l=0;continue}}t&&(i+=i.length>0?`${n}..`:"..",o=2)}else i.length>0?i+=`${n}${e.slice(s+1,f)}`:i=e.slice(s+1,f),o=f-s-1;s=f,l=0}else c===Le&&l!==-1?++l:l=-1}return i}function Yi(e,t){let n=t.dir||t.root,r=t.base||`${t.name||""}${t.ext||""}`;return n?n===t.root?`${n}${r}`:`${n}${e}${r}`:r}var da,ha,ga,pa,Le,W,ie,Me,ma,Vi,Ce,ya,Ie,qi,Dn,ba,Sa,wa,_a,Ea,xa,Ta,Aa,Ra,va,Na,ka,Ma,Oa,Ca,In=M(()=>{"use strict";T();re();da=65,ha=97,ga=90,pa=122,Le=46,W=47,ie=92,Me=58,ma=63,Vi=y.platform==="win32";Ce={resolve(...e){let t="",n="",r=!1;for(let i=e.length-1;i>=-1;i--){let o;if(i>=0){if(o=e[i],o.length===0)continue}else t.length===0?o=y.cwd():(o=y.env[`=${t}`]||y.cwd(),(o===void 0||o.slice(0,2).toLowerCase()!==t.toLowerCase()&&o.charCodeAt(2)===ie)&&(o=`${t}\\`));let s=o.length,l=0,c="",f=!1,h=o.charCodeAt(0);if(s===1)v(h)&&(l=1,f=!0);else if(v(h))if(f=!0,v(o.charCodeAt(1))){let a=2,u=a;for(;a<s&&!v(o.charCodeAt(a));)a++;if(a<s&&a!==u){let d=o.slice(u,a);for(u=a;a<s&&v(o.charCodeAt(a));)a++;if(a<s&&a!==u){for(u=a;a<s&&!v(o.charCodeAt(a));)a++;(a===s||a!==u)&&(c=`\\\\${d}\\${o.slice(u,a)}`,l=a)}}}else l=1;else Oe(h)&&o.charCodeAt(1)===Me&&(c=o.slice(0,2),l=2,s>2&&v(o.charCodeAt(2))&&(f=!0,l=3));if(c.length>0)if(t.length>0){if(c.toLowerCase()!==t.toLowerCase())continue}else t=c;if(r){if(t.length>0)break}else if(n=`${o.slice(l)}\\${n}`,r=f,f&&t.length>0)break}return n=Wt(n,!r,"\\",v),r?`${t}\\${n}`:`${t}${n}`||"."},normalize(e){let t=e.length;if(t===0)return".";let n=0,r,i=!1,o=e.charCodeAt(0);if(t===1)return Un(o)?"\\":e;if(v(o))if(i=!0,v(e.charCodeAt(1))){let l=2,c=l;for(;l<t&&!v(e.charCodeAt(l));)l++;if(l<t&&l!==c){let f=e.slice(c,l);for(c=l;l<t&&v(e.charCodeAt(l));)l++;if(l<t&&l!==c){for(c=l;l<t&&!v(e.charCodeAt(l));)l++;if(l===t)return`\\\\${f}\\${e.slice(c)}\\`;l!==c&&(r=`\\\\${f}\\${e.slice(c,l)}`,n=l)}}}else n=1;else Oe(o)&&e.charCodeAt(1)===Me&&(r=e.slice(0,2),n=2,t>2&&v(e.charCodeAt(2))&&(i=!0,n=3));let s=n<t?Wt(e.slice(n),!i,"\\",v):"";return s.length===0&&!i&&(s="."),s.length>0&&v(e.charCodeAt(t-1))&&(s+="\\"),r===void 0?i?`\\${s}`:s:i?`${r}\\${s}`:`${r}${s}`},isAbsolute(e){let t=e.length;if(t===0)return!1;let n=e.charCodeAt(0);return v(n)||t>2&&Oe(n)&&e.charCodeAt(1)===Me&&v(e.charCodeAt(2))},join(...e){if(e.length===0)return".";let t,n;for(let o=0;o<e.length;++o){let s=e[o];s.length>0&&(t===void 0?t=n=s:t+=`\\${s}`)}if(t===void 0)return".";let r=!0,i=0;if(v(n.charCodeAt(0))){++i;let o=n.length;o>1&&v(n.charCodeAt(1))&&(++i,o>2&&(v(n.charCodeAt(2))?++i:r=!1))}if(r){for(;i<t.length&&v(t.charCodeAt(i));)i++;i>=2&&(t=`\\${t.slice(i)}`)}return Ce.normalize(t)},relative(e,t){if(e===t)return"";let n=Ce.resolve(e),r=Ce.resolve(t);if(n===r||(e=n.toLowerCase(),t=r.toLowerCase(),e===t))return"";let i=0;for(;i<e.length&&e.charCodeAt(i)===ie;)i++;let o=e.length;for(;o-1>i&&e.charCodeAt(o-1)===ie;)o--;let s=o-i,l=0;for(;l<t.length&&t.charCodeAt(l)===ie;)l++;let c=t.length;for(;c-1>l&&t.charCodeAt(c-1)===ie;)c--;let f=c-l,h=s<f?s:f,a=-1,u=0;for(;u<h;u++){let p=e.charCodeAt(i+u);if(p!==t.charCodeAt(l+u))break;p===ie&&(a=u)}if(u!==h){if(a===-1)return r}else{if(f>h){if(t.charCodeAt(l+u)===ie)return r.slice(l+u+1);if(u===2)return r.slice(l+u)}s>h&&(e.charCodeAt(i+u)===ie?a=u:u===2&&(a=3)),a===-1&&(a=0)}let d="";for(u=i+a+1;u<=o;++u)(u===o||e.charCodeAt(u)===ie)&&(d+=d.length===0?"..":"\\..");return l+=a,d.length>0?`${d}${r.slice(l,c)}`:(r.charCodeAt(l)===ie&&++l,r.slice(l,c))},toNamespacedPath(e){if(typeof e!="string"||e.length===0)return e;let t=Ce.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===ie){if(t.charCodeAt(1)===ie){let n=t.charCodeAt(2);if(n!==ma&&n!==Le)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(Oe(t.charCodeAt(0))&&t.charCodeAt(1)===Me&&t.charCodeAt(2)===ie)return`\\\\?\\${t}`;return e},dirname(e){let t=e.length;if(t===0)return".";let n=-1,r=0,i=e.charCodeAt(0);if(t===1)return v(i)?e:".";if(v(i)){if(n=r=1,v(e.charCodeAt(1))){let l=2,c=l;for(;l<t&&!v(e.charCodeAt(l));)l++;if(l<t&&l!==c){for(c=l;l<t&&v(e.charCodeAt(l));)l++;if(l<t&&l!==c){for(c=l;l<t&&!v(e.charCodeAt(l));)l++;if(l===t)return e;l!==c&&(n=r=l+1)}}}}else Oe(i)&&e.charCodeAt(1)===Me&&(n=t>2&&v(e.charCodeAt(2))?3:2,r=n);let o=-1,s=!0;for(let l=t-1;l>=r;--l)if(v(e.charCodeAt(l))){if(!s){o=l;break}}else s=!1;if(o===-1){if(n===-1)return".";o=n}return e.slice(0,o)},basename(e,t){let n=0,r=-1,i=!0;if(e.length>=2&&Oe(e.charCodeAt(0))&&e.charCodeAt(1)===Me&&(n=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,s=-1;for(let l=e.length-1;l>=n;--l){let c=e.charCodeAt(l);if(v(c)){if(!i){n=l+1;break}}else s===-1&&(i=!1,s=l+1),o>=0&&(c===t.charCodeAt(o)?--o===-1&&(r=l):(o=-1,r=s))}return n===r?r=s:r===-1&&(r=e.length),e.slice(n,r)}for(let o=e.length-1;o>=n;--o)if(v(e.charCodeAt(o))){if(!i){n=o+1;break}}else r===-1&&(i=!1,r=o+1);return r===-1?"":e.slice(n,r)},extname(e){let t=0,n=-1,r=0,i=-1,o=!0,s=0;e.length>=2&&e.charCodeAt(1)===Me&&Oe(e.charCodeAt(0))&&(t=r=2);for(let l=e.length-1;l>=t;--l){let c=e.charCodeAt(l);if(v(c)){if(!o){r=l+1;break}continue}i===-1&&(o=!1,i=l+1),c===Le?n===-1?n=l:s!==1&&(s=1):n!==-1&&(s=-1)}return n===-1||i===-1||s===0||s===1&&n===i-1&&n===r+1?"":e.slice(n,i)},format:Yi.bind(null,"\\"),parse(e){let t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;let n=e.length,r=0,i=e.charCodeAt(0);if(n===1)return v(i)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(v(i)){if(r=1,v(e.charCodeAt(1))){let a=2,u=a;for(;a<n&&!v(e.charCodeAt(a));)a++;if(a<n&&a!==u){for(u=a;a<n&&v(e.charCodeAt(a));)a++;if(a<n&&a!==u){for(u=a;a<n&&!v(e.charCodeAt(a));)a++;a===n?r=a:a!==u&&(r=a+1)}}}}else if(Oe(i)&&e.charCodeAt(1)===Me){if(n<=2)return t.root=t.dir=e,t;if(r=2,v(e.charCodeAt(2))){if(n===3)return t.root=t.dir=e,t;r=3}}r>0&&(t.root=e.slice(0,r));let o=-1,s=r,l=-1,c=!0,f=e.length-1,h=0;for(;f>=r;--f){if(i=e.charCodeAt(f),v(i)){if(!c){s=f+1;break}continue}l===-1&&(c=!1,l=f+1),i===Le?o===-1?o=f:h!==1&&(h=1):o!==-1&&(h=-1)}return l!==-1&&(o===-1||h===0||h===1&&o===l-1&&o===s+1?t.base=t.name=e.slice(s,l):(t.name=e.slice(s,o),t.base=e.slice(s,l),t.ext=e.slice(o,l))),s>0&&s!==r?t.dir=e.slice(0,s-1):t.dir=t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},ya=(()=>{if(Vi){let e=/\\/g;return()=>{let t=y.cwd().replace(e,"/");return t.slice(t.indexOf("/"))}}return()=>y.cwd()})(),Ie={resolve(...e){let t="",n=!1;for(let r=e.length-1;r>=-1&&!n;r--){let i=r>=0?e[r]:ya();i.length!==0&&(t=`${i}/${t}`,n=i.charCodeAt(0)===W)}return t=Wt(t,!n,"/",Un),n?`/${t}`:t.length>0?t:"."},normalize(e){if(e.length===0)return".";let t=e.charCodeAt(0)===W,n=e.charCodeAt(e.length-1)===W;return e=Wt(e,!t,"/",Un),e.length===0?t?"/":n?"./":".":(n&&(e+="/"),t?`/${e}`:e)},isAbsolute(e){return e.length>0&&e.charCodeAt(0)===W},join(...e){if(e.length===0)return".";let t;for(let n=0;n<e.length;++n){let r=e[n];r.length>0&&(t===void 0?t=r:t+=`/${r}`)}return t===void 0?".":Ie.normalize(t)},relative(e,t){if(e===t||(e=Ie.resolve(e),t=Ie.resolve(t),e===t))return"";let n=1,r=e.length,i=r-n,o=1,s=t.length-o,l=i<s?i:s,c=-1,f=0;for(;f<l;f++){let a=e.charCodeAt(n+f);if(a!==t.charCodeAt(o+f))break;a===W&&(c=f)}if(f===l)if(s>l){if(t.charCodeAt(o+f)===W)return t.slice(o+f+1);if(f===0)return t.slice(o+f)}else i>l&&(e.charCodeAt(n+f)===W?c=f:f===0&&(c=0));let h="";for(f=n+c+1;f<=r;++f)(f===r||e.charCodeAt(f)===W)&&(h+=h.length===0?"..":"/..");return`${h}${t.slice(o+c)}`},toNamespacedPath(e){return e},dirname(e){if(e.length===0)return".";let t=e.charCodeAt(0)===W,n=-1,r=!0;for(let i=e.length-1;i>=1;--i)if(e.charCodeAt(i)===W){if(!r){n=i;break}}else r=!1;return n===-1?t?"/":".":t&&n===1?"//":e.slice(0,n)},basename(e,t){let n=0,r=-1,i=!0;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,s=-1;for(let l=e.length-1;l>=0;--l){let c=e.charCodeAt(l);if(c===W){if(!i){n=l+1;break}}else s===-1&&(i=!1,s=l+1),o>=0&&(c===t.charCodeAt(o)?--o===-1&&(r=l):(o=-1,r=s))}return n===r?r=s:r===-1&&(r=e.length),e.slice(n,r)}for(let o=e.length-1;o>=0;--o)if(e.charCodeAt(o)===W){if(!i){n=o+1;break}}else r===-1&&(i=!1,r=o+1);return r===-1?"":e.slice(n,r)},extname(e){let t=-1,n=0,r=-1,i=!0,o=0;for(let s=e.length-1;s>=0;--s){let l=e.charCodeAt(s);if(l===W){if(!i){n=s+1;break}continue}r===-1&&(i=!1,r=s+1),l===Le?t===-1?t=s:o!==1&&(o=1):t!==-1&&(o=-1)}return t===-1||r===-1||o===0||o===1&&t===r-1&&t===n+1?"":e.slice(t,r)},format:Yi.bind(null,"/"),parse(e){let t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;let n=e.charCodeAt(0)===W,r;n?(t.root="/",r=1):r=0;let i=-1,o=0,s=-1,l=!0,c=e.length-1,f=0;for(;c>=r;--c){let h=e.charCodeAt(c);if(h===W){if(!l){o=c+1;break}continue}s===-1&&(l=!1,s=c+1),h===Le?i===-1?i=c:f!==1&&(f=1):i!==-1&&(f=-1)}if(s!==-1){let h=o===0&&n?1:o;i===-1||f===0||f===1&&i===s-1&&i===o+1?t.base=t.name=e.slice(h,s):(t.name=e.slice(h,i),t.base=e.slice(h,s),t.ext=e.slice(i,s))}return o>0?t.dir=e.slice(0,o-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};Ie.win32=Ce.win32=Ce;Ie.posix=Ce.posix=Ie;qi=Vi?Ce:Ie,Dn=qi,{resolve:ba,normalize:Sa,isAbsolute:wa,join:_a,relative:Ea,toNamespacedPath:xa,dirname:Ta,basename:Aa,extname:Ra,format:va,parse:Na,sep:ka,delimiter:Ma,win32:Oa,posix:Ca}=qi});var jn={};tt(jn,{isAnyArrayBuffer:()=>hc,isArgumentsObject:()=>Ia,isArrayBuffer:()=>Ki,isArrayBufferView:()=>Wa,isAsyncFunction:()=>lc,isBigInt64Array:()=>ec,isBigIntObject:()=>to,isBigUint64Array:()=>tc,isBooleanObject:()=>eo,isBoxedPrimitive:()=>dc,isDataView:()=>sc,isExternal:()=>pc,isFloat32Array:()=>Za,isFloat64Array:()=>Qa,isGeneratorFunction:()=>Ga,isGeneratorObject:()=>uc,isInt16Array:()=>Ka,isInt32Array:()=>Xa,isInt8Array:()=>Ja,isMap:()=>nc,isMapIterator:()=>ac,isModuleNamespaceObject:()=>mc,isNumberObject:()=>Zi,isPromise:()=>Ha,isProxy:()=>gc,isSet:()=>rc,isSetIterator:()=>cc,isSharedArrayBuffer:()=>Xi,isStringObject:()=>Qi,isSymbolObject:()=>no,isTypedArray:()=>ja,isUint16Array:()=>Ya,isUint32Array:()=>qa,isUint8Array:()=>za,isUint8ClampedArray:()=>Va,isWeakMap:()=>ic,isWeakSet:()=>oc,isWebAssemblyCompiledModule:()=>fc});function Ia(e){return e!==null&&typeof e=="object"&&Symbol.toStringTag in e?!1:ae(e)==="[object Arguments]"}function Ga(e){return Object.getPrototypeOf(e)===Ua}function ja(e){return e instanceof Da}function Ha(e){return e instanceof Promise}function Wa(e){return ArrayBuffer.isView(e)}function za(e){return e instanceof Uint8Array}function Va(e){return e instanceof Uint8ClampedArray}function Ya(e){return e instanceof Uint16Array}function qa(e){return e instanceof Uint32Array}function Ja(e){return e instanceof Int8Array}function Ka(e){return e instanceof Int16Array}function Xa(e){return e instanceof Int32Array}function Za(e){return e instanceof Float32Array}function Qa(e){return e instanceof Float64Array}function ec(e){return e instanceof BigInt64Array}function tc(e){return e instanceof BigUint64Array}function nc(e){return ae(e)==="[object Map]"}function rc(e){return ae(e)==="[object Set]"}function ic(e){return ae(e)==="[object WeakMap]"}function oc(e){return ae(e)==="[object WeakSet]"}function Ki(e){return ae(e)==="[object ArrayBuffer]"}function sc(e){return ae(e)==="[object DataView]"}function Xi(e){return ae(e)==="[object SharedArrayBuffer]"}function lc(e){return ae(e)==="[object AsyncFunction]"}function ac(e){return ae(e)==="[object Map Iterator]"}function cc(e){return ae(e)==="[object Set Iterator]"}function uc(e){return ae(e)==="[object Generator]"}function fc(e){return ae(e)==="[object WebAssembly.Module]"}function Zi(e){return Rt(e,La)}function Qi(e){return Rt(e,$a)}function eo(e){return Rt(e,Pa)}function to(e){return Rt(e,Fa)}function no(e){return Rt(e,Ba)}function Rt(e,t){if(typeof e!="object")return!1;try{return t(e),!0}catch{return!1}}function dc(e){return Zi(e)||Qi(e)||eo(e)||to(e)||no(e)}function hc(e){return Ki(e)||Xi(e)}function gc(e){Gn("isProxy")}function pc(e){Gn("isExternal")}function mc(e){Gn("isModuleNamespaceObject")}function Gn(e){throw new Error(`${e} is not supported in userland`)}function it(e){return e.call.bind(e)}var ae,La,$a,Pa,Fa,Ba,Ua,Da,ro=M(()=>{"use strict";T();ae=it(Object.prototype.toString),La=it(Number.prototype.valueOf),$a=it(String.prototype.valueOf),Pa=it(Boolean.prototype.valueOf),Fa=it(BigInt.prototype.valueOf),Ba=it(Symbol.prototype.valueOf),Ua=Object.getPrototypeOf(function*(){}),Da=Object.getPrototypeOf(Int8Array)});function oo(e){if(!qn(e)){let o=[];for(let s=0;s<arguments.length;s++)o.push(D(arguments[s]));return o.join(" ")}let t=1,n=arguments,r=n.length,i=String(e).replace(yc,function(o){if(o==="%%")return"%";if(t>=r)return o;switch(o){case"%s":return String(n[t++]);case"%d":return Number(n[t++]);case"%j":try{return JSON.stringify(n[t++])}catch{return"[Circular]"}default:return o}});for(let o=n[t];t<r;o=n[++t])Yn(o)||!vt(o)?i+=" "+o:i+=" "+D(o);return i}function D(e,t){let n={seen:[],stylize:wc};return arguments.length>=3&&(n.depth=arguments[2]),arguments.length>=4&&(n.colors=arguments[3]),so(t)?n.showHidden=t:t&&vc(n,t),ot(n.showHidden)&&(n.showHidden=!1),ot(n.depth)&&(n.depth=2),ot(n.colors)&&(n.colors=!1),ot(n.customInspect)&&(n.customInspect=!0),n.colors&&(n.stylize=Sc),Yt(n,e,n.depth)}function Sc(e,t){let n=D.styles[t];return n?"\x1B["+D.colors[n][0]+"m"+e+"\x1B["+D.colors[n][1]+"m":e}function wc(e,t){return e}function _c(e){let t={};return e.forEach(function(n,r){t[n]=!0}),t}function Yt(e,t,n){if(e.customInspect&&t&&Wn(t.inspect)&&t.inspect!==D&&!(t.constructor&&t.constructor.prototype===t)){let h=t.inspect(n,e);return qn(h)||(h=Yt(e,h,n)),h}let r=Ec(e,t);if(r)return r;let i=Object.keys(t),o=_c(i);if(e.showHidden&&(i=Object.getOwnPropertyNames(t)),Vt(t)&&(i.indexOf("message")>=0||i.indexOf("description")>=0))return Hn(t);if(i.length===0){if(Wn(t)){let h=t.name?": "+t.name:"";return e.stylize("[Function"+h+"]","special")}if(zt(t))return e.stylize(RegExp.prototype.toString.call(t),"regexp");if(Vn(t))return e.stylize(Date.prototype.toString.call(t),"date");if(Vt(t))return Hn(t)}let s="",l=!1,c=["{","}"];if(Ac(t)&&(l=!0,c=["[","]"]),Wn(t)&&(s=" [Function"+(t.name?": "+t.name:"")+"]"),zt(t)&&(s=" "+RegExp.prototype.toString.call(t)),Vn(t)&&(s=" "+Date.prototype.toUTCString.call(t)),Vt(t)&&(s=" "+Hn(t)),i.length===0&&(!l||t.length==0))return c[0]+s+c[1];if(n<0)return zt(t)?e.stylize(RegExp.prototype.toString.call(t),"regexp"):e.stylize("[Object]","special");e.seen.push(t);let f;return l?f=xc(e,t,n,o,i):f=i.map(function(h){return zn(e,t,n,o,h,l)}),e.seen.pop(),Tc(f,s,c)}function Ec(e,t){if(ot(t))return e.stylize("undefined","undefined");if(qn(t)){let n="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(n,"string")}if(Rc(t))return e.stylize(""+t,"number");if(so(t))return e.stylize(""+t,"boolean");if(Yn(t))return e.stylize("null","null")}function Hn(e){return"["+Error.prototype.toString.call(e)+"]"}function xc(e,t,n,r,i){let o=[];for(let s=0,l=t.length;s<l;++s)lo(t,String(s))?o.push(zn(e,t,n,r,String(s),!0)):o.push("");return i.forEach(function(s){s.match(/^\d+$/)||o.push(zn(e,t,n,r,s,!0))}),o}function zn(e,t,n,r,i,o){let s,l,c;if(c=Object.getOwnPropertyDescriptor(t,i)||{value:t[i]},c.get?c.set?l=e.stylize("[Getter/Setter]","special"):l=e.stylize("[Getter]","special"):c.set&&(l=e.stylize("[Setter]","special")),lo(r,i)||(s="["+i+"]"),l||(e.seen.indexOf(c.value)<0?(Yn(n)?l=Yt(e,c.value,null):l=Yt(e,c.value,n-1),l.indexOf(`
`)>-1&&(o?l=l.split(`
`).map(function(f){return"  "+f}).join(`
`).substr(2):l=`
`+l.split(`
`).map(function(f){return"   "+f}).join(`
`))):l=e.stylize("[Circular]","special")),ot(s)){if(o&&i.match(/^\d+$/))return l;s=JSON.stringify(""+i),s.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(s=s.substr(1,s.length-2),s=e.stylize(s,"name")):(s=s.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),s=e.stylize(s,"string"))}return s+": "+l}function Tc(e,t,n){let r=0;return e.reduce(function(o,s){return r++,s.indexOf(`
`)>=0&&r++,o+s.replace(/\u001b\[\d\d?m/g,"").length+1},0)>60?n[0]+(t===""?"":t+`
 `)+" "+e.join(`,
  `)+" "+n[1]:n[0]+t+" "+e.join(", ")+" "+n[1]}function Ac(e){return Array.isArray(e)}function so(e){return typeof e=="boolean"}function Yn(e){return e===null}function Rc(e){return typeof e=="number"}function qn(e){return typeof e=="string"}function ot(e){return e===void 0}function zt(e){return vt(e)&&Jn(e)==="[object RegExp]"}function vt(e){return typeof e=="object"&&e!==null}function Vn(e){return vt(e)&&Jn(e)==="[object Date]"}function Vt(e){return vt(e)&&(Jn(e)==="[object Error]"||e instanceof Error)}function Wn(e){return typeof e=="function"}function Jn(e){return Object.prototype.toString.call(e)}function vc(e,t){if(!t||!vt(t))return e;let n=Object.keys(t),r=n.length;for(;r--;)e[n[r]]=t[n[r]];return e}function lo(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function qt(e){if(typeof e!="function")throw new TypeError('The "original" argument must be of type Function');if(Ge&&e[Ge]){let n=e[Ge];if(typeof n!="function")throw new TypeError('The "util.promisify.custom" argument must be of type Function');return Object.defineProperty(n,Ge,{value:n,enumerable:!1,writable:!1,configurable:!0}),n}function t(){let n,r,i=new Promise(function(s,l){n=s,r=l}),o=[];for(let s=0;s<arguments.length;s++)o.push(arguments[s]);o.push(function(s,l){s?r(s):n(l)});try{e.apply(this,o)}catch(s){r(s)}return i}return Object.setPrototypeOf(t,Object.getPrototypeOf(e)),Ge&&Object.defineProperty(t,Ge,{value:t,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(t,Object.getOwnPropertyDescriptors(e))}var io,yc,bc,Ge,st=M(()=>{"use strict";T();ro();re();io={...jn,isRegExp:zt,isDate:Vn,isNativeError:Vt},yc=/%[sdj%]/g;bc=/^$/;if(y.env.NODE_DEBUG){let e=y.env.NODE_DEBUG;e=e.replace(/[|\\{}()[\]^$+?.]/g,"\\$&").replace(/\*/g,".*").replace(/,/g,"$|^").toUpperCase(),bc=new RegExp("^"+e+"$","i")}D.custom=Symbol.for("nodejs.util.inspect.custom");D.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]};D.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"};Ge=Symbol("util.promisify.custom");qt.custom=Ge});function je(e,t){if(e&&t&&e!==t){if(Array.isArray(t.errors))return t.errors.push(e),t;let n=new AggregateError([t,e],t.message);return n.code=t.code,n}return e||t}function ao(e,t){return function(...r){let i=new e,o=Nc(t,r,i);return Object.defineProperties(i,{message:{value:o,enumerable:!1,writable:!0,configurable:!0},toString:{value(){return`${this.name} [${t}]: ${this.message}`},enumerable:!1,writable:!0,configurable:!0}}),i.code=t,i}}function G(e,t,n,...r){co.set(e,t),n=ao(n,e),r.length!==0&&r.forEach(i=>{n[i.name]=ao(i,e)}),F[e]=n}function Nc(e,t,n){let r=co.get(e);if(typeof r=="function")return Reflect.apply(r,n,t);let i=(r.match(/%[dfijoOs]/g)||[]).length;return t.length===0?r:(t.unshift(r),Reflect.apply(oo,null,t))}var co,F,oe,se=M(()=>{"use strict";T();st();co=new Map,F={};oe=class extends Error{constructor(){super("The operation was aborted"),this.code="ABORT_ERR",this.name="AbortError"}};G("ERR_EVENT_RECURSION",'The event "%s" is already being dispatched',Error);G("ERR_ILLEGAL_CONSTRUCTOR","Illegal constructor",TypeError);G("ERR_INVALID_ARG_TYPE","Invalid argument type",TypeError);G("ERR_INVALID_ARG_VALUE","Invalid argument value",TypeError,RangeError);G("ERR_INVALID_RETURN_VALUE","Invalid return value",TypeError,RangeError);G("ERR_INVALID_THIS",'Value of "this" must be of type %s',TypeError);G("ERR_METHOD_NOT_IMPLEMENTED","The %s method is not implemented",Error);G("ERR_MISSING_ARGS","Missing argument",TypeError);G("ERR_MULTIPLE_CALLBACK","Callback called multiple times",Error);G("ERR_OUT_OF_RANGE","Out of range",RangeError);G("ERR_STREAM_ALREADY_FINISHED","Cannot call %s after a stream was finished",Error);G("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable",Error);G("ERR_STREAM_DESTROYED","Cannot call %s after a stream was destroyed",Error);G("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError);G("ERR_STREAM_PREMATURE_CLOSE","Premature close",Error);G("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF",Error);G("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event",Error);G("ERR_STREAM_WRITE_AFTER_END","write after end",Error);G("ERR_UNKNOWN_ENCODING","Unknown encoding: %s",TypeError)});function He(e){let t=!1;return function(...n){t||(t=!0,Reflect.apply(e,this,n))}}var Kn=M(()=>{"use strict";T()});function fe(e){return!!(e&&typeof e.pipe=="function"&&typeof e.on=="function"&&(!e._writableState||e._readableState?.readable!==!1)&&(!e._writableState||e._readableState))}function Ee(e){return!!(e&&typeof e.write=="function"&&typeof e.on=="function"&&(!e._readableState||e._writableState?.writable!==!1))}function uo(e){return!!(e&&typeof e.pipe=="function"&&e._readableState&&typeof e.on=="function"&&typeof e.write=="function")}function Z(e){return e&&(e._readableState||e._writableState||typeof e.write=="function"&&typeof e.on=="function"||typeof e.pipe=="function"&&typeof e.on=="function")}function he(e,t){return e==null?!1:t===!0?typeof e[Symbol.asyncIterator]=="function":t===!1?typeof e[Symbol.iterator]=="function":typeof e[Symbol.asyncIterator]=="function"||typeof e[Symbol.iterator]=="function"}function Nt(e){if(!Z(e))return null;let t=e._writableState,n=e._readableState,r=t||n;return!!(e.destroyed||e[Xn]||r?.destroyed)}function Mc(e){if(!Ee(e))return null;if(e.writableEnded===!0)return!0;let t=e._writableState;return t?.errored?!1:typeof t?.ended!="boolean"?null:t.ended}function Zn(e,t){if(!Ee(e))return null;if(e.writableFinished===!0)return!0;let n=e._writableState;return n?.errored?!1:typeof n?.finished!="boolean"?null:!!(n.finished||t===!1&&n.ended===!0&&n.length===0)}function Jt(e,t){if(!fe(e))return null;let n=e._readableState;return n?.errored?!1:typeof n?.endEmitted!="boolean"?null:!!(n.endEmitted||t===!1&&n.ended===!0&&n.length===0)}function xe(e){let t=fe(e);return t===null||typeof e?.readable!="boolean"?null:Nt(e)?!1:t&&e.readable&&!Jt(e)}function Te(e){let t=Ee(e);return t===null||typeof e?.writable!="boolean"?null:Nt(e)?!1:t&&e.writable&&!Mc(e)}function fo(e,t){return Z(e)?Nt(e)?!0:!(t?.readable!==!1&&xe(e)||t?.writable!==!1&&Te(e)):null}function ho(e){if(!Z(e))return null;let t=e._writableState,n=e._readableState;return typeof t?.closed=="boolean"||typeof n?.closed=="boolean"?t?.closed||n?.closed:typeof e._closed=="boolean"&&go(e)?e._closed:null}function go(e){return typeof e._closed=="boolean"&&typeof e._defaultKeepAlive=="boolean"&&typeof e._removedConnection=="boolean"&&typeof e._removedContLen=="boolean"}function Oc(e){return typeof e._sent100=="boolean"&&go(e)}function po(e){return typeof e._consuming=="boolean"&&typeof e._dumped=="boolean"&&e.req?.upgradeOrConnect===void 0}function mo(e){if(!Z(e))return null;let t=e._writableState,n=e._readableState,r=t||n;return!r&&Oc(e)||!!(r&&r.autoDestroy&&r.emitClose&&r.closed===!1)}function Qn(e){return!!(e&&(e.readableDidRead||e.readableAborted||e[kc]))}var Xn,kc,$e=M(()=>{"use strict";T();Xn=Symbol("kDestroyed"),kc=Symbol("kIsDisturbed")});function Cc(e){return e.setHeader&&typeof e.abort=="function"}function Q(e,t,n){arguments.length===2?(n=t,t={}):t==null&&(t={}),n=He(n);let r=t.readable||t.readable!==!1&&fe(e),i=t.writable||t.writable!==!1&&Ee(e);Z(e);let o=e._writableState,s=e._readableState,l=()=>{e.writable||h()},c=mo(e)&&fe(e)===r&&Ee(e)===i,f=Zn(e,!1),h=()=>{f=!0,e.destroyed&&(c=!1),!(c&&(!e.readable||r))&&(!r||a)&&n.call(e)},a=Jt(e,!1),u=()=>{a=!0,e.destroyed&&(c=!1),!(c&&(!e.writable||i))&&(!i||f)&&n.call(e)},d=x=>{n.call(e,x)},p=ho(e),m=()=>{p=!0;let x=o?.errored||s?.errored;if(x&&typeof x!="boolean")return n.call(e,x);if(r&&!a&&!Jt(e,!1))return n.call(e,new yo);if(i&&!f&&!Zn(e,!1))return n.call(e,new yo);n.call(e)},S=()=>{e.req.on("finish",h)};Cc(e)?(e.on("complete",h),c||e.on("abort",m),e.req?S():e.on("request",S)):i&&!o&&(e.on("end",l),e.on("close",l)),!c&&typeof e.aborted=="boolean"&&e.on("aborted",m),e.on("end",u),e.on("finish",h),t.error!==!1&&e.on("error",d),e.on("close",m),p?y.nextTick(m):o?.errorEmitted||s?.errorEmitted?c||y.nextTick(m):!r&&(!c||xe(e))&&(f||!Te(e))?y.nextTick(m):!i&&(!c||Te(e))&&(a||!xe(e))?y.nextTick(m):s&&e.req&&e.aborted&&y.nextTick(m);let _=()=>{n=Lc,e.removeListener("aborted",m),e.removeListener("complete",h),e.removeListener("abort",m),e.removeListener("request",S),e.req&&e.req.removeListener("finish",h),e.removeListener("end",l),e.removeListener("close",l),e.removeListener("finish",h),e.removeListener("end",u),e.removeListener("error",d),e.removeListener("close",m)};if(t.signal&&!p){let x=()=>{let A=n;_(),A.call(e,new oe)};if(t.signal.aborted)y.nextTick(x);else{let A=n;n=He((...O)=>{t.signal.removeEventListener("abort",x),A.apply(e,O)}),t.signal.addEventListener("abort",x)}}return _}var yo,Lc,lt=M(()=>{"use strict";T();se();Kn();$e();re();({ERR_STREAM_PREMATURE_CLOSE:yo}=F);Lc=()=>{}});function Pc(e){return!!(e&&typeof e.pipe=="function")}function We(e,t){if($c(e,"signal"),!Pc(t))throw new bo("stream","stream.Stream",t);return module.exports.addAbortSignalNoValidate(e,t)}var bo,$c,Kt=M(()=>{"use strict";T();se();({ERR_INVALID_ARG_TYPE:bo}=F),$c=(e,t)=>{if(typeof e!="object"||!("aborted"in e))throw new bo(t,"AbortSignal",e)}});var at={};tt(at,{construct:()=>Ot,destroy:()=>kt,destroyer:()=>z,errorOrDestroy:()=>nr,undestroy:()=>Mt});function Eo(e,t,n){e&&(e.stack,t&&!t.errored&&(t.errored=e),n&&!n.errored&&(n.errored=e))}function kt(e,t){let n=this._readableState,r=this._writableState,i=r||n;return r&&r.destroyed||n&&n.destroyed?(typeof t=="function"&&t(),this):(Eo(e,r,n),r&&(r.destroyed=!0),n&&(n.destroyed=!0),i.constructed?So(this,e,t):this.once(_o,function(o){So(this,je(o,e),t)}),this)}function So(e,t,n){let r=!1;function i(o){if(r)return;r=!0;let s=e._readableState,l=e._writableState;Eo(o,l,s),l&&(l.closed=!0),s&&(s.closed=!0),typeof n=="function"&&n(o),o?y.nextTick(Bc,e,o):y.nextTick(xo,e)}try{let o=e._destroy(t||null,i);if(o!=null){let s=o.then;typeof s=="function"&&s.call(o,function(){y.nextTick(i,null)},function(l){y.nextTick(i,l)})}}catch(o){i(o)}}function Bc(e,t){tr(e,t),xo(e)}function xo(e){let t=e._readableState,n=e._writableState;n&&(n.closeEmitted=!0),t&&(t.closeEmitted=!0),(n&&n.emitClose||t&&t.emitClose)&&e.emit("close")}function tr(e,t){let n=e._readableState,r=e._writableState;r&&r.errorEmitted||n&&n.errorEmitted||(r&&(r.errorEmitted=!0),n&&(n.errorEmitted=!0),e.emit("error",t))}function Mt(){let e=this._readableState,t=this._writableState;e&&(e.constructed=!0,e.closed=!1,e.closeEmitted=!1,e.destroyed=!1,e.errored=null,e.errorEmitted=!1,e.reading=!1,e.ended=e.readable===!1,e.endEmitted=e.readable===!1),t&&(t.constructed=!0,t.destroyed=!1,t.closed=!1,t.closeEmitted=!1,t.errored=null,t.errorEmitted=!1,t.finalCalled=!1,t.prefinished=!1,t.ended=t.writable===!1,t.ending=t.writable===!1,t.finished=t.writable===!1)}function nr(e,t,n){let r=e._readableState,i=e._writableState;if(i&&i.destroyed||r&&r.destroyed)return this;r&&r.autoDestroy||i&&i.autoDestroy?e.destroy(t):t&&(t.stack,i&&!i.errored&&(i.errored=t),r&&!r.errored&&(r.errored=t),n?y.nextTick(tr,e,t):tr(e,t))}function Ot(e,t){if(typeof e._construct!="function")return;let n=e._readableState,r=e._writableState;n&&(n.constructed=!1),r&&(r.constructed=!1),e.once(er,t),!(e.listenerCount(er)>1)&&y.nextTick(Uc,e)}function Uc(e){let t=!1;function n(r){if(t){nr(e,r??new Fc);return}t=!0;let i=e._readableState,o=e._writableState,s=o||i;i&&(i.constructed=!0),o&&(o.constructed=!0),s.destroyed?e.emit(_o,r):r?nr(e,r,!0):y.nextTick(Dc,e)}try{let r=e._construct(n);if(r!=null){let i=r.then;typeof i=="function"&&i.call(r,function(){y.nextTick(n,null)},function(o){y.nextTick(n,o)})}}catch(r){n(r)}}function Dc(e){e.emit(er)}function wo(e){return e&&e.setHeader&&typeof e.abort=="function"}function To(e){e.emit("close")}function Ic(e,t){e.emit("error",t),y.nextTick(To,e)}function z(e,t){!e||Nt(e)||(!t&&!fo(e)&&(t=new oe),po(e)?(e.socket=null,e.destroy(t)):wo(e)?e.abort():wo(e.req)?e.req.abort():typeof e.destroy=="function"?e.destroy(t):typeof e.close=="function"?e.close():t?y.nextTick(Ic,e):y.nextTick(To,e),e.destroyed||(e[Xn]=!0))}var Fc,_o,er,ze=M(()=>{"use strict";T();se();$e();re();({ERR_MULTIPLE_CALLBACK:Fc}=F),_o=Symbol("kDestroy"),er=Symbol("kConstruct")});function Gc(e){console.warn(e)}function $(){$.init.call(this)}function Xt(e){if(typeof e!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function Ro(e){return e._maxListeners===void 0?$.defaultMaxListeners:e._maxListeners}function vo(e,t,n,r){let i;Xt(n);let o=e._events;if(o===void 0?(o=e._events=Object.create(null),e._eventsCount=0):(o.newListener!==void 0&&(e.emit("newListener",t,n.listener?n.listener:n),o=e._events),i=o[t]),i===void 0)i=o[t]=n,++e._eventsCount;else{typeof i=="function"?i=o[t]=r?[n,i]:[i,n]:r?i.unshift(n):i.push(n);let s=Ro(e);if(s>0&&i.length>s&&!i.warned){i.warned=!0;let l=new Error("Possible EventEmitter memory leak detected. "+i.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");l.name="MaxListenersExceededWarning",l.emitter=e,l.type=t,l.count=i.length,Gc(l)}}return e}function jc(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function No(e,t,n){let r={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},i=jc.bind(r);return i.listener=n,r.wrapFn=i,i}function ko(e,t,n){let r=e._events;if(r===void 0)return[];let i=r[t];return i===void 0?[]:typeof i=="function"?n?[i.listener||i]:[i]:n?Wc(i):Oo(i,i.length)}function Mo(e){let t=this._events;if(t!==void 0){let n=t[e];if(typeof n=="function")return 1;if(n!==void 0)return n.length}return 0}function Oo(e,t){let n=new Array(t);for(let r=0;r<t;++r)n[r]=e[r];return n}function Hc(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}function Wc(e){let t=new Array(e.length);for(let n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}var ee,Ao,Ct=M(()=>{"use strict";T();ee=$;$.EventEmitter=$;$.prototype._events=void 0;$.prototype._eventsCount=0;$.prototype._maxListeners=void 0;Ao=10;Object.defineProperty($,"defaultMaxListeners",{enumerable:!0,get:function(){return Ao},set:function(e){if(typeof e!="number"||e<0||Number.isNaN(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");Ao=e}});$.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};$.prototype.setMaxListeners=function(t){if(typeof t!="number"||t<0||Number.isNaN(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this};$.prototype.getMaxListeners=function(){return Ro(this)};$.prototype.emit=function(t){let n=[];for(let s=1;s<arguments.length;s++)n.push(arguments[s]);let r=t==="error",i=this._events;if(i!==void 0)r=r&&i.error===void 0;else if(!r)return!1;if(r){let s;if(n.length>0&&(s=n[0]),s instanceof Error)throw s;let l=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw l.context=s,l}let o=i[t];if(o===void 0)return!1;if(typeof o=="function")Reflect.apply(o,this,n);else{let s=o.length,l=Oo(o,s);for(let c=0;c<s;++c)Reflect.apply(l[c],this,n)}return!0};$.prototype.addListener=function(t,n){return vo(this,t,n,!1)};$.prototype.on=$.prototype.addListener;$.prototype.prependListener=function(t,n){return vo(this,t,n,!0)};$.prototype.once=function(t,n){return Xt(n),this.on(t,No(this,t,n)),this};$.prototype.prependOnceListener=function(t,n){return Xt(n),this.prependListener(t,No(this,t,n)),this};$.prototype.removeListener=function(t,n){Xt(n);let r=this._events;if(r===void 0)return this;let i=r[t];if(i===void 0)return this;if(i===n||i.listener===n)--this._eventsCount===0?this._events=Object.create(null):(delete r[t],r.removeListener&&this.emit("removeListener",t,i.listener||n));else if(typeof i!="function"){let o,s=-1;for(let l=i.length-1;l>=0;l--)if(i[l]===n||i[l].listener===n){o=i[l].listener,s=l;break}if(s<0)return this;s===0?i.shift():Hc(i,s),i.length===1&&(r[t]=i[0]),r.removeListener!==void 0&&this.emit("removeListener",t,o||n)}return this};$.prototype.off=$.prototype.removeListener;$.prototype.removeAllListeners=function(t){let n=this._events;if(n===void 0)return this;if(n.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):n[t]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete n[t]),this;if(arguments.length===0){let i=Object.keys(n);for(let o=0;o<i.length;++o){let s=i[o];s!=="removeListener"&&this.removeAllListeners(s)}return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}let r=n[t];if(typeof r=="function")this.removeListener(t,r);else if(r!==void 0)for(let i=r.length-1;i>=0;i--)this.removeListener(t,r[i]);return this};$.prototype.listeners=function(t){return ko(this,t,!0)};$.prototype.rawListeners=function(t){return ko(this,t,!1)};$.listenerCount=function(e,t){return typeof e.listenerCount=="function"?e.listenerCount(t):Mo.call(e,t)};$.prototype.listenerCount=Mo;$.prototype.eventNames=function(){return this._eventsCount>0?Reflect.ownKeys(this._events):[]}});function V(e){return typeof e?.[on]=="string"}function lr(){return or===null&&(or=new FinalizationRegistry(e=>e.remove())),sr===null&&(sr=new WeakMap),{registry:or,map:sr}}function Ho(e){e[le]=new Map,e[sn]=ee.defaultMaxListeners,e[ar]=!1}function Yc(e){Ho(e)}function Po(e){if(typeof e=="function"||typeof e?.handleEvent=="function")return!0;if(e==null)return!1;throw new Fo("listener","EventListener",e)}function qc(e){return typeof e=="boolean"?{capture:e}:e===null?{}:{once:!!e.once,capture:!!e.capture,passive:!!e.passive,signal:e.signal,weak:e[Go],isNodeStyleListener:!!e[rn]}}function en(e){return e?.constructor?.[Uo]}function ge(e){return e?.constructor?.[Do]}function Jc(e){let t=e.then;typeof t=="function"&&t.call(e,void 0,function(n){Wo(n)})}function Wo(e){y.nextTick(()=>{throw e})}function Kc(e){function t(...n){if(typeof t.handler=="function")return Reflect.apply(t.handler,this,n)}return t.handler=e,t}function zo(e,t){Object.defineProperty(e,`on${t}`,{get(){return this[Lt]?.get(t)?.handler},set(n){this[Lt]||(this[Lt]=new Map);let r=this[Lt]?.get(t);if(r){if(typeof r.handler=="function"){this[le].get(t).size--;let i=this[le].get(t).size;this[nn](i,t,r.handler,!1)}if(r.handler=n,typeof r.handler=="function"){this[le].get(t).size++;let i=this[le].get(t).size;this[tn](i,t,n,!1,!1,!1)}}else r=Kc(n),this.addEventListener(t,r);this[Lt].set(t,r)},configurable:!0,enumerable:!0})}var Fo,zc,Bo,C,Uo,Do,sn,ar,le,Ae,Io,Ve,Lt,Go,cr,Co,tn,nn,rn,dr,on,Zt,Qt,rr,Lo,$o,ir,jo,Vc,Ye,L,ur,or,sr,ln,qe,fr,Vo=M(()=>{"use strict";T();se();Ct();re();st();({ERR_INVALID_ARG_TYPE:Fo,ERR_EVENT_RECURSION:zc,ERR_MISSING_ARGS:Bo,ERR_INVALID_THIS:C}=F),Uo=Symbol.for("nodejs.event_target"),Do=Symbol("kIsNodeEventTarget"),{kMaxEventTargetListeners:sn,kMaxEventTargetListenersWarned:ar}=ee,le=Symbol("kEvents"),Ae=Symbol("kIsBeingDispatched"),Io=Symbol("kStop"),Ve=Symbol("kTarget"),Lt=Symbol("khandlers"),Go=Symbol("kWeak"),cr=Symbol.for("nodejs.internal.kHybridDispatch"),Co=Symbol("kCreateEvent"),tn=Symbol("kNewListener"),nn=Symbol("kRemoveListener"),rn=Symbol("kIsNodeStyleListener"),dr=Symbol("kTrustEvent"),on=Symbol("type"),Zt=Symbol("defaultPrevented"),Qt=Symbol("cancelable"),rr=Symbol("timestamp"),Lo=Symbol("bubbles"),$o=Symbol("composed"),ir=Symbol("propagationStopped"),jo=new WeakSet,Vc=Object.getOwnPropertyDescriptor({get isTrusted(){return jo.has(this)}},"isTrusted").get;Ye=class e{constructor(t,n=null){if(arguments.length===0)throw new Bo("type");let{cancelable:r,bubbles:i,composed:o}={...n};this[Qt]=!!r,this[Lo]=!!i,this[$o]=!!o,this[on]=`${t}`,this[Zt]=!1,this[rr]=Date.now(),this[ir]=!1,n?.[dr]&&jo.add(this),Object.defineProperty(this,"isTrusted",{get:Vc,enumerable:!0,configurable:!1}),this[Ve]=null,this[Ae]=!1}[D.custom](t,n){if(!V(this))throw new C("Event");let r=this.constructor.name;if(t<0)return r;let i=Object.assign({},n,{depth:Number.isInteger(n.depth)?n.depth-1:n.depth});return`${r} ${D({type:this[on],defaultPrevented:this[Zt],cancelable:this[Qt],timeStamp:this[rr]},i)}`}stopImmediatePropagation(){if(!V(this))throw new C("Event");this[Io]=!0}preventDefault(){if(!V(this))throw new C("Event");this[Zt]=!0}get target(){if(!V(this))throw new C("Event");return this[Ve]}get currentTarget(){if(!V(this))throw new C("Event");return this[Ve]}get srcElement(){if(!V(this))throw new C("Event");return this[Ve]}get type(){if(!V(this))throw new C("Event");return this[on]}get cancelable(){if(!V(this))throw new C("Event");return this[Qt]}get defaultPrevented(){if(!V(this))throw new C("Event");return this[Qt]&&this[Zt]}get timeStamp(){if(!V(this))throw new C("Event");return this[rr]}composedPath(){if(!V(this))throw new C("Event");return this[Ae]?[this[Ve]]:[]}get returnValue(){if(!V(this))throw new C("Event");return!this.defaultPrevented}get bubbles(){if(!V(this))throw new C("Event");return this[Lo]}get composed(){if(!V(this))throw new C("Event");return this[$o]}get eventPhase(){if(!V(this))throw new C("Event");return this[Ae]?e.AT_TARGET:e.NONE}get cancelBubble(){if(!V(this))throw new C("Event");return this[ir]}set cancelBubble(t){if(!V(this))throw new C("Event");t&&this.stopPropagation()}stopPropagation(){if(!V(this))throw new C("Event");this[ir]=!0}static NONE=0;static CAPTURING_PHASE=1;static AT_TARGET=2;static BUBBLING_PHASE=3},L=Object.create(null);L.enumerable=!0;Object.defineProperties(Ye.prototype,{[Symbol.toStringTag]:{writable:!1,enumerable:!1,configurable:!0,value:"Event"},stopImmediatePropagation:L,preventDefault:L,target:L,currentTarget:L,srcElement:L,type:L,cancelable:L,defaultPrevented:L,timeStamp:L,composedPath:L,returnValue:L,bubbles:L,composed:L,eventPhase:L,cancelBubble:L,stopPropagation:L});ur=class extends Ye{constructor(t,n){super(t,n),n?.detail&&(this.detail=n.detail)}},or=null,sr=null;ln=class{constructor(t,n,r,i,o,s,l){this.next=void 0,t!==void 0&&(t.next=this),this.previous=t,this.listener=n,this.once=r,this.capture=i,this.passive=o,this.isNodeStyleListener=s,this.removed=!1,this.weak=!!l,this.weak?(this.callback=new WeakRef(n),lr().registry.register(n,this,this),lr().map.set(l,n),this.listener=this.callback):typeof n=="function"?(this.callback=n,this.listener=n):(this.callback=n.handleEvent.bind(n),this.listener=n)}same(t,n){return(this.weak?this.listener.deref():this.listener)===t&&this.capture===n}remove(){this.previous!==void 0&&(this.previous.next=this.next),this.next!==void 0&&(this.next.previous=this.previous),this.removed=!0,this.weak&&lr().registry.unregister(this)}};qe=class{static[Uo]=!0;constructor(){Ho(this)}[tn](t,n,r,i,o,s){if(this[sn]>0&&t>this[sn]&&!this[ar]){this[ar]=!0;let l=new Error(`Possible EventTarget memory leak detected. ${t} ${n} listeners added to ${D(this,{depth:-1})}. Use events.setMaxListeners() to increase limit`);l.name="MaxListenersExceededWarning",l.target=this,l.type=n,l.count=t,y.emitWarning(l)}}[nn](t,n,r,i){}addEventListener(t,n,r={}){if(!en(this))throw new C("EventTarget");if(arguments.length<2)throw new Bo("type","listener");let{once:i,capture:o,passive:s,signal:l,isNodeStyleListener:c,weak:f}=qc(r);if(!Po(n)){let d=new Error(`addEventListener called with ${n} which has no effect.`);d.name="AddEventListenerArgumentTypeWarning",d.target=this,d.type=t,y.emitWarning(d);return}if(t=String(t),l){if(l.aborted)return;l.addEventListener("abort",()=>{this.removeEventListener(t,n,r)},{once:!0,[Go]:this})}let h=this[le].get(t);if(h===void 0){h={size:1,next:void 0},new ln(h,n,i,o,s,c,f),this[tn](h.size,t,n,i,o,s),this[le].set(t,h);return}let a=h.next,u=h;for(;a!==void 0&&!a.same(n,o);)u=a,a=a.next;a===void 0&&(new ln(u,n,i,o,s,c,f),h.size++,this[tn](h.size,t,n,i,o,s))}removeEventListener(t,n,r={}){if(!en(this))throw new C("EventTarget");if(!Po(n))return;t=String(t);let i=r?.capture===!0,o=this[le].get(t);if(o===void 0||o.next===void 0)return;let s=o.next;for(;s!==void 0;){if(s.same(n,i)){s.remove(),o.size--,o.size===0&&this[le].delete(t),this[nn](o.size,t,n,i);break}s=s.next}}dispatchEvent(t){if(!en(this))throw new C("EventTarget");if(!(t instanceof Ye))throw new Fo("event","Event",t);if(t[Ae])throw new zc(t.type);return this[cr](t,t.type,t),t.defaultPrevented!==!0}[cr](t,n,r){let i=()=>(r===void 0&&(r=this[Co](t,n),r[Ve]=this,r[Ae]=!0),r);r!==void 0&&(r[Ve]=this,r[Ae]=!0);let o=this[le].get(n);if(o===void 0||o.next===void 0)return r!==void 0&&(r[Ae]=!1),!0;let s=o.next,l;for(;s!==void 0&&(s.passive||r?.[Io]!==!0);){if(l=s.next,s.removed){s=l;continue}if(s.once){s.remove(),o.size--;let{listener:c,capture:f}=s;this[nn](o.size,n,c,f)}try{let c;s.isNodeStyleListener?c=t:c=i();let f=s.weak?s.callback.deref():s.callback,h;f&&(h=f.call(this,c),s.isNodeStyleListener||(c[Ae]=!1)),h!=null&&Jc(h)}catch(c){Wo(c)}s=l}r!==void 0&&(r[Ae]=!1)}[Co](t,n){return new ur(n,{detail:t})}[D.custom](t,n){if(!en(this))throw new C("EventTarget");let r=this.constructor.name;if(t<0)return r;let i=Object.assign({},n,{depth:Number.isInteger(n.depth)?n.depth-1:n.depth});return`${r} ${D({},i)}`}};Object.defineProperties(qe.prototype,{addEventListener:L,removeEventListener:L,dispatchEvent:L,[Symbol.toStringTag]:{writable:!1,enumerable:!1,configurable:!0,value:"EventTarget"}});fr=class extends qe{static[Do]=!0;static defaultMaxListeners=10;constructor(){super(),Yc(this)}setMaxListeners(t){if(!ge(this))throw new C("NodeEventTarget");ee.setMaxListeners(t,this)}getMaxListeners(){if(!ge(this))throw new C("NodeEventTarget");return this[sn]}eventNames(){if(!ge(this))throw new C("NodeEventTarget");return Array.from(this[le].keys())}listenerCount(t){if(!ge(this))throw new C("NodeEventTarget");let n=this[le].get(String(t));return n!==void 0?n.size:0}off(t,n,r){if(!ge(this))throw new C("NodeEventTarget");return this.removeEventListener(t,n,r),this}removeListener(t,n,r){if(!ge(this))throw new C("NodeEventTarget");return this.removeEventListener(t,n,r),this}on(t,n){if(!ge(this))throw new C("NodeEventTarget");return this.addEventListener(t,n,{[rn]:!0}),this}addListener(t,n){if(!ge(this))throw new C("NodeEventTarget");return this.addEventListener(t,n,{[rn]:!0}),this}emit(t,n){if(!ge(this))throw new C("NodeEventTarget");let r=this.listenerCount(t)>0;return this[cr](n,t),r}once(t,n){if(!ge(this))throw new C("NodeEventTarget");return this.addEventListener(t,n,{once:!0,[rn]:!0}),this}removeAllListeners(t){if(!ge(this))throw new C("NodeEventTarget");return t!==void 0?this[le].delete(String(t)):this[le].clear(),this}};Object.defineProperties(fr.prototype,{setMaxListeners:L,getMaxListeners:L,eventNames:L,listenerCount:L,off:L,removeListener:L,on:L,addListener:L,once:L,emit:L,removeAllListeners:L})});function Jo(e,t,n,r){if(n<0)return e;let i=Object.assign({},r,{depth:r.depth===null?null:r.depth-1});return`${e.constructor.name} ${D(t,i)}`}function Zc(e){if(e?.[$t]===void 0)throw new qo("AbortSignal")}function Ko(e=!1){let t=new qe;return Object.setPrototypeOf(t,ct.prototype),t[$t]=e,t}function Qc(e){if(e[$t])return;e[$t]=!0;let t=new Ye("abort",{[dr]:!0});e.dispatchEvent(t)}function Yo(e){if(e?.[an]===void 0)throw new qo("AbortController")}var Xc,qo,$t,ct,an,Pe,hr=M(()=>{"use strict";T();se();Vo();st();({ERR_ILLEGAL_CONSTRUCTOR:Xc,ERR_INVALID_THIS:qo}=F),$t=Symbol("kAborted");ct=class extends qe{constructor(){throw new Xc}get aborted(){return Zc(this),!!this[$t]}[D.custom](t,n){return Jo(this,{aborted:this.aborted},t,n)}static abort(){return Ko(!0)}};Object.defineProperties(ct.prototype,{aborted:{enumerable:!0}});Object.defineProperty(ct.prototype,Symbol.toStringTag,{writable:!1,enumerable:!1,configurable:!0,value:"AbortSignal"});zo(ct.prototype,"abort");an=Symbol("signal");Pe=class{constructor(){this[an]=Ko()}get signal(){return Yo(this),this[an]}abort(){Yo(this),Qc(this[an])}[D.custom](t,n){return Jo(this,{signal:this.signal},t,n)}};Object.defineProperties(Pe.prototype,{signal:{enumerable:!0},abort:{enumerable:!0}});Object.defineProperty(Pe.prototype,Symbol.toStringTag,{writable:!1,enumerable:!1,configurable:!0,value:"AbortController"})});function ut(e,t,n){let r;if(typeof t=="string"||t instanceof g)return new e({objectMode:!0,...n,read(){this.push(t),this.push(null)}});let i;if(t&&t[Symbol.asyncIterator])i=!0,r=t[Symbol.asyncIterator]();else if(t&&t[Symbol.iterator])i=!1,r=t[Symbol.iterator]();else throw new eu("iterable",["Iterable"],t);let o=new e({objectMode:!0,highWaterMark:1,...n}),s=!1;o._read=function(){s||(s=!0,c())},o._destroy=function(f,h){l(f).then(()=>y.nextTick(h,f),a=>y.nextTick(h,a||f))};async function l(f){let h=f!=null,a=typeof r.throw=="function";if(h&&a){let{value:u,done:d}=await r.throw(f);if(await u,d)return}if(typeof r.return=="function"){let{value:u}=await r.return();await u}}async function c(){for(;;){try{let{value:f,done:h}=i?await r.next():r.next();if(h)o.push(null);else{let a=f&&typeof f.then=="function"?await f:f;if(a===null)throw s=!1,new tu;if(o.push(a))continue;s=!1}}catch(f){o.destroy(f)}break}}return o}var eu,tu,gr=M(()=>{"use strict";T();se();ke();re();({ERR_INVALID_ARG_TYPE:eu,ERR_STREAM_NULL_VALUES:tu}=F)});var Pt,Xo=M(()=>{"use strict";T();ke();st();Pt=class{constructor(){this.head=null,this.tail=null,this.length=0}push(t){let n={data:t,next:null};this.length>0?this.tail.next=n:this.head=n,this.tail=n,++this.length}unshift(t){let n={data:t,next:this.head};this.length===0&&(this.tail=n),this.head=n,++this.length}shift(){if(this.length===0)return;let t=this.head.data;return this.length===1?this.head=this.tail=null:this.head=this.head.next,--this.length,t}clear(){this.head=this.tail=null,this.length=0}join(t){if(this.length===0)return"";let n=this.head,r=""+n.data;for(;n=n.next;)r+=t+n.data;return r}concat(t){if(this.length===0)return g.alloc(0);let n=g.allocUnsafe(t>>>0),r=this.head,i=0;for(;r;)n.set(r.data,i),i+=r.data.length,r=r.next;return n}consume(t,n){let r=this.head.data;if(t<r.length){let i=r.slice(0,t);return this.head.data=r.slice(t),i}return t===r.length?this.shift():n?this._getString(t):this._getBuffer(t)}first(){return this.head.data}*[Symbol.iterator](){for(let t=this.head;t;t=t.next)yield t.data}_getString(t){let n="",r=this.head,i=0;do{let o=r.data;if(t>o.length)n+=o,t-=o.length;else{t===o.length?(n+=o,++i,r.next?this.head=r.next:this.head=this.tail=null):(n+=o.slice(0,t),this.head=r,r.data=o.slice(t));break}++i}while(r=r.next);return this.length-=i,n}_getBuffer(t){let n=g.allocUnsafe(t),r=t,i=this.head,o=0;do{let s=i.data;if(t>s.length)n.set(s,r-t),t-=s.length;else{t===s.length?(n.set(s,r-t),++o,i.next?this.head=i.next:this.head=this.tail=null):(n.set(new Uint8Array(s.buffer,s.byteOffset,t),r-t),this.head=i,i.data=s.slice(t));break}++o}while(i=i.next);return this.length-=o,n}[D.custom](t,n){return D(this,{...n,depth:0,customInspect:!1})}}});function N(e){ee.call(this,e)}function cn(e,t,n){if(typeof e.prependListener=="function")return e.prependListener(t,n);!e._events||!e._events[t]?e.on(t,n):Array.isArray(e._events[t])?e._events[t].unshift(n):e._events[t]=[n,e._events[t]]}var un=M(()=>{"use strict";T();Ct();Object.setPrototypeOf(N.prototype,ee.prototype);Object.setPrototypeOf(N,ee);N.prototype.pipe=function(e,t){let n=this;function r(h){e.writable&&e.write(h)===!1&&n.pause&&n.pause()}n.on("data",r);function i(){n.readable&&n.resume&&n.resume()}e.on("drain",i),!e._isStdio&&(!t||t.end!==!1)&&(n.on("end",s),n.on("close",l));let o=!1;function s(){o||(o=!0,e.end())}function l(){o||(o=!0,typeof e.destroy=="function"&&e.destroy())}function c(h){f(),ee.listenerCount(this,"error")===0&&this.emit("error",h)}cn(n,"error",c),cn(e,"error",c);function f(){n.removeListener("data",r),e.removeListener("drain",i),n.removeListener("end",s),n.removeListener("close",l),n.removeListener("error",c),e.removeListener("error",c),n.removeListener("end",f),n.removeListener("close",f),e.removeListener("close",f)}return n.on("end",f),n.on("close",f),e.on("close",f),e.emit("pipe",n),e}});function ru(e,t,n){return e.highWaterMark!=null?e.highWaterMark:t?e[n]:null}function Ft(e){return e?16:16*1024}function fn(e,t,n,r){let i=ru(t,r,n);if(i!=null){if(!Number.isInteger(i)||i<0){let o=r?`options.${n}`:"options.highWaterMark";throw new nu(o,i)}return Math.floor(i)}return Ft(e.objectMode)}var nu,pr=M(()=>{"use strict";T();se();({ERR_INVALID_ARG_VALUE:nu}=F)});function iu(e){if(!e)return"utf8";let t=!1;for(;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}function ou(e){let t=iu(e);if(t===void 0&&(g.isEncoding===Zo||!Zo(e)))throw new Error("Unknown encoding: "+e);return t||e}function Je(e){this.encoding=ou(e);let t;switch(this.encoding){case"utf16le":this.text=fu,this.end=du,t=4;break;case"utf8":this.fillLast=au,t=4;break;case"base64":this.text=hu,this.end=gu,t=3;break;default:this.write=pu,this.end=mu;return}this.lastNeed=0,this.lastTotal=0,this.lastChar=g.allocUnsafe(t)}function mr(e){return e<=127?0:e>>5===6?2:e>>4===14?3:e>>3===30?4:e>>6===2?-1:-2}function su(e,t,n){let r=t.length-1;if(r<n)return 0;let i=mr(t[r]);return i>=0?(i>0&&(e.lastNeed=i-1),i):--r<n||i===-2?0:(i=mr(t[r]),i>=0?(i>0&&(e.lastNeed=i-2),i):--r<n||i===-2?0:(i=mr(t[r]),i>=0?(i>0&&(i===2?i=0:e.lastNeed=i-3),i):0))}function lu(e,t,n){if((t[0]&192)!==128)return e.lastNeed=0,"\uFFFD";if(e.lastNeed>1&&t.length>1){if((t[1]&192)!==128)return e.lastNeed=1,"\uFFFD";if(e.lastNeed>2&&t.length>2&&(t[2]&192)!==128)return e.lastNeed=2,"\uFFFD"}}function au(e){let t=this.lastTotal-this.lastNeed,n=lu(this,e,t);if(n!==void 0)return n;if(this.lastNeed<=e.length)return e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,t,0,e.length),this.lastNeed-=e.length}function cu(e,t){let n=su(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=n;let r=e.length-(n-this.lastNeed);return e.copy(this.lastChar,0,r),e.toString("utf8",t,r)}function uu(e){let t=e&&e.length?this.write(e):"";return this.lastNeed?t+"\uFFFD":t}function fu(e,t){if((e.length-t)%2===0){let n=e.toString("utf16le",t);if(n){let r=n.charCodeAt(n.length-1);if(r>=55296&&r<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],n.slice(0,-1)}return n}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function du(e){let t=e&&e.length?this.write(e):"";if(this.lastNeed){let n=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,n)}return t}function hu(e,t){let n=(e.length-t)%3;return n===0?e.toString("base64",t):(this.lastNeed=3-n,this.lastTotal=3,n===1?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-n))}function gu(e){let t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function pu(e){return e.toString(this.encoding)}function mu(e){return e&&e.length?this.write(e):""}var Zo,Qo=M(()=>{"use strict";T();ke();Zo=g.isEncoding;Je.prototype.write=function(e){if(e.length===0)return"";let t,n;if(this.lastNeed){if(t=this.fillLast(e),t===void 0)return"";n=this.lastNeed,this.lastNeed=0}else n=0;return n<e.length?t?t+this.text(e,n):this.text(e,n):t||""};Je.prototype.end=uu;Je.prototype.text=cu;Je.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length}});function ts(e,t,n){typeof n!="boolean"&&(n=t instanceof N.Duplex),this.objectMode=!!(e&&e.objectMode),n&&(this.objectMode=this.objectMode||!!(e&&e.readableObjectMode)),this.highWaterMark=e?fn(this,e,"readableHighWaterMark",n):Ft(!1),this.buffer=new Pt,this.length=0,this.pipes=[],this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.constructed=!0,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this[Xe]=null,this.errorEmitted=!1,this.emitClose=!e||e.emitClose!==!1,this.autoDestroy=!e||e.autoDestroy!==!1,this.destroyed=!1,this.errored=null,this.closed=!1,this.closeEmitted=!1,this.defaultEncoding=e&&e.defaultEncoding||"utf8",this.awaitDrainWriters=null,this.multiAwaitDrain=!1,this.readingMore=!1,this.dataEmitted=!1,this.decoder=null,this.encoding=null,e&&e.encoding&&(this.decoder=new Je(e.encoding),this.encoding=e.encoding)}function k(e){if(!(this instanceof k))return new k(e);let t=this instanceof N.Duplex;this._readableState=new ts(e,this,t),e&&(typeof e.read=="function"&&(this._read=e.read),typeof e.destroy=="function"&&(this._destroy=e.destroy),typeof e.construct=="function"&&(this._construct=e.construct),e.signal&&!t&&We(e.signal,this)),N.call(this,e),Ot(this,()=>{this._readableState.needReadable&&hn(this,this._readableState)})}function ns(e,t,n,r){let i=e._readableState,o;if(i.objectMode||(typeof t=="string"?(n=n||i.defaultEncoding,i.encoding!==n&&(r&&i.encoding?t=g.from(t,n).toString(i.encoding):(t=g.from(t,n),n=""))):t instanceof g?n="":N._isUint8Array(t)?(t=N._uint8ArrayToBuffer(t),n=""):t!=null&&(o=new yu("chunk",["string","Buffer","Uint8Array"],t))),o)Ke(e,o);else if(t===null)i.reading=!1,Tu(e,i);else if(i.objectMode||t&&t.length>0)if(r)if(i.endEmitted)Ke(e,new _u);else{if(i.destroyed||i.errored)return!1;yr(e,i,t,!0)}else if(i.ended)Ke(e,new wu);else{if(i.destroyed||i.errored)return!1;i.reading=!1,i.decoder&&!n?(t=i.decoder.write(t),i.objectMode||t.length!==0?yr(e,i,t,!1):hn(e,i)):yr(e,i,t,!1)}else r||(i.reading=!1,hn(e,i));return!i.ended&&(i.length<i.highWaterMark||i.length===0)}function yr(e,t,n,r){t.flowing&&t.length===0&&!t.sync&&e.listenerCount("data")>0?(t.multiAwaitDrain?t.awaitDrainWriters.clear():t.awaitDrainWriters=null,t.dataEmitted=!0,e.emit("data",n)):(t.length+=t.objectMode?1:n.length,r?t.buffer.unshift(n):t.buffer.push(n),t.needReadable&&gn(e)),hn(e,t)}function xu(e){if(e>Eu)throw new Su("size","<= 1GiB",e);return e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++,e}function es(e,t){return e<=0||t.length===0&&t.ended?0:t.objectMode?1:Number.isNaN(e)?t.flowing&&t.length?t.buffer.first().length:t.length:e<=t.length?e:t.ended?t.length:0}function Tu(e,t){if(!t.ended){if(t.decoder){let n=t.decoder.end();n&&n.length&&(t.buffer.push(n),t.length+=t.objectMode?1:n.length)}t.ended=!0,t.sync?gn(e):(t.needReadable=!1,t.emittedReadable=!0,rs(e))}}function gn(e){let t=e._readableState;t.needReadable=!1,t.emittedReadable||(t.emittedReadable=!0,y.nextTick(rs,e))}function rs(e){let t=e._readableState;!t.destroyed&&!t.errored&&(t.length||t.ended)&&(e.emit("readable"),t.emittedReadable=!1),t.needReadable=!t.flowing&&!t.ended&&t.length<=t.highWaterMark,Sr(e)}function hn(e,t){!t.readingMore&&t.constructed&&(t.readingMore=!0,y.nextTick(Au,e,t))}function Au(e,t){for(;!t.reading&&!t.ended&&(t.length<t.highWaterMark||t.flowing&&t.length===0);){let n=t.length;if(e.read(0),n===t.length)break}t.readingMore=!1}function Ru(e,t){return function(){let r=e._readableState;r.awaitDrainWriters===t?r.awaitDrainWriters=null:r.multiAwaitDrain&&r.awaitDrainWriters.delete(t),(!r.awaitDrainWriters||r.awaitDrainWriters.size===0)&&ee.listenerCount(e,"data")&&(r.flowing=!0,Sr(e))}}function is(e){let t=e._readableState;t.readableListening=e.listenerCount("readable")>0,t.resumeScheduled&&t[Xe]===!1?t.flowing=!0:e.listenerCount("data")>0?e.resume():t.readableListening||(t.flowing=null)}function vu(e){e.read(0)}function Nu(e,t){t.resumeScheduled||(t.resumeScheduled=!0,y.nextTick(ku,e,t))}function ku(e,t){t.reading||e.read(0),t.resumeScheduled=!1,e.emit("resume"),Sr(e),t.flowing&&!t.reading&&e.read(0)}function Sr(e){let t=e._readableState;for(;t.flowing&&e.read()!==null;);}function os(e,t){typeof e.read!="function"&&(e=k.wrap(e,{objectMode:!0}));let n=Mu(e,t);return n.stream=e,n}async function*Mu(e,t){let n=dn;function r(o){this===e?(n(),n=dn):n=o}e.on("readable",r);let i;Q(e,{writable:!1},o=>{i=o?je(i,o):null,n(),n=dn});try{for(;;){let o=e.destroyed?null:e.read();if(o!==null)yield o;else{if(i)throw i;if(i===null)return;await new Promise(r)}}}catch(o){throw i=je(i,o),i}finally{(i||t?.destroyOnReturn!==!1)&&(i===void 0||e._readableState.autoDestroy)&&z(e,null)}}function ss(e,t){if(t.length===0)return null;let n;return t.objectMode?n=t.buffer.shift():!e||e>=t.length?(t.decoder?n=t.buffer.join(""):t.buffer.length===1?n=t.buffer.first():n=t.buffer.concat(t.length),t.buffer.clear()):n=t.buffer.consume(e,t.decoder),n}function br(e){let t=e._readableState;t.endEmitted||(t.ended=!0,y.nextTick(Ou,t,e))}function Ou(e,t){if(!e.errored&&!e.closeEmitted&&!e.endEmitted&&e.length===0){if(e.endEmitted=!0,t.emit("end"),t.writable&&t.allowHalfOpen===!1)y.nextTick(Cu,t);else if(e.autoDestroy){let n=t._writableState;(!n||n.autoDestroy&&(n.finished||n.writable===!1))&&t.destroy()}}}function Cu(e){e.writable&&!e.writableEnded&&!e.destroyed&&e.end()}var be,yu,bu,Su,wu,_u,Xe,dn,Ke,Eu,pn=M(()=>{"use strict";T();Kt();Xo();ze();lt();se();gr();un();pr();ke();Ct();re();Qo();be=k,{ERR_INVALID_ARG_TYPE:yu,ERR_METHOD_NOT_IMPLEMENTED:bu,ERR_OUT_OF_RANGE:Su,ERR_STREAM_PUSH_AFTER_EOF:wu,ERR_STREAM_UNSHIFT_AFTER_END_EVENT:_u}=F,Xe=Symbol("kPaused");Object.setPrototypeOf(k.prototype,N.prototype);Object.setPrototypeOf(k,N);dn=()=>{},{errorOrDestroy:Ke}=at;k.prototype.destroy=kt;k.prototype._undestroy=Mt;k.prototype._destroy=function(e,t){t(e)};k.prototype[ee.captureRejectionSymbol]=function(e){this.destroy(e)};k.prototype.push=function(e,t){return ns(this,e,t,!1)};k.prototype.unshift=function(e,t){return ns(this,e,t,!0)};k.prototype.isPaused=function(){let e=this._readableState;return e[Xe]===!0||e.flowing===!1};k.prototype.setEncoding=function(e){let t=new Je(e);this._readableState.decoder=t,this._readableState.encoding=this._readableState.decoder.encoding;let n=this._readableState.buffer,r="";for(let i of n)r+=t.write(i);return n.clear(),r!==""&&n.push(r),this._readableState.length=r.length,this};Eu=1073741824;k.prototype.read=function(e){e===void 0?e=NaN:Number.isInteger(e)||(e=Number.parseInt(e,10));let t=this._readableState,n=e;if(e>t.highWaterMark&&(t.highWaterMark=xu(e)),e!==0&&(t.emittedReadable=!1),e===0&&t.needReadable&&((t.highWaterMark!==0?t.length>=t.highWaterMark:t.length>0)||t.ended))return t.length===0&&t.ended?br(this):gn(this),null;if(e=es(e,t),e===0&&t.ended)return t.length===0&&br(this),null;let r=t.needReadable;if((t.length===0||t.length-e<t.highWaterMark)&&(r=!0),t.ended||t.reading||t.destroyed||t.errored||!t.constructed)r=!1;else if(r){t.reading=!0,t.sync=!0,t.length===0&&(t.needReadable=!0);try{let o=this._read(t.highWaterMark);if(o!=null){let s=o.then;typeof s=="function"&&s.call(o,dn,function(l){Ke(this,l)})}}catch(o){Ke(this,o)}t.sync=!1,t.reading||(e=es(n,t))}let i;return e>0?i=ss(e,t):i=null,i===null?(t.needReadable=t.length<=t.highWaterMark,e=0):(t.length-=e,t.multiAwaitDrain?t.awaitDrainWriters.clear():t.awaitDrainWriters=null),t.length===0&&(t.ended||(t.needReadable=!0),n!==e&&t.ended&&br(this)),i!==null&&!t.errorEmitted&&!t.closeEmitted&&(t.dataEmitted=!0,this.emit("data",i)),i};k.prototype._read=function(e){throw new bu("_read()")};k.prototype.pipe=function(e,t){let n=this,r=this._readableState;r.pipes.length===1&&(r.multiAwaitDrain||(r.multiAwaitDrain=!0,r.awaitDrainWriters=new Set(r.awaitDrainWriters?[r.awaitDrainWriters]:[]))),r.pipes.push(e);let o=(!t||t.end!==!1)&&e!==y.stdout&&e!==y.stderr?l:S;r.endEmitted?y.nextTick(o):n.once("end",o),e.on("unpipe",s);function s(_,x){_===n&&x&&x.hasUnpiped===!1&&(x.hasUnpiped=!0,h())}function l(){e.end()}let c,f=!1;function h(){e.removeListener("close",p),e.removeListener("finish",m),c&&e.removeListener("drain",c),e.removeListener("error",d),e.removeListener("unpipe",s),n.removeListener("end",l),n.removeListener("end",S),n.removeListener("data",u),f=!0,c&&r.awaitDrainWriters&&(!e._writableState||e._writableState.needDrain)&&c()}function a(){f||(r.pipes.length===1&&r.pipes[0]===e?(r.awaitDrainWriters=e,r.multiAwaitDrain=!1):r.pipes.length>1&&r.pipes.includes(e)&&r.awaitDrainWriters.add(e),n.pause()),c||(c=Ru(n,e),e.on("drain",c))}n.on("data",u);function u(_){e.write(_)===!1&&a()}function d(_){if(S(),e.removeListener("error",d),ee.listenerCount(e,"error")===0){let x=e._writableState||e._readableState;x&&!x.errorEmitted?Ke(e,_):e.emit("error",_)}}cn(e,"error",d);function p(){e.removeListener("finish",m),S()}e.once("close",p);function m(){e.removeListener("close",p),S()}e.once("finish",m);function S(){n.unpipe(e)}return e.emit("pipe",n),e.writableNeedDrain===!0?r.flowing&&a():r.flowing||n.resume(),e};k.prototype.unpipe=function(e){let t=this._readableState,n={hasUnpiped:!1};if(t.pipes.length===0)return this;if(!e){let i=t.pipes;t.pipes=[],this.pause();for(let o=0;o<i.length;o++)i[o].emit("unpipe",this,{hasUnpiped:!1});return this}let r=t.pipes.indexOf(e);return r===-1?this:(t.pipes.splice(r,1),t.pipes.length===0&&this.pause(),e.emit("unpipe",this,n),this)};k.prototype.on=function(e,t){let n=N.prototype.on.call(this,e,t),r=this._readableState;return e==="data"?(r.readableListening=this.listenerCount("readable")>0,r.flowing!==!1&&this.resume()):e==="readable"&&!r.endEmitted&&!r.readableListening&&(r.readableListening=r.needReadable=!0,r.flowing=!1,r.emittedReadable=!1,r.length?gn(this):r.reading||y.nextTick(vu,this)),n};k.prototype.addListener=k.prototype.on;k.prototype.removeListener=function(e,t){let n=N.prototype.removeListener.call(this,e,t);return e==="readable"&&y.nextTick(is,this),n};k.prototype.off=k.prototype.removeListener;k.prototype.removeAllListeners=function(e){let t=N.prototype.removeAllListeners.apply(this,arguments);return(e==="readable"||e===void 0)&&y.nextTick(is,this),t};k.prototype.resume=function(){let e=this._readableState;return e.flowing||(e.flowing=!e.readableListening,Nu(this,e)),e[Xe]=!1,this};k.prototype.pause=function(){return this._readableState.flowing!==!1&&(this._readableState.flowing=!1,this.emit("pause")),this._readableState[Xe]=!0,this};k.prototype.wrap=function(e){let t=!1;e.on("data",r=>{!this.push(r)&&e.pause&&(t=!0,e.pause())}),e.on("end",()=>{this.push(null)}),e.on("error",r=>{Ke(this,r)}),e.on("close",()=>{this.destroy()}),e.on("destroy",()=>{this.destroy()}),this._read=()=>{t&&e.resume&&(t=!1,e.resume())};let n=Object.keys(e);for(let r=1;r<n.length;r++){let i=n[r];this[i]===void 0&&typeof e[i]=="function"&&(this[i]=e[i].bind(e))}return this};k.prototype[Symbol.asyncIterator]=function(){return os(this)};k.prototype.iterator=function(e){return os(this,e)};Object.defineProperties(k.prototype,{readable:{get(){let e=this._readableState;return!!e&&e.readable!==!1&&!e.destroyed&&!e.errorEmitted&&!e.endEmitted},set(e){this._readableState&&(this._readableState.readable=!!e)}},readableDidRead:{enumerable:!1,get:function(){return this._readableState.dataEmitted}},readableAborted:{enumerable:!1,get:function(){return!!(this._readableState.destroyed||this._readableState.errored)&&!this._readableState.endEmitted}},readableHighWaterMark:{enumerable:!1,get:function(){return this._readableState.highWaterMark}},readableBuffer:{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}},readableFlowing:{enumerable:!1,get:function(){return this._readableState.flowing},set:function(e){this._readableState&&(this._readableState.flowing=e)}},readableLength:{enumerable:!1,get(){return this._readableState.length}},readableObjectMode:{enumerable:!1,get(){return this._readableState?this._readableState.objectMode:!1}},readableEncoding:{enumerable:!1,get(){return this._readableState?this._readableState.encoding:null}},destroyed:{enumerable:!1,get(){return this._readableState===void 0?!1:this._readableState.destroyed},set(e){this._readableState&&(this._readableState.destroyed=e)}},readableEnded:{enumerable:!1,get(){return this._readableState?this._readableState.endEmitted:!1}}});Object.defineProperties(ts.prototype,{pipesCount:{get(){return this.pipes.length}},paused:{get(){return this[Xe]!==!1},set(e){this[Xe]=!!e}}});k._fromList=ss;k.from=function(e,t){return ut(k,e,t)};k.wrap=function(e,t){return new k({objectMode:e.readableObjectMode??e.objectMode??!0,...t,destroy(n,r){z(e,n),r(n)}}).wrap(e)}});function Er(){}function yn(e,t,n){typeof n!="boolean"&&(n=t instanceof N.Duplex),this.objectMode=!!(e&&e.objectMode),n&&(this.objectMode=this.objectMode||!!(e&&e.writableObjectMode)),this.highWaterMark=e?fn(this,e,"writableHighWaterMark",n):Ft(!1),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;let r=!!(e&&e.decodeStrings===!1);this.decodeStrings=!r,this.defaultEncoding=e&&e.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=Iu.bind(void 0,t),this.writecb=null,this.writelen=0,this.afterWriteTickInfo=null,mn(this),this.pendingcb=0,this.constructed=!0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!e||e.emitClose!==!1,this.autoDestroy=!e||e.autoDestroy!==!1,this.errored=null,this.closed=!1,this.closeEmitted=!1,this[dt]=[]}function mn(e){e.buffered=[],e.bufferedIndex=0,e.allBuffers=!0,e.allNoop=!0}function I(e){let t=this instanceof N.Duplex;if(!t&&!fs.call(I,this))return new I(e);this._writableState=new yn(e,this,t),e&&(typeof e.write=="function"&&(this._write=e.write),typeof e.writev=="function"&&(this._writev=e.writev),typeof e.destroy=="function"&&(this._destroy=e.destroy),typeof e.final=="function"&&(this._final=e.final),typeof e.construct=="function"&&(this._construct=e.construct),e.signal&&We(e.signal,this)),N.call(this,e),Ot(this,()=>{let n=this._writableState;n.writing||Tr(this,n),Ar(this,n)})}function ds(e,t,n,r){let i=e._writableState;if(typeof n=="function")r=n,n=i.defaultEncoding;else{if(!n)n=i.defaultEncoding;else if(n!=="buffer"&&!g.isEncoding(n))throw new us(n);typeof r!="function"&&(r=Er)}if(t===null)throw new Bu;if(!i.objectMode)if(typeof t=="string")i.decodeStrings!==!1&&(t=g.from(t,n),n="buffer");else if(t instanceof g)n="buffer";else if(N._isUint8Array(t))t=N._uint8ArrayToBuffer(t),n="buffer";else throw new Lu("chunk",["string","Buffer","Uint8Array"],t);let o;return i.ending?o=new Uu:i.destroyed&&(o=new Bt("write")),o?(y.nextTick(r,o),ft(e,o,!0),o):(i.pendingcb++,Du(e,i,t,n,r))}function Du(e,t,n,r,i){let o=t.objectMode?1:n.length;t.length+=o;let s=t.length<t.highWaterMark;return s||(t.needDrain=!0),t.writing||t.corked||t.errored||!t.constructed?(t.buffered.push({chunk:n,encoding:r,callback:i}),t.allBuffers&&r!=="buffer"&&(t.allBuffers=!1),t.allNoop&&i!==Er&&(t.allNoop=!1)):(t.writelen=o,t.writecb=i,t.writing=!0,t.sync=!0,e._write(n,r,t.onwrite),t.sync=!1),s&&!t.errored&&!t.destroyed}function ls(e,t,n,r,i,o,s){t.writelen=r,t.writecb=s,t.writing=!0,t.sync=!0,t.destroyed?t.onwrite(new Bt("write")):n?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function as(e,t,n,r){--t.pendingcb,r(n),xr(t),ft(e,n)}function Iu(e,t){let n=e._writableState,r=n.sync,i=n.writecb;if(typeof i!="function"){ft(e,new cs);return}n.writing=!1,n.writecb=null,n.length-=n.writelen,n.writelen=0,t?(t.stack,n.errored||(n.errored=t),e._readableState&&!e._readableState.errored&&(e._readableState.errored=t),r?y.nextTick(as,e,n,t,i):as(e,n,t,i)):(n.buffered.length>n.bufferedIndex&&Tr(e,n),r?n.afterWriteTickInfo!==null&&n.afterWriteTickInfo.cb===i?n.afterWriteTickInfo.count++:(n.afterWriteTickInfo={count:1,cb:i,stream:e,state:n},y.nextTick(Gu,n.afterWriteTickInfo)):hs(e,n,1,i))}function Gu({stream:e,state:t,count:n,cb:r}){return t.afterWriteTickInfo=null,hs(e,t,n,r)}function hs(e,t,n,r){for(!t.ending&&!e.destroyed&&t.length===0&&t.needDrain&&(t.needDrain=!1,e.emit("drain"));n-- >0;)t.pendingcb--,r();t.destroyed&&xr(t),Ar(e,t)}function xr(e){if(e.writing)return;for(let n=e.bufferedIndex;n<e.buffered.length;++n){let{chunk:r,callback:i}=e.buffered[n],o=e.objectMode?1:r.length;e.length-=o,i(e.errored??new Bt("write"))}let t=e[dt].splice(0);for(let n=0;n<t.length;n++)t[n](e.errored??new Bt("end"));mn(e)}function Tr(e,t){if(t.corked||t.bufferProcessing||t.destroyed||!t.constructed)return;let{buffered:n,bufferedIndex:r,objectMode:i}=t,o=n.length-r;if(!o)return;let s=r;if(t.bufferProcessing=!0,o>1&&e._writev){t.pendingcb-=o-1;let l=t.allNoop?Er:f=>{for(let h=s;h<n.length;++h)n[h].callback(f)},c=t.allNoop&&s===0?n:n.slice(s);c.allBuffers=t.allBuffers,ls(e,t,!0,t.length,c,"",l),mn(t)}else{do{let{chunk:l,encoding:c,callback:f}=n[s];n[s++]=null;let h=i?1:l.length;ls(e,t,!1,h,l,c,f)}while(s<n.length&&!t.writing);s===n.length?mn(t):s>256?(n.splice(0,s),t.bufferedIndex=0):t.bufferedIndex=s}t.bufferProcessing=!1}function wr(e){return e.ending&&e.constructed&&e.length===0&&!e.errored&&e.buffered.length===0&&!e.finished&&!e.writing&&!e.errorEmitted&&!e.closeEmitted}function ju(e,t){let n=!1;function r(i){if(n){ft(e,i??cs());return}if(n=!0,t.pendingcb--,i){let o=t[dt].splice(0);for(let s=0;s<o.length;s++)o[s](i);ft(e,i,t.sync)}else wr(t)&&(t.prefinished=!0,e.emit("prefinish"),t.pendingcb++,y.nextTick(_r,e,t))}t.sync=!0,t.pendingcb++;try{let i=e._final(r);if(i!=null){let o=i.then;typeof o=="function"&&o.call(i,function(){y.nextTick(r,null)},function(s){y.nextTick(r,s)})}}catch(i){r(e,t,i)}t.sync=!1}function Hu(e,t){!t.prefinished&&!t.finalCalled&&(typeof e._final=="function"&&!t.destroyed?(t.finalCalled=!0,ju(e,t)):(t.prefinished=!0,e.emit("prefinish")))}function Ar(e,t,n){wr(t)&&(Hu(e,t),t.pendingcb===0&&wr(t)&&(t.pendingcb++,n?y.nextTick(_r,e,t):_r(e,t)))}function _r(e,t){t.pendingcb--,t.finished=!0;let n=t[dt].splice(0);for(let r=0;r<n.length;r++)n[r]();if(e.emit("finish"),t.autoDestroy){let r=e._readableState;(!r||r.autoDestroy&&(r.endEmitted||r.readable===!1))&&e.destroy()}}var K,Lu,$u,cs,Pu,Bt,Fu,Bu,Uu,us,ft,dt,fs,Wu,Rr=M(()=>{"use strict";T();Kt();ze();se();un();pr();ke();Ct();re();K=I,{ERR_INVALID_ARG_TYPE:Lu,ERR_METHOD_NOT_IMPLEMENTED:$u,ERR_MULTIPLE_CALLBACK:cs,ERR_STREAM_CANNOT_PIPE:Pu,ERR_STREAM_DESTROYED:Bt,ERR_STREAM_ALREADY_FINISHED:Fu,ERR_STREAM_NULL_VALUES:Bu,ERR_STREAM_WRITE_AFTER_END:Uu,ERR_UNKNOWN_ENCODING:us}=F,{errorOrDestroy:ft}=at;Object.setPrototypeOf(I.prototype,N.prototype);Object.setPrototypeOf(I,N);dt=Symbol("kOnFinished");yn.prototype.getBuffer=function(){return this.buffered.slice(this.bufferedIndex)};Object.defineProperty(yn.prototype,"bufferedRequestCount",{get(){return this.buffered.length-this.bufferedIndex}});fs=Function.prototype[Symbol.hasInstance];Object.defineProperty(I,Symbol.hasInstance,{value:function(e){return fs.call(this,e)?!0:this!==I?!1:e&&e._writableState instanceof yn}});I.prototype.pipe=function(){ft(this,new Pu)};I.prototype.write=function(e,t,n){return ds(this,e,t,n)===!0};I.prototype.cork=function(){this._writableState.corked++};I.prototype.uncork=function(){let e=this._writableState;e.corked&&(e.corked--,e.writing||Tr(this,e))};I.prototype.setDefaultEncoding=function(t){if(typeof t=="string"&&(t=t.toLowerCase()),!g.isEncoding(t))throw new us(t);return this._writableState.defaultEncoding=t,this};I.prototype._write=function(e,t,n){if(this._writev)this._writev([{chunk:e,encoding:t}],n);else throw new $u("_write()")};I.prototype._writev=null;I.prototype.end=function(e,t,n){let r=this._writableState;typeof e=="function"?(n=e,e=null,t=null):typeof t=="function"&&(n=t,t=null);let i;if(e!=null){let o=ds(this,e,t);o instanceof Error&&(i=o)}return r.corked&&(r.corked=1,this.uncork()),i||(!r.errored&&!r.ending?(r.ending=!0,Ar(this,r,!0),r.ended=!0):r.finished?i=new Fu("end"):r.destroyed&&(i=new Bt("end"))),typeof n=="function"&&(i||r.finished?y.nextTick(n,i):r[dt].push(n)),this};Object.defineProperties(I.prototype,{destroyed:{get(){return this._writableState?this._writableState.destroyed:!1},set(e){this._writableState&&(this._writableState.destroyed=e)}},writable:{get(){let e=this._writableState;return!!e&&e.writable!==!1&&!e.destroyed&&!e.errored&&!e.ending&&!e.ended},set(e){this._writableState&&(this._writableState.writable=!!e)}},writableFinished:{get(){return this._writableState?this._writableState.finished:!1}},writableObjectMode:{get(){return this._writableState?this._writableState.objectMode:!1}},writableBuffer:{get(){return this._writableState&&this._writableState.getBuffer()}},writableEnded:{get(){return this._writableState?this._writableState.ending:!1}},writableNeedDrain:{get(){let e=this._writableState;return e?!e.destroyed&&!e.ending&&e.needDrain:!1}},writableHighWaterMark:{get(){return this._writableState&&this._writableState.highWaterMark}},writableCorked:{get(){return this._writableState?this._writableState.corked:0}},writableLength:{get(){return this._writableState&&this._writableState.length}}});Wu=kt;I.prototype.destroy=function(e,t){let n=this._writableState;return!n.destroyed&&(n.bufferedIndex<n.buffered.length||n[dt].length)&&y.nextTick(xr,n),Wu.call(this,e,t),this};I.prototype._undestroy=Mt;I.prototype._destroy=function(e,t){t(e)};I.prototype[ee.captureRejectionSymbol]=function(e){this.destroy(e)}});function U(e){if(!(this instanceof U))return new U(e);be.call(this,e),K.call(this,e),e?(this.allowHalfOpen=e.allowHalfOpen!==!1,e.readable===!1&&(this._readableState.readable=!1,this._readableState.ended=!0,this._readableState.endEmitted=!0),e.writable===!1&&(this._writableState.writable=!1,this._writableState.ending=!0,this._writableState.ended=!0,this._writableState.finished=!0)):this.allowHalfOpen=!0}function vr(e,t){if(uo(e))return e;if(fe(e))return bn({readable:e});if(Ee(e))return bn({writable:e});if(Z(e))return bn({writable:!1,readable:!1});if(typeof e=="function"){let{value:r,write:i,final:o,destroy:s}=Vu(e);if(he(r))return ut(Ze,r,{objectMode:!0,write:i,final:o,destroy:s});let l=r?.then;if(typeof l=="function"){let c,f=l.call(r,h=>{if(h!=null)throw new gs("nully","body",h)},h=>{z(c,h)});return c=new Ze({objectMode:!0,readable:!1,write:i,final(h){o(async()=>{try{await f,y.nextTick(h,null)}catch(a){y.nextTick(h,a)}})},destroy:s})}throw new gs("Iterable, AsyncIterable or AsyncFunction",t,r)}if(he(e))return ut(Ze,e,{objectMode:!0,writable:!1});if(typeof e?.writable=="object"||typeof e?.readable=="object"){let r=e?.readable?fe(e?.readable)?e?.readable:vr(e.readable):void 0,i=e?.writable?Ee(e?.writable)?e?.writable:vr(e.writable):void 0;return bn({readable:r,writable:i})}let n=e?.then;if(typeof n=="function"){let r;return n.call(e,i=>{i!=null&&r.push(i),r.push(null)},i=>{z(r,i)}),r=new Ze({objectMode:!0,writable:!1,read(){}})}throw new zu(t,["Blob","ReadableStream","WritableStream","Stream","Iterable","AsyncIterable","Function","{ readable, writable } pair","Promise"],e)}function Vu(e){let{promise:t,resolve:n}=ps(),r=new Pe,i=r.signal;return{value:e(async function*(){for(;;){let{chunk:s,done:l,cb:c}=await t;if(y.nextTick(c),l)return;if(i.aborted)throw new oe;yield s,{promise:t,resolve:n}=ps()}}(),{signal:i}),write(s,l,c){n({chunk:s,done:!1,cb:c})},final(s){n({done:!0,cb:s})},destroy(s,l){r.abort(),l(s)}}}function bn(e){let t=e.readable&&typeof e.readable.read!="function"?be.wrap(e.readable):e.readable,n=e.writable,r=!!xe(t),i=!!Te(n),o,s,l,c,f;function h(a){let u=c;c=null,u?u(a):a?f.destroy(a):!r&&!i&&f.destroy()}return f=new Ze({readableObjectMode:!!t?.readableObjectMode,writableObjectMode:!!n?.writableObjectMode,readable:r,writable:i}),i&&(Q(n,a=>{i=!1,a&&z(t,a),h(a)}),f._write=function(a,u,d){n.write(a,u)?d():o=d},f._final=function(a){n.end(),s=a},n.on("drain",function(){if(o){let a=o;o=null,a()}}),n.on("finish",function(){if(s){let a=s;s=null,a()}})),r&&(Q(t,a=>{r=!1,a&&z(t,a),h(a)}),t.on("readable",function(){if(l){let a=l;l=null,a()}}),t.on("end",function(){f.push(null)}),f._read=function(){for(;;){let a=t.read();if(a===null){l=f._read;return}if(!f.push(a))return}}),f._destroy=function(a,u){!a&&c!==null&&(a=new oe),l=null,o=null,s=null,c===null?u(a):(c=u,z(n,a),z(t,a))},f}function ps(){let e,t;return{promise:new Promise((r,i)=>{e=r,t=i}),resolve:e,reject:t}}var zu,gs,Ze,Ut=M(()=>{"use strict";T();hr();ze();lt();se();gr();pn();$e();Rr();re();({ERR_INVALID_ARG_TYPE:zu,ERR_INVALID_RETURN_VALUE:gs}=F);Object.setPrototypeOf(U.prototype,be.prototype);Object.setPrototypeOf(U,be);for(let e of Object.keys(K.prototype))U.prototype[e]||(U.prototype[e]=K.prototype[e]);Object.defineProperties(U.prototype,{writable:Object.getOwnPropertyDescriptor(K.prototype,"writable"),writableHighWaterMark:Object.getOwnPropertyDescriptor(K.prototype,"writableHighWaterMark"),writableObjectMode:Object.getOwnPropertyDescriptor(K.prototype,"writableObjectMode"),writableBuffer:Object.getOwnPropertyDescriptor(K.prototype,"writableBuffer"),writableLength:Object.getOwnPropertyDescriptor(K.prototype,"writableLength"),writableFinished:Object.getOwnPropertyDescriptor(K.prototype,"writableFinished"),writableCorked:Object.getOwnPropertyDescriptor(K.prototype,"writableCorked"),writableEnded:Object.getOwnPropertyDescriptor(K.prototype,"writableEnded"),writableNeedDrain:Object.getOwnPropertyDescriptor(K.prototype,"writableNeedDrain"),destroyed:{get(){return this._readableState===void 0||this._writableState===void 0?!1:this._readableState.destroyed&&this._writableState.destroyed},set(e){this._readableState&&this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}});U.from=function(e){return vr(e,"body")};Ze=class extends U{constructor(t){super(t),t?.readable===!1&&(this._readableState.readable=!1,this._readableState.ended=!0,this._readableState.endEmitted=!0),t?.writable===!1&&(this._writableState.writable=!1,this._writableState.ending=!0,this._writableState.ended=!0,this._writableState.finished=!0)}}});function X(e){if(!(this instanceof X))return new X(e);U.call(this,e),this._readableState.sync=!1,this[ht]=null,e&&(typeof e.transform=="function"&&(this._transform=e.transform),typeof e.flush=="function"&&(this._flush=e.flush)),this.on("prefinish",qu)}function Nr(e){let t=!1;if(typeof this._flush=="function"&&!this.destroyed){let n=this._flush((r,i)=>{if(t=!0,r){e?e(r):this.destroy(r);return}i!=null&&this.push(i),this.push(null),e&&e()});if(n!=null)try{let r=n.then;typeof r=="function"&&r.call(n,i=>{t||(i!=null&&this.push(i),this.push(null),e&&y.nextTick(e))},i=>{e?y.nextTick(e,i):y.nextTick(()=>this.destroy(i))})}catch(r){y.nextTick(()=>this.destroy(r))}}else this.push(null),e&&e()}function qu(){this._final!==Nr&&Nr.call(this)}var Yu,ht,kr=M(()=>{"use strict";T();Ut();se();re();({ERR_METHOD_NOT_IMPLEMENTED:Yu}=F);Object.setPrototypeOf(X.prototype,U.prototype);Object.setPrototypeOf(X,U);ht=Symbol("kCallback");X.prototype._final=Nr;X.prototype._transform=function(e,t,n){throw new Yu("_transform()")};X.prototype._write=function(e,t,n){let r=this._readableState,i=this._writableState,o=r.length,s=!1,l=this._transform(e,t,(c,f)=>{if(s=!0,c){n(c);return}f!=null&&this.push(f),i.ended||o===r.length||r.length<r.highWaterMark||r.length===0?n():this[ht]=n});if(l!==void 0&&l!=null)try{let c=l.then;typeof c=="function"&&c.call(l,f=>{s||(f!=null&&this.push(f),i.ended||o===r.length||r.length<r.highWaterMark||r.length===0?y.nextTick(n):this[ht]=n)},f=>{y.nextTick(n,f)})}catch(c){y.nextTick(n,c)}};X.prototype._read=function(){if(this[ht]){let e=this[ht];this[ht]=null,e()}}});function de(e){if(!(this instanceof de))return new de(e);X.call(this,e)}var Mr=M(()=>{"use strict";T();kr();Object.setPrototypeOf(de.prototype,X.prototype);Object.setPrototypeOf(de,X);de.prototype._transform=function(e,t,n){n(null,e)}});function ms(e,t,n,r){r=He(r);let i=!1;return e.on("close",()=>{i=!0}),Q(e,{readable:t,writable:n},o=>{i=!o;let s=e._readableState;o&&o.code==="ERR_STREAM_PREMATURE_CLOSE"&&t&&s&&s.ended&&!s.errored&&!s.errorEmitted?e.once("end",r).once("error",r):r(o)}),o=>{i||(i=!0,z(e,o),r(o||new Xu("pipe")))}}function Zu(e){return e.pop()}function ys(e){if(he(e))return e;if(fe(e))return Qu(e);throw new Ju("val",["Readable","Iterable","AsyncIterable"],e)}async function*Qu(e){yield*be.prototype[Symbol.asyncIterator].call(e)}async function bs(e,t,n){let r,i=null,o=c=>{if(c&&(r=c),i){let f=i;i=null,f()}},s=()=>new Promise((c,f)=>{r?f(r):i=()=>{r?f(r):c()}});t.on("drain",o);let l=Q(t,{readable:!1},o);try{t.writableNeedDrain&&await s();for await(let c of e)t.write(c)||await s();t.end(),await s(),n()}catch(c){n(r!==c?je(r,c):c)}finally{l(),t.off("drain",o)}}function Cr(...e){let t=He(Zu(e));return Array.isArray(e[0])&&e.length===1&&(e=e[0]),Lr(e,t)}function Lr(e,t,n){if(e.length<2)throw new Ku("streams");let r=new Pe,i=r.signal,o=n?.signal;function s(){u(new oe)}o?.addEventListener("abort",s);let l,c,f=[],h=0;function a(p){u(p,--h===0)}function u(p,m){if(p&&(!l||l.code==="ERR_STREAM_PREMATURE_CLOSE")&&(l=p),!(!l&&!m)){for(;f.length;)f.shift()(l);o?.removeEventListener("abort",s),r.abort(),m&&t(l,c)}}let d;for(let p=0;p<e.length;p++){let m=e[p],S=p<e.length-1,_=p>0;if(Z(m)&&(h++,f.push(ms(m,S,_,a))),p===0)if(typeof m=="function"){if(d=m({signal:i}),!he(d))throw new Or("Iterable, AsyncIterable or Stream","source",d)}else he(m)||fe(m)?d=m:d=U.from(m);else if(typeof m=="function")if(d=ys(d),d=m(d,{signal:i}),S){if(!he(d,!0))throw new Or("AsyncIterable",`transform[${p-1}]`,d)}else{let x=new de({objectMode:!0}),A=d?.then;if(typeof A=="function")A.call(d,O=>{c=O,x.end(O)},O=>{x.destroy(O)});else if(he(d,!0))h++,bs(d,x,a);else throw new Or("AsyncIterable or Promise","destination",d);d=x,h++,f.push(ms(d,!1,!0,a))}else Z(m)?(fe(d)?(d.pipe(m),(m===y.stdout||m===y.stderr)&&d.on("end",()=>m.end())):(d=ys(d),h++,bs(d,m,a)),d=m):d=U.from(m)}return(i?.aborted||o?.aborted)&&y.nextTick(s),d}var Ju,Or,Ku,Xu,Sn,wn=M(()=>{"use strict";T();hr();ze();Ut();lt();se();Kn();Mr();pn();$e();re();({ERR_INVALID_ARG_TYPE:Ju,ERR_INVALID_RETURN_VALUE:Or,ERR_MISSING_ARGS:Ku,ERR_STREAM_DESTROYED:Xu}=F);Sn=Cr});function _n(...e){if(e.length===0)throw new ef("streams");if(e.length===1)return U.from(e[0]);let t=[...e];if(typeof e[0]=="function"&&(e[0]=U.from(e[0])),typeof e[e.length-1]=="function"){let u=e.length-1;e[u]=U.from(e[u])}for(let u=0;u<e.length;++u)if(Z(e[u])){if(u<e.length-1&&!xe(e[u]))throw new Ss(`streams[${u}]`,t[u],"must be readable");if(u>0&&!Te(e[u]))throw new Ss(`streams[${u}]`,t[u],"must be writable")}let n,r,i,o,s;function l(u){let d=o;o=null,d?d(u):u?s.destroy(u):!a&&!h&&s.destroy()}let c=e[0],f=Cr(e,l),h=!!Te(c),a=!!xe(f);return s=new $r({writableObjectMode:!!c?.writableObjectMode,readableObjectMode:!!f?.writableObjectMode,writable:h,readable:a}),h&&(s._write=function(u,d,p){c.write(u,d)?p():n=p},s._final=function(u){c.end(),r=u},c.on("drain",function(){if(n){let u=n;n=null,u()}}),f.on("finish",function(){if(r){let u=r;r=null,u()}})),a&&(f.on("readable",function(){if(i){let u=i;i=null,u()}}),f.on("end",function(){s.push(null)}),s._read=function(){for(;;){let u=f.read();if(u===null){i=s._read;return}if(!s.push(u))return}}),s._destroy=function(u,d){!u&&o!==null&&(u=new oe),i=null,n=null,r=null,o===null?d(u):(o=d,z(f,u))},s}var Ss,ef,$r,ws=M(()=>{"use strict";T();ze();Ut();se();wn();$e();({ERR_INVALID_ARG_VALUE:Ss,ERR_MISSING_ARGS:ef}=F),$r=class extends U{constructor(t){super(t),t?.readable===!1&&(this._readableState.readable=!1,this._readableState.ended=!0,this._readableState.endEmitted=!0),t?.writable===!1&&(this._writableState.writable=!1,this._writableState.ending=!0,this._writableState.ended=!0,this._writableState.finished=!0)}}});var En={};tt(En,{finished:()=>Fr,pipeline:()=>Pr});function Pr(...e){return new Promise((t,n)=>{let r,i=e[e.length-1];i&&typeof i=="object"&&!Z(i)&&!he(i)&&(r=e.pop().signal),Lr(e,(o,s)=>{o?n(o):t(s)},{signal:r})})}function Fr(e,t){return new Promise((n,r)=>{Q(e,t,i=>{i?r(i):n()})})}var _s=M(()=>{"use strict";T();lt();wn();$e()});var Es=M(()=>{"use strict";T();Kt();ws();ze();Ut();lt();un();Mr();wn();_s();pn();kr();$e();Rr();ke();st();N.isDisturbed=Qn;N.Readable=be;N.Writable=K;N.Duplex=U;N.Transform=X;N.PassThrough=de;N.pipeline=Sn;N.addAbortSignal=We;N.finished=Q;N.destroy=z;N.compose=_n;Object.defineProperty(N,"promises",{configurable:!0,enumerable:!0,get(){return En}});Object.defineProperty(Sn,qt.custom,{enumerable:!0,get(){return Pr}});Object.defineProperty(Q,qt.custom,{enumerable:!0,get(){return Fr}});N.Stream=N;N._isUint8Array=io.isUint8Array;N._uint8ArrayToBuffer=g.from});var Br,xs=M(()=>{"use strict";T();Es();Br=N});var mt={};tt(mt,{Stats:()=>Gt,constants:()=>pe,createReadStream:()=>qs,createWriteStream:()=>Js,default:()=>vf,list:()=>Ws,lstat:()=>rl,lstatSync:()=>oi,readFile:()=>Xs,readFileSync:()=>ei,readdir:()=>Ks,readdirSync:()=>si,readlink:()=>Qs,readlinkSync:()=>ti,rmdir:()=>el,rmdirSync:()=>ni,stat:()=>nl,statSync:()=>ii,unlink:()=>tl,unlinkSync:()=>ri,writeFile:()=>Zs,writeFileSync:()=>Qr});function Wr(e,t){let{FindFirstFileW:n,FindNextFileW:r,FindClose:i}=ve(),o=Memory.alloc(592),s=n(Memory.allocUtf16String(e),o),l=s.value;l.equals(It)&&Re(s.lastError);try{do t(o);while(r(l,o)!==0)}finally{i(l)}}function Qr(e,t,n={}){typeof n=="string"&&(n={encoding:n});let{encoding:r=null}=n,i;typeof t=="string"?r!==null&&!Gs(r)?i=g.from(t,r).buffer:i=t:i=t.buffer;let o=new File(e,"wb");try{o.write(i)}finally{o.close()}}function Ts(e,t){let n=Memory.alloc(Ef),r=e(Memory.allocUtf8String(t),n);return r.value!==0&&Qe(r.errno),Vr(t,n)}function Is(e,t,n){if(Gs(n))return e.readUtf8String(t);let r=g.from(e.readByteArray(t));return n!==null?r.toString(n):r}function Gs(e){return e==="utf8"||e==="utf-8"}function si(e){let t=[];return js(e,n=>{let r=Tn(n,"d_name");t.push(r)}),t}function Ws(e){let t=Object.keys(Hs).filter(r=>!r.startsWith("d_")),n=[];return js(e,r=>{let i=Tn(r,"d_name"),o=Tn(r,"d_type",Dn.join(e,i)),s={};for(let l of t)s[l]=Tn(r,l);n.push({name:i,type:o,...s})}),n}function Tn(e,t,...n){let r=Hs[t],[i,o]=r,l=(typeof o=="string"?NativePointer.prototype["read"+o]:o).call(e.add(i),...n);return l instanceof Int64||l instanceof UInt64?l.valueOf():l}function zr(){if(Ur!==null)return Ur;let e;if(Be)e=vs.windows;else{let t=Ne(),n=t.stat64??t.__xstat64,r;if(gt==="darwin"?r=`darwin-${pt*8}`:(r=`${gt}-${Process.arch}`,pt===4&&n!==void 0&&(r+="-stat64")),e=vs[r],e===void 0)throw new Error("Current OS/arch combo is not yet supported; please open a PR");e._stat=n??t.stat,e._lstat=t.lstat64??t.__lxstat64??t.lstat}return Ur=e,e}function Vr(e,t){return new Proxy(new Gt,{has(n,r){return typeof r=="symbol"?r in n:Ns(r)},get(n,r,i){switch(r){case"prototype":return;case"constructor":case"toString":return n[r];case"hasOwnProperty":return Ns;case"valueOf":return i;case"buffer":return t;default:{let o;return typeof r=="symbol"||(o=n[r])!==void 0?o:Yr.call(i,r,e)}}},set(n,r,i,o){return!1},ownKeys(n){return Array.from(zs)},getOwnPropertyDescriptor(n,r){return{writable:!1,configurable:!0,enumerable:!0}}})}function Ns(e){return zs.has(e)}function Yr(e,t){let n=zr().fields[e];if(n===void 0){if(e==="birthtime")return Yr.call(this,"ctime",t);let l=e.lastIndexOf("Ms");return l===e.length-2?Yr.call(this,e.substring(0,l),t).getTime():void 0}let[r,i]=n,s=(typeof i=="string"?NativePointer.prototype["read"+i]:i).call(this.buffer.add(r),t);return s instanceof Int64||s instanceof UInt64?s.valueOf():s}function Vs(e){let t=this.readU32(),n=!1;(t&uf)!==0&&Wr(e,o=>{let s=o.add(36).readU32();n=s===ff||s===df});let r=(t&cf)!==0,i;return n?i=Zr:r?i=Xr:i=Kr,r?i|=493:i|=420,i}function et(){let r=BigInt(this.readU64().toString()).valueOf()/10000n-11644473600000n;return new Date(parseInt(r.toString()))}function qr(){let e=this.readU32(),t=this.add(4).readU32();return uint64(e).shl(32).or(t)}function ce(){let e=this.readU32(),n=this.add(4).readU32()/1e6;return new Date(e*1e3+n)}function Se(){let e=this.readU64().valueOf(),n=this.add(8).readU64().valueOf()/1e6;return new Date(e*1e3+n)}function Dt(){return 0}function ks(){return 1}function Re(e){throw li(e)}function Qe(e){throw ai(e)}function li(e){let i=Memory.alloc(512);return ve().FormatMessageW(4608,NULL,e,0,i,256,NULL),new Error(i.readUtf16String())}function ai(e){let t=Ne().strerror(e).readUtf8String();return new Error(t)}function Ue(e){return function(...t){let n=t.length-1,r=t.slice(0,n),i=t[n];y.nextTick(function(){try{let o=e(...r);i(null,o)}catch(o){i(o)}})}}function xf(){let e=SystemFunction,t=NativeFunction;return Ys([["CreateFileW",e,"pointer",["pointer","uint","uint","pointer","uint","uint","pointer"]],["DeleteFileW",e,"uint",["pointer"]],["GetFileSizeEx",e,"uint",["pointer","pointer"]],["ReadFile",e,"uint",["pointer","pointer","uint","pointer","pointer"]],["RemoveDirectoryW",e,"uint",["pointer"]],["CloseHandle",t,"uint",["pointer"]],["FindFirstFileW",e,"pointer",["pointer","pointer"]],["FindNextFileW",t,"uint",["pointer","pointer"]],["FindClose",t,"uint",["pointer"]],["GetFileAttributesExW",e,"uint",["pointer","uint","pointer"]],["GetFinalPathNameByHandleW",e,"uint",["pointer","pointer","uint","uint"]],["FormatMessageW",t,"uint",["uint","pointer","uint","uint","pointer","uint","pointer"]]])}function Tf(){let e=SystemFunction,t=NativeFunction;return Ys([["open",e,"int",["pointer","int","...","int"]],["close",t,"int",["int"]],["lseek",t,Os,["int",Os,"int"]],["read",e,Jr,["int","pointer",Ms]],["opendir",e,"pointer",["pointer"]],["opendir$INODE64",e,"pointer",["pointer"]],["closedir",t,"int",["pointer"]],["readdir",t,"pointer",["pointer"]],["readdir$INODE64",t,"pointer",["pointer"]],["readlink",e,Jr,["pointer","pointer",Ms]],["rmdir",e,"int",["pointer"]],["unlink",e,"int",["pointer"]],["stat",e,"int",["pointer","pointer"]],["stat64",e,"int",["pointer","pointer"]],["__xstat64",e,"int",["int","pointer","pointer"],Cs],["lstat",e,"int",["pointer","pointer"]],["lstat64",e,"int",["pointer","pointer"]],["__lxstat64",e,"int",["int","pointer","pointer"],Cs],["strerror",t,"pointer",["int"]]])}function Cs(e,t,n){return e(_f,t,n)}function Ys(e){return e.reduce((t,n)=>(Rf(t,n),t),{})}function Rf(e,t){let[n]=t;Object.defineProperty(e,n,{configurable:!0,get(){let[,r,i,o,s]=t;Be&&Dr===null&&(Dr=Process.getModuleByName("kernel32.dll"));let l=null,c=Be?Dr.findExportByName(n):Module.findGlobalExportByName(n);return c!==null&&(l=new r(c,i,o,Af)),s!==void 0&&(l=s.bind(null,l)),Object.defineProperty(e,n,{value:l}),l}})}function qs(e){return new jr(e)}function Js(e){return new Hr(e)}function il(e){let t,n=!1;return function(...r){return n||(t=e(...r),n=!0),t}}var ve,Ne,gt,pt,Be,Fe,Kr,Xr,Ls,$s,Ps,Zr,Fs,tf,nf,pe,It,Bs,rf,Ir,of,sf,lf,Gr,af,cf,uf,ff,df,Us,hf,gf,pf,mf,yf,bf,jr,Hr,xn,Ds,Sf,js,ei,ti,ni,ri,ii,oi,As,Hs,zs,Rs,vs,wf,_f,Ur,Ef,Gt,Jr,Ms,Os,Dr,Af,Ks,Xs,Zs,Qs,el,tl,nl,rl,vf,yt=M(()=>{"use strict";T();ke();In();re();xs();ve=il(xf),Ne=il(Tf),gt=Process.platform,pt=Process.pointerSize,Be=gt==="windows",Fe=61440,Kr=32768,Xr=16384,Ls=8192,$s=24576,Ps=4096,Zr=40960,Fs=49152,tf={S_IFMT:Fe,S_IFREG:Kr,S_IFDIR:Xr,S_IFCHR:Ls,S_IFBLK:$s,S_IFIFO:Ps,S_IFLNK:Zr,S_IFSOCK:Fs,S_IRWXU:448,S_IRUSR:256,S_IWUSR:128,S_IXUSR:64,S_IRWXG:56,S_IRGRP:32,S_IWGRP:16,S_IXGRP:8,S_IRWXO:7,S_IROTH:4,S_IWOTH:2,S_IXOTH:1,DT_UNKNOWN:0,DT_FIFO:1,DT_CHR:2,DT_DIR:4,DT_BLK:6,DT_REG:8,DT_LNK:10,DT_SOCK:12,DT_WHT:14},nf={darwin:{O_RDONLY:0,O_WRONLY:1,O_RDWR:2,O_CREAT:512,O_EXCL:2048,O_NOCTTY:131072,O_TRUNC:1024,O_APPEND:8,O_DIRECTORY:1048576,O_NOFOLLOW:256,O_SYNC:128,O_DSYNC:4194304,O_SYMLINK:2097152,O_NONBLOCK:4},linux:{O_RDONLY:0,O_WRONLY:1,O_RDWR:2,O_CREAT:64,O_EXCL:128,O_NOCTTY:256,O_TRUNC:512,O_APPEND:1024,O_DIRECTORY:65536,O_NOATIME:262144,O_NOFOLLOW:131072,O_SYNC:1052672,O_DSYNC:4096,O_DIRECT:16384,O_NONBLOCK:2048}},pe={...tf,...nf[gt]},It=-1,Bs=2147483648,rf=1073741824,Ir=1,of=2,sf=4,lf=2,Gr=3,af=128,cf=16,uf=1024,ff=2684354563,df=2684354572,Us=1073741824,hf=33554432,gf=8,pf=32,mf=0,yf=2,bf=4,jr=class extends Br.Readable{#e=null;#t=null;constructor(t){if(super({highWaterMark:4*1024*1024}),Be){let r=ve().CreateFileW(Memory.allocUtf16String(t),Bs,Ir,NULL,Gr,Us,NULL),i=r.value;if(i.equals(It)){y.nextTick(()=>{this.destroy(li(r.lastError))});return}this.#e=new Win32InputStream(i,{autoClose:!0})}else{let r=Ne().open(Memory.allocUtf8String(t),pe.O_RDONLY,0),i=r.value;if(i===-1){y.nextTick(()=>{this.destroy(ai(r.errno))});return}this.#e=new UnixInputStream(i,{autoClose:!0})}}_destroy(t,n){this.#e?.close(),this.#e=null,n(t)}_read(t){this.#t===null&&(this.#t=this.#e.read(t).then(n=>{if(this.#t=null,n.byteLength===0){this.push(null);return}this.push(g.from(n))&&this._read(t)}).catch(n=>{this.#t=null,this.destroy(n)}))}},Hr=class extends Br.Writable{#e=null;#t=null;constructor(t){if(super({highWaterMark:4*1024*1024}),Be){let r=ve().CreateFileW(Memory.allocUtf16String(t),rf,0,NULL,lf,af|Us,NULL),i=r.value;if(i.equals(It)){y.nextTick(()=>{this.destroy(li(r.lastError))});return}this.#e=new Win32OutputStream(i,{autoClose:!0})}else{let n=Ne(),r=Memory.allocUtf8String(t),i=pe.O_WRONLY|pe.O_CREAT|pe.O_TRUNC,o=pe.S_IRUSR|pe.S_IWUSR|pe.S_IRGRP|pe.S_IROTH,s=n.open(r,i,o),l=s.value;if(l===-1){y.nextTick(()=>{this.destroy(ai(s.errno))});return}this.#e=new UnixOutputStream(l,{autoClose:!0})}}_destroy(t,n){this.#e?.close(),this.#e=null,n(t)}_write(t,n,r){this.#t===null&&(this.#t=this.#e.writeAll(t).then(i=>{this.#t=null,r()}).catch(i=>{this.#t=null,r(i)}))}},xn={enumerateDirectoryEntries(e,t){Wr(e+"\\*",t)},readFileSync(e,t={}){typeof t=="string"&&(t={encoding:t});let{encoding:n=null}=t,{CreateFileW:r,GetFileSizeEx:i,ReadFile:o,CloseHandle:s}=ve(),l=r(Memory.allocUtf16String(e),Bs,Ir,NULL,Gr,0,NULL),c=l.value;c.equals(It)&&Re(l.lastError);try{let f=Memory.alloc(8),h=f,a=i(c,h);a.value===0&&Re(a.lastError);let u=h.readU64().valueOf(),d=Memory.alloc(u),p=f,m=o(c,d,u,p,NULL);if(m.value===0&&Re(m.lastError),p.readU32()!==u)throw new Error("Short read");return Is(d,u,n)}finally{s(c)}},readlinkSync(e){let{CreateFileW:t,GetFinalPathNameByHandleW:n,CloseHandle:r}=ve(),i=t(Memory.allocUtf16String(e),0,Ir|of|sf,NULL,Gr,hf,NULL),o=i.value;o.equals(It)&&Re(i.lastError);try{let s=256;for(;;){let l=Memory.alloc(s*2),{value:c,lastError:f}=n(o,l,s,0);if(c===0&&Re(f),f===gf){s*=2;continue}return l.readUtf16String().substring(4)}}finally{r(o)}},rmdirSync(e){let t=ve().RemoveDirectoryW(Memory.allocUtf16String(e));t.value===0&&Re(t.lastError)},unlinkSync(e){let t=ve().DeleteFileW(Memory.allocUtf16String(e));t.value===0&&Re(t.lastError)},statSync(e){let t=xn.lstatSync(e);if(!t.isSymbolicLink())return t;let n=xn.readlinkSync(e);return xn.lstatSync(n)},lstatSync(e){let n=Memory.alloc(36),r=ve().GetFileAttributesExW(Memory.allocUtf16String(e),0,n);if(r.value===0){if(r.lastError===pf){let i;return Wr(e,o=>{i=Memory.dup(o,36)}),Vr(e,i)}Re(r.lastError)}return Vr(e,n)}};Ds={enumerateDirectoryEntries(e,t){let{opendir:n,opendir$INODE64:r,closedir:i,readdir:o,readdir$INODE64:s}=Ne(),l=r||n,c=s||o,f=l(Memory.allocUtf8String(e)),h=f.value;h.isNull()&&Qe(f.errno);try{let a;for(;!(a=c(h)).isNull();)t(a)}finally{i(h)}},readFileSync(e,t={}){typeof t=="string"&&(t={encoding:t});let{encoding:n=null}=t,{open:r,close:i,lseek:o,read:s}=Ne(),l=r(Memory.allocUtf8String(e),pe.O_RDONLY,0),c=l.value;c===-1&&Qe(l.errno);try{let f=o(c,0,yf).valueOf();o(c,0,mf);let h=Memory.alloc(f),a,u,d;do a=s(c,h,f),u=a.value.valueOf(),d=u===-1;while(d&&a.errno===bf);if(d&&Qe(a.errno),u!==f.valueOf())throw new Error("Short read");return Is(h,f,n)}finally{i(c)}},readlinkSync(e){let t=Memory.allocUtf8String(e),n=Ds.lstatSync(e).size.valueOf(),r=Memory.alloc(n),i=Ne().readlink(t,r,n),o=i.value.valueOf();return o===-1&&Qe(i.errno),r.readUtf8String(o)},rmdirSync(e){let t=Ne().rmdir(Memory.allocUtf8String(e));t.value===-1&&Qe(t.errno)},unlinkSync(e){let t=Ne().unlink(Memory.allocUtf8String(e));t.value===-1&&Qe(t.errno)},statSync(e){return Ts(zr()._stat,e)},lstatSync(e){return Ts(zr()._lstat,e)}};Sf=Be?xn:Ds,{enumerateDirectoryEntries:js,readFileSync:ei,readlinkSync:ti,rmdirSync:ni,unlinkSync:ri,statSync:ii,lstatSync:oi}=Sf,As={windows:{d_name:[44,"Utf16String"],d_type:[0,Vs],atime:[12,et],mtime:[20,et],ctime:[4,et],size:[28,qr]},"linux-32":{d_name:[11,"Utf8String"],d_type:[10,"U8"]},"linux-64":{d_name:[19,"Utf8String"],d_type:[18,"U8"]},"darwin-32":{d_name:[21,"Utf8String"],d_type:[20,"U8"]},"darwin-64":{d_name:[21,"Utf8String"],d_type:[20,"U8"]}},Hs=Be?As.windows:As[`${gt}-${pt*8}`];zs=new Set(["dev","mode","nlink","uid","gid","rdev","blksize","ino","size","blocks","atimeMs","mtimeMs","ctimeMs","birthtimeMs","atime","mtime","ctime","birthtime"]),Rs={size:88,fields:{dev:[0,"U64"],mode:[16,"U32"],nlink:[20,"U32"],ino:[12,"U32"],uid:[24,"U32"],gid:[28,"U32"],rdev:[32,"U64"],atime:[56,ce],mtime:[64,ce],ctime:[72,ce],size:[44,"S32"],blocks:[52,"S32"],blksize:[48,"S32"]}},vs={windows:{size:36,fields:{dev:[0,Dt],mode:[0,Vs],nlink:[0,ks],ino:[0,Dt],uid:[0,Dt],gid:[0,Dt],rdev:[0,Dt],atime:[12,et],mtime:[20,et],ctime:[20,et],birthtime:[4,et],size:[28,qr],blocks:[28,qr],blksize:[0,ks]}},"darwin-32":{size:108,fields:{dev:[0,"S32"],mode:[4,"U16"],nlink:[6,"U16"],ino:[8,"U64"],uid:[16,"U32"],gid:[20,"U32"],rdev:[24,"S32"],atime:[28,ce],mtime:[36,ce],ctime:[44,ce],birthtime:[52,ce],size:[60,"S64"],blocks:[68,"S64"],blksize:[76,"S32"]}},"darwin-64":{size:144,fields:{dev:[0,"S32"],mode:[4,"U16"],nlink:[6,"U16"],ino:[8,"U64"],uid:[16,"U32"],gid:[20,"U32"],rdev:[24,"S32"],atime:[32,Se],mtime:[48,Se],ctime:[64,Se],birthtime:[80,Se],size:[96,"S64"],blocks:[104,"S64"],blksize:[112,"S32"]}},"linux-ia32":Rs,"linux-ia32-stat64":{size:96,fields:{dev:[0,"U64"],mode:[16,"U32"],nlink:[20,"U32"],ino:[88,"U64"],uid:[24,"U32"],gid:[28,"U32"],rdev:[32,"U64"],atime:[64,ce],mtime:[72,ce],ctime:[80,ce],size:[44,"S64"],blocks:[56,"S64"],blksize:[52,"S32"]}},"linux-x64":{size:144,fields:{dev:[0,"U64"],mode:[24,"U32"],nlink:[16,"U64"],ino:[8,"U64"],uid:[28,"U32"],gid:[32,"U32"],rdev:[40,"U64"],atime:[72,Se],mtime:[88,Se],ctime:[104,Se],size:[48,"S64"],blocks:[64,"S64"],blksize:[56,"S64"]}},"linux-arm":Rs,"linux-arm-stat64":{size:104,fields:{dev:[0,"U64"],mode:[16,"U32"],nlink:[20,"U32"],ino:[96,"U64"],uid:[24,"U32"],gid:[28,"U32"],rdev:[32,"U64"],atime:[72,ce],mtime:[80,ce],ctime:[88,ce],size:[48,"S64"],blocks:[64,"S64"],blksize:[56,"S32"]}},"linux-arm64":{size:128,fields:{dev:[0,"U64"],mode:[16,"U32"],nlink:[20,"U32"],ino:[8,"U64"],uid:[24,"U32"],gid:[28,"U32"],rdev:[32,"U64"],atime:[72,Se],mtime:[88,Se],ctime:[104,Se],size:[48,"S64"],blocks:[64,"S64"],blksize:[56,"S32"]}}},wf={ia32:3,x64:1,arm:3,arm64:0,mips:3},_f=wf[Process.arch],Ur=null,Ef=256;Gt=class{dev;mode;nlink;uid;gid;rdev;blksize;ino;size;blocks;atimeMs;mtimeMs;ctimeMs;birthtimeMs;atime;mtime;ctime;birthtime;buffer;isFile(){return(this.mode&Fe)===Kr}isDirectory(){return(this.mode&Fe)===Xr}isCharacterDevice(){return(this.mode&Fe)===Ls}isBlockDevice(){return(this.mode&Fe)===$s}isFIFO(){return(this.mode&Fe)===Ps}isSymbolicLink(){return(this.mode&Fe)===Zr}isSocket(){return(this.mode&Fe)===Fs}};Jr=pt===8?"int64":"int32",Ms="u"+Jr,Os=gt==="darwin"||pt===8?"int64":"int32";Dr=null,Af=Be&&pt===4?{abi:"stdcall"}:{};Ks=Ue(si),Xs=Ue(ei),Zs=Ue(Qr),Qs=Ue(ti),el=Ue(ni),tl=Ue(ri),nl=Ue(ii),rl=Ue(oi);vf={constants:pe,createReadStream:qs,createWriteStream:Js,readdir:Ks,readdirSync:si,list:Ws,readFile:Xs,readFileSync:ei,writeFile:Zs,writeFileSync:Qr,readlink:Qs,readlinkSync:ti,rmdir:el,rmdirSync:ni,unlink:tl,unlinkSync:ri,stat:nl,statSync:ii,lstat:rl,lstatSync:oi,Stats:Gt}});T();T();var w=function(e,t,n,r){var i=arguments.length,o=i<3?t:r===null?r=Object.getOwnPropertyDescriptor(t,n):r,s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")o=Reflect.decorate(e,t,n,r);else for(var l=e.length-1;l>=0;l--)(s=e[l])&&(o=(i<3?s(o):i>3?s(t,n,o):s(t,n))||o);return i>3&&o&&Object.defineProperty(t,n,o),o},E;(function(e){e.application={get dataPath(){return t("get_persistentDataPath")},get identifier(){return t("get_identifier")??t("get_bundleIdentifier")??Process.mainModule.name},get version(){return t("get_version")??ca(e.module).toString(16)}},J(e,"unityVersion",()=>{try{let r=e.$config.unityVersion??t("get_unityVersion");if(r!=null)return r}catch{}let n="69 6c 32 63 70 70";for(let r of e.module.enumerateRanges("r--").concat(Process.getRangeByAddress(e.module.base)))for(let{address:i}of Memory.scanSync(r.base,r.size,n)){for(;i.readU8()!=0;)i=i.sub(1);let o=_t.find(i.add(1).readCString());if(o!=null)return o}R("couldn't determine the Unity version, please specify it manually")},b),J(e,"unityVersionIsBelow201830",()=>_t.lt(e.unityVersion,"2018.3.0"),b),J(e,"unityVersionIsBelow202120",()=>_t.lt(e.unityVersion,"2021.2.0"),b);function t(n){let r=e.exports.resolveInternalCall(Memory.allocUtf8String("UnityEngine.Application::"+n)),i=new NativeFunction(r,"pointer",[]);return i.isNull()?null:new e.String(i()).asNullable()?.content??null}})(E||(E={}));var E;(function(e){function t(n,r){let i={int8:"System.SByte",uint8:"System.Byte",int16:"System.Int16",uint16:"System.UInt16",int32:"System.Int32",uint32:"System.UInt32",int64:"System.Int64",uint64:"System.UInt64",char:"System.Char",intptr:"System.IntPtr",uintptr:"System.UIntPtr"},o=typeof n=="boolean"?"System.Boolean":typeof n=="number"?i[r??"int32"]:n instanceof Int64?"System.Int64":n instanceof UInt64?"System.UInt64":n instanceof NativePointer?i[r??"intptr"]:R(`Cannot create boxed primitive using value of type '${typeof n}'`),s=e.corlib.class(o??R(`Unknown primitive type name '${r}'`)).alloc();return(s.tryField("m_value")??s.tryField("_pointer")??R(`Could not find primitive field in class '${o}'`)).value=n,s}e.boxed=t})(E||(E={}));var E;(function(e){e.$config={moduleName:void 0,unityVersion:void 0,exports:void 0}})(E||(E={}));var E;(function(e){function t(s,l){s=s??`${e.application.identifier}_${e.application.version}.cs`,l=l??e.application.dataPath??Process.getCurrentDir(),i(l);let c=`${l}/${s}`,f=new File(c,"w");for(let h of e.domain.assemblies){Et(`dumping ${h.name}...`);for(let a of h.image.classes)f.write(`${a}

`)}f.flush(),f.close(),Ni(`dump saved to ${c}`),o()}e.dump=t;function n(s,l=!1){s=s??`${e.application.dataPath??Process.getCurrentDir()}/${e.application.identifier}_${e.application.version}`,!l&&r(s)&&R(`directory ${s} already exists - pass ignoreAlreadyExistingDirectory = true to skip this check`);for(let c of e.domain.assemblies){Et(`dumping ${c.name}...`);let f=`${s}/${c.name.replaceAll(".","/")}.cs`;i(f.substring(0,f.lastIndexOf("/")));let h=new File(f,"w");for(let a of c.image.classes)h.write(`${a}

`);h.flush(),h.close()}Ni(`dump saved to ${s}`),o()}e.dumpTree=n;function r(s){return e.corlib.class("System.IO.Directory").method("Exists").invoke(e.string(s))}function i(s){e.corlib.class("System.IO.Directory").method("CreateDirectory").invoke(e.string(s))}function o(){Ht("this api will be removed in a future release, please use `npx frida-il2cpp-bridge dump` instead")}})(E||(E={}));var E;(function(e){function t(n="current"){let r=e.exports.threadGetCurrent();return Interceptor.attach(e.module.getExportByName("__cxa_throw"),function(i){n=="current"&&!e.exports.threadGetCurrent().equals(r)||Et(new e.Object(i[0].readPointer()))})}e.installExceptionListener=t})(E||(E={}));var E;(function(e){e.exports={get alloc(){return t("il2cpp_alloc","pointer",["size_t"])},get arrayGetLength(){return t("il2cpp_array_length","uint32",["pointer"])},get arrayNew(){return t("il2cpp_array_new","pointer",["pointer","uint32"])},get assemblyGetImage(){return t("il2cpp_assembly_get_image","pointer",["pointer"])},get classForEach(){return t("il2cpp_class_for_each","void",["pointer","pointer"])},get classFromName(){return t("il2cpp_class_from_name","pointer",["pointer","pointer","pointer"])},get classFromObject(){return t("il2cpp_class_from_system_type","pointer",["pointer"])},get classGetArrayClass(){return t("il2cpp_array_class_get","pointer",["pointer","uint32"])},get classGetArrayElementSize(){return t("il2cpp_class_array_element_size","int",["pointer"])},get classGetAssemblyName(){return t("il2cpp_class_get_assemblyname","pointer",["pointer"])},get classGetBaseType(){return t("il2cpp_class_enum_basetype","pointer",["pointer"])},get classGetDeclaringType(){return t("il2cpp_class_get_declaring_type","pointer",["pointer"])},get classGetElementClass(){return t("il2cpp_class_get_element_class","pointer",["pointer"])},get classGetFieldFromName(){return t("il2cpp_class_get_field_from_name","pointer",["pointer","pointer"])},get classGetFields(){return t("il2cpp_class_get_fields","pointer",["pointer","pointer"])},get classGetFlags(){return t("il2cpp_class_get_flags","int",["pointer"])},get classGetImage(){return t("il2cpp_class_get_image","pointer",["pointer"])},get classGetInstanceSize(){return t("il2cpp_class_instance_size","int32",["pointer"])},get classGetInterfaces(){return t("il2cpp_class_get_interfaces","pointer",["pointer","pointer"])},get classGetMethodFromName(){return t("il2cpp_class_get_method_from_name","pointer",["pointer","pointer","int"])},get classGetMethods(){return t("il2cpp_class_get_methods","pointer",["pointer","pointer"])},get classGetName(){return t("il2cpp_class_get_name","pointer",["pointer"])},get classGetNamespace(){return t("il2cpp_class_get_namespace","pointer",["pointer"])},get classGetNestedClasses(){return t("il2cpp_class_get_nested_types","pointer",["pointer","pointer"])},get classGetParent(){return t("il2cpp_class_get_parent","pointer",["pointer"])},get classGetStaticFieldData(){return t("il2cpp_class_get_static_field_data","pointer",["pointer"])},get classGetValueTypeSize(){return t("il2cpp_class_value_size","int32",["pointer","pointer"])},get classGetType(){return t("il2cpp_class_get_type","pointer",["pointer"])},get classHasReferences(){return t("il2cpp_class_has_references","bool",["pointer"])},get classInitialize(){return t("il2cpp_runtime_class_init","void",["pointer"])},get classIsAbstract(){return t("il2cpp_class_is_abstract","bool",["pointer"])},get classIsAssignableFrom(){return t("il2cpp_class_is_assignable_from","bool",["pointer","pointer"])},get classIsBlittable(){return t("il2cpp_class_is_blittable","bool",["pointer"])},get classIsEnum(){return t("il2cpp_class_is_enum","bool",["pointer"])},get classIsGeneric(){return t("il2cpp_class_is_generic","bool",["pointer"])},get classIsInflated(){return t("il2cpp_class_is_inflated","bool",["pointer"])},get classIsInterface(){return t("il2cpp_class_is_interface","bool",["pointer"])},get classIsSubclassOf(){return t("il2cpp_class_is_subclass_of","bool",["pointer","pointer","bool"])},get classIsValueType(){return t("il2cpp_class_is_valuetype","bool",["pointer"])},get domainGetAssemblyFromName(){return t("il2cpp_domain_assembly_open","pointer",["pointer","pointer"])},get domainGet(){return t("il2cpp_domain_get","pointer",[])},get domainGetAssemblies(){return t("il2cpp_domain_get_assemblies","pointer",["pointer","pointer"])},get fieldGetClass(){return t("il2cpp_field_get_parent","pointer",["pointer"])},get fieldGetFlags(){return t("il2cpp_field_get_flags","int",["pointer"])},get fieldGetName(){return t("il2cpp_field_get_name","pointer",["pointer"])},get fieldGetOffset(){return t("il2cpp_field_get_offset","int32",["pointer"])},get fieldGetStaticValue(){return t("il2cpp_field_static_get_value","void",["pointer","pointer"])},get fieldGetType(){return t("il2cpp_field_get_type","pointer",["pointer"])},get fieldSetStaticValue(){return t("il2cpp_field_static_set_value","void",["pointer","pointer"])},get free(){return t("il2cpp_free","void",["pointer"])},get gcCollect(){return t("il2cpp_gc_collect","void",["int"])},get gcCollectALittle(){return t("il2cpp_gc_collect_a_little","void",[])},get gcDisable(){return t("il2cpp_gc_disable","void",[])},get gcEnable(){return t("il2cpp_gc_enable","void",[])},get gcGetHeapSize(){return t("il2cpp_gc_get_heap_size","int64",[])},get gcGetMaxTimeSlice(){return t("il2cpp_gc_get_max_time_slice_ns","int64",[])},get gcGetUsedSize(){return t("il2cpp_gc_get_used_size","int64",[])},get gcHandleGetTarget(){return t("il2cpp_gchandle_get_target","pointer",["uint32"])},get gcHandleFree(){return t("il2cpp_gchandle_free","void",["uint32"])},get gcHandleNew(){return t("il2cpp_gchandle_new","uint32",["pointer","bool"])},get gcHandleNewWeakRef(){return t("il2cpp_gchandle_new_weakref","uint32",["pointer","bool"])},get gcIsDisabled(){return t("il2cpp_gc_is_disabled","bool",[])},get gcIsIncremental(){return t("il2cpp_gc_is_incremental","bool",[])},get gcSetMaxTimeSlice(){return t("il2cpp_gc_set_max_time_slice_ns","void",["int64"])},get gcStartIncrementalCollection(){return t("il2cpp_gc_start_incremental_collection","void",[])},get gcStartWorld(){return t("il2cpp_start_gc_world","void",[])},get gcStopWorld(){return t("il2cpp_stop_gc_world","void",[])},get getCorlib(){return t("il2cpp_get_corlib","pointer",[])},get imageGetAssembly(){return t("il2cpp_image_get_assembly","pointer",["pointer"])},get imageGetClass(){return t("il2cpp_image_get_class","pointer",["pointer","uint"])},get imageGetClassCount(){return t("il2cpp_image_get_class_count","uint32",["pointer"])},get imageGetName(){return t("il2cpp_image_get_name","pointer",["pointer"])},get initialize(){return t("il2cpp_init","void",["pointer"])},get livenessAllocateStruct(){return t("il2cpp_unity_liveness_allocate_struct","pointer",["pointer","int","pointer","pointer","pointer"])},get livenessCalculationBegin(){return t("il2cpp_unity_liveness_calculation_begin","pointer",["pointer","int","pointer","pointer","pointer","pointer"])},get livenessCalculationEnd(){return t("il2cpp_unity_liveness_calculation_end","void",["pointer"])},get livenessCalculationFromStatics(){return t("il2cpp_unity_liveness_calculation_from_statics","void",["pointer"])},get livenessFinalize(){return t("il2cpp_unity_liveness_finalize","void",["pointer"])},get livenessFreeStruct(){return t("il2cpp_unity_liveness_free_struct","void",["pointer"])},get memorySnapshotCapture(){return t("il2cpp_capture_memory_snapshot","pointer",[])},get memorySnapshotFree(){return t("il2cpp_free_captured_memory_snapshot","void",["pointer"])},get memorySnapshotGetClasses(){return t("il2cpp_memory_snapshot_get_classes","pointer",["pointer","pointer"])},get memorySnapshotGetObjects(){return t("il2cpp_memory_snapshot_get_objects","pointer",["pointer","pointer"])},get methodGetClass(){return t("il2cpp_method_get_class","pointer",["pointer"])},get methodGetFlags(){return t("il2cpp_method_get_flags","uint32",["pointer","pointer"])},get methodGetName(){return t("il2cpp_method_get_name","pointer",["pointer"])},get methodGetObject(){return t("il2cpp_method_get_object","pointer",["pointer","pointer"])},get methodGetParameterCount(){return t("il2cpp_method_get_param_count","uint8",["pointer"])},get methodGetParameterName(){return t("il2cpp_method_get_param_name","pointer",["pointer","uint32"])},get methodGetParameters(){return t("il2cpp_method_get_parameters","pointer",["pointer","pointer"])},get methodGetParameterType(){return t("il2cpp_method_get_param","pointer",["pointer","uint32"])},get methodGetReturnType(){return t("il2cpp_method_get_return_type","pointer",["pointer"])},get methodIsGeneric(){return t("il2cpp_method_is_generic","bool",["pointer"])},get methodIsInflated(){return t("il2cpp_method_is_inflated","bool",["pointer"])},get methodIsInstance(){return t("il2cpp_method_is_instance","bool",["pointer"])},get monitorEnter(){return t("il2cpp_monitor_enter","void",["pointer"])},get monitorExit(){return t("il2cpp_monitor_exit","void",["pointer"])},get monitorPulse(){return t("il2cpp_monitor_pulse","void",["pointer"])},get monitorPulseAll(){return t("il2cpp_monitor_pulse_all","void",["pointer"])},get monitorTryEnter(){return t("il2cpp_monitor_try_enter","bool",["pointer","uint32"])},get monitorTryWait(){return t("il2cpp_monitor_try_wait","bool",["pointer","uint32"])},get monitorWait(){return t("il2cpp_monitor_wait","void",["pointer"])},get objectGetClass(){return t("il2cpp_object_get_class","pointer",["pointer"])},get objectGetVirtualMethod(){return t("il2cpp_object_get_virtual_method","pointer",["pointer","pointer"])},get objectInitialize(){return t("il2cpp_runtime_object_init_exception","void",["pointer","pointer"])},get objectNew(){return t("il2cpp_object_new","pointer",["pointer"])},get objectGetSize(){return t("il2cpp_object_get_size","uint32",["pointer"])},get objectUnbox(){return t("il2cpp_object_unbox","pointer",["pointer"])},get resolveInternalCall(){return t("il2cpp_resolve_icall","pointer",["pointer"])},get stringGetChars(){return t("il2cpp_string_chars","pointer",["pointer"])},get stringGetLength(){return t("il2cpp_string_length","int32",["pointer"])},get stringNew(){return t("il2cpp_string_new","pointer",["pointer"])},get valueTypeBox(){return t("il2cpp_value_box","pointer",["pointer","pointer"])},get threadAttach(){return t("il2cpp_thread_attach","pointer",["pointer"])},get threadDetach(){return t("il2cpp_thread_detach","void",["pointer"])},get threadGetAttachedThreads(){return t("il2cpp_thread_get_all_attached_threads","pointer",["pointer"])},get threadGetCurrent(){return t("il2cpp_thread_current","pointer",[])},get threadIsVm(){return t("il2cpp_is_vm_thread","bool",["pointer"])},get typeEquals(){return t("il2cpp_type_equals","bool",["pointer","pointer"])},get typeGetClass(){return t("il2cpp_class_from_type","pointer",["pointer"])},get typeGetName(){return t("il2cpp_type_get_name","pointer",["pointer"])},get typeGetObject(){return t("il2cpp_type_get_object","pointer",["pointer"])},get typeGetTypeEnum(){return t("il2cpp_type_get_type","int",["pointer"])}},aa(e.exports,b),J(e,"memorySnapshotExports",()=>new CModule(`#include <stdint.h>
#include <string.h>

typedef struct Il2CppManagedMemorySnapshot Il2CppManagedMemorySnapshot;
typedef struct Il2CppMetadataType Il2CppMetadataType;

struct Il2CppManagedMemorySnapshot
{
  struct Il2CppManagedHeap
  {
    uint32_t section_count;
    void * sections;
  } heap;
  struct Il2CppStacks
  {
    uint32_t stack_count;
    void * stacks;
  } stacks;
  struct Il2CppMetadataSnapshot
  {
    uint32_t type_count;
    Il2CppMetadataType * types;
  } metadata_snapshot;
  struct Il2CppGCHandles
  {
    uint32_t tracked_object_count;
    void ** pointers_to_objects;
  } gc_handles;
  struct Il2CppRuntimeInformation
  {
    uint32_t pointer_size;
    uint32_t object_header_size;
    uint32_t array_header_size;
    uint32_t array_bounds_offset_in_header;
    uint32_t array_size_offset_in_header;
    uint32_t allocation_granularity;
  } runtime_information;
  void * additional_user_information;
};

struct Il2CppMetadataType
{
  uint32_t flags;
  void * fields;
  uint32_t field_count;
  uint32_t statics_size;
  uint8_t * statics;
  uint32_t base_or_element_type_index;
  char * name;
  const char * assembly_name;
  uint64_t type_info_address;
  uint32_t size;
};

uintptr_t
il2cpp_memory_snapshot_get_classes (
    const Il2CppManagedMemorySnapshot * snapshot, Il2CppMetadataType ** iter)
{
  const int zero = 0;
  const void * null = 0;

  if (iter != NULL && snapshot->metadata_snapshot.type_count > zero)
  {
    if (*iter == null)
    {
      *iter = snapshot->metadata_snapshot.types;
      return (uintptr_t) (*iter)->type_info_address;
    }
    else
    {
      Il2CppMetadataType * metadata_type = *iter + 1;

      if (metadata_type < snapshot->metadata_snapshot.types +
                              snapshot->metadata_snapshot.type_count)
      {
        *iter = metadata_type;
        return (uintptr_t) (*iter)->type_info_address;
      }
    }
  }
  return 0;
}

void **
il2cpp_memory_snapshot_get_objects (
    const Il2CppManagedMemorySnapshot * snapshot, uint32_t * size)
{
  *size = snapshot->gc_handles.tracked_object_count;
  return snapshot->gc_handles.pointers_to_objects;
}
`),b);function t(n,r,i){let o=e.$config.exports?.[n]?.()??e.module.findExportByName(n)??e.memorySnapshotExports[n],s=new NativeFunction(o??NULL,r,i);return s.isNull()?new Proxy(s,{get(l,c){let f=l[c];return typeof f=="function"?f.bind(l):f},apply(){o==null?R(`couldn't resolve export ${n}`):o.isNull()&&R(`export ${n} points to NULL IL2CPP library has likely been stripped, obfuscated, or customized`)}}):s}})(E||(E={}));var E;(function(e){function t(r){return i=>i instanceof e.Class?r.isAssignableFrom(i):r.isAssignableFrom(i.class)}e.is=t;function n(r){return i=>i instanceof e.Class?i.equals(r):i.class.equals(r)}e.isExactly=n})(E||(E={}));var E;(function(e){e.gc={get heapSize(){return e.exports.gcGetHeapSize()},get isEnabled(){return!e.exports.gcIsDisabled()},get isIncremental(){return!!e.exports.gcIsIncremental()},get maxTimeSlice(){return e.exports.gcGetMaxTimeSlice()},get usedHeapSize(){return e.exports.gcGetUsedSize()},set isEnabled(t){t?e.exports.gcEnable():e.exports.gcDisable()},set maxTimeSlice(t){e.exports.gcSetMaxTimeSlice(t)},choose(t){let n=[],r=(o,s)=>{for(let l=0;l<s;l++)n.push(new e.Object(o.add(l*Process.pointerSize).readPointer()))},i=new NativeCallback(r,"void",["pointer","int","pointer"]);if(e.unityVersionIsBelow202120){let o=new NativeCallback(()=>{},"void",[]),s=e.exports.livenessCalculationBegin(t,0,i,NULL,o,o);e.exports.livenessCalculationFromStatics(s),e.exports.livenessCalculationEnd(s)}else{let o=(c,f)=>!c.isNull()&&f.compare(0)==0?(e.free(c),NULL):e.alloc(f),s=new NativeCallback(o,"pointer",["pointer","size_t","pointer"]);this.stopWorld();let l=e.exports.livenessAllocateStruct(t,0,i,NULL,s);e.exports.livenessCalculationFromStatics(l),e.exports.livenessFinalize(l),this.startWorld(),e.exports.livenessFreeStruct(l)}return n},collect(t){e.exports.gcCollect(t<0?0:t>2?2:t)},collectALittle(){e.exports.gcCollectALittle()},startWorld(){return e.exports.gcStartWorld()},startIncrementalCollection(){return e.exports.gcStartIncrementalCollection()},stopWorld(){return e.exports.gcStopWorld()}}})(E||(E={}));var Fn;(function(e){J(e,"apiLevel",()=>{let n=t("ro.build.version.sdk");return n?parseInt(n):null},b);function t(n){let r=Process.findModuleByName("libc.so")?.findExportByName("__system_property_get");if(r){let i=new NativeFunction(r,"void",["pointer","pointer"]),o=Memory.alloc(92).writePointer(NULL);return i(Memory.allocUtf8String(n),o),o.readCString()??void 0}}})(Fn||(Fn={}));function R(e){let t=new Error(e);throw t.name="Il2CppError",t.stack=t.stack?.replace(/^(Il2Cpp)?Error/,"\x1B[0m\x1B[38;5;9mil2cpp\x1B[0m")?.replace(/\n    at (.+) \((.+):(.+)\)/,"\x1B[3m\x1B[2m")?.concat("\x1B[0m"),t}function Ht(e){globalThis.console.log(`\x1B[38;5;11mil2cpp\x1B[0m: ${e}`)}function Ni(e){globalThis.console.log(`\x1B[38;5;10mil2cpp\x1B[0m: ${e}`)}function Et(e){globalThis.console.log(`\x1B[38;5;12mil2cpp\x1B[0m: ${e}`)}function aa(e,t,n=Object.getOwnPropertyDescriptors(e)){for(let r in n)n[r]=t(e,r,n[r]);return Object.defineProperties(e,n),e}function J(e,t,n,r){globalThis.Object.defineProperty(e,t,r?.(e,t,{get:n,configurable:!0})??{get:n,configurable:!0})}function ki(e){let t=3735928559,n=1103547991;for(let r=0,i;r<e.length;r++)i=e.charCodeAt(r),t=Math.imul(t^i,2654435761),n=Math.imul(n^i,1597334677);return t=Math.imul(t^t>>>16,2246822507),t^=Math.imul(n^n>>>13,3266489909),n=Math.imul(n^n>>>16,2246822507),n^=Math.imul(t^t>>>13,3266489909),4294967296*(2097151&n)+(t>>>0)}function ca(e){return ki(e.enumerateExports().sort((t,n)=>t.name.localeCompare(n.name)).map(t=>t.name+t.address.sub(e.base)).join(""))}function b(e,t,n){let r=n.get;if(!r)throw new Error("@lazy can only be applied to getter accessors");return n.get=function(){let i=r.call(this);return Object.defineProperty(this,t,{value:i,configurable:n.configurable,enumerable:n.enumerable,writable:!1}),i},n}var H=class{handle;constructor(t){t instanceof NativePointer?this.handle=t:this.handle=t.handle}equals(t){return this.handle.equals(t.handle)}isNull(){return this.handle.isNull()}asNullable(){return this.isNull()?null:this}};function ua(e){return Object.keys(e).reduce((t,n)=>(t[t[n]]=n,t),e)}NativePointer.prototype.offsetOf=function(e,t){t??=512;for(let n=0;t>0?n<t:n<-t;n++)if(e(t>0?this.add(n):this.sub(n)))return n;return null};function wt(e){let t=[],n=Memory.alloc(Process.pointerSize),r=e(n);for(;!r.isNull();)t.push(r),r=e(n);return t}function Bn(e){let t=Memory.alloc(Process.pointerSize),n=e(t);if(n.isNull())return[];let r=new Array(t.readInt());for(let i=0;i<r.length;i++)r[i]=n.add(i*Process.pointerSize).readPointer();return r}function xt(e){return new Proxy(e,{cache:new Map,construct(t,n){let r=n[0].toUInt32();return this.cache.has(r)||this.cache.set(r,new t(n[0])),this.cache.get(r)}})}var _t;(function(e){let t=/(6\d{3}|20\d{2}|\d)\.(\d)\.(\d{1,2})(?:[abcfp]|rc){0,2}\d?/;function n(s){return s?.match(t)?.[0]}e.find=n;function r(s,l){return o(s,l)>=0}e.gte=r;function i(s,l){return o(s,l)<0}e.lt=i;function o(s,l){let c=s.match(t),f=l.match(t);for(let h=1;h<=3;h++){let a=Number(c?.[h]??-1),u=Number(f?.[h]??-1);if(a>u)return 1;if(a<u)return-1}return 0}})(_t||(_t={}));var E;(function(e){function t(l=Process.pointerSize){return e.exports.alloc(l)}e.alloc=t;function n(l){return e.exports.free(l)}e.free=n;function r(l,c){switch(c.enumValue){case e.Type.Enum.BOOLEAN:return!!l.readS8();case e.Type.Enum.BYTE:return l.readS8();case e.Type.Enum.UBYTE:return l.readU8();case e.Type.Enum.SHORT:return l.readS16();case e.Type.Enum.USHORT:return l.readU16();case e.Type.Enum.INT:return l.readS32();case e.Type.Enum.UINT:return l.readU32();case e.Type.Enum.CHAR:return l.readU16();case e.Type.Enum.LONG:return l.readS64();case e.Type.Enum.ULONG:return l.readU64();case e.Type.Enum.FLOAT:return l.readFloat();case e.Type.Enum.DOUBLE:return l.readDouble();case e.Type.Enum.NINT:case e.Type.Enum.NUINT:return l.readPointer();case e.Type.Enum.POINTER:return new e.Pointer(l.readPointer(),c.class.baseType);case e.Type.Enum.VALUE_TYPE:return new e.ValueType(l,c);case e.Type.Enum.OBJECT:case e.Type.Enum.CLASS:return new e.Object(l.readPointer());case e.Type.Enum.GENERIC_INSTANCE:return c.class.isValueType?new e.ValueType(l,c):new e.Object(l.readPointer());case e.Type.Enum.STRING:return new e.String(l.readPointer());case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return new e.Array(l.readPointer())}R(`couldn't read the value from ${l} using an unhandled or unknown type ${c.name} (${c.enumValue}), please file an issue`)}e.read=r;function i(l,c,f){switch(f.enumValue){case e.Type.Enum.BOOLEAN:return l.writeS8(+c);case e.Type.Enum.BYTE:return l.writeS8(c);case e.Type.Enum.UBYTE:return l.writeU8(c);case e.Type.Enum.SHORT:return l.writeS16(c);case e.Type.Enum.USHORT:return l.writeU16(c);case e.Type.Enum.INT:return l.writeS32(c);case e.Type.Enum.UINT:return l.writeU32(c);case e.Type.Enum.CHAR:return l.writeU16(c);case e.Type.Enum.LONG:return l.writeS64(c);case e.Type.Enum.ULONG:return l.writeU64(c);case e.Type.Enum.FLOAT:return l.writeFloat(c);case e.Type.Enum.DOUBLE:return l.writeDouble(c);case e.Type.Enum.NINT:case e.Type.Enum.NUINT:case e.Type.Enum.POINTER:case e.Type.Enum.STRING:case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return l.writePointer(c);case e.Type.Enum.VALUE_TYPE:return Memory.copy(l,c,f.class.valueTypeSize),l;case e.Type.Enum.OBJECT:case e.Type.Enum.CLASS:case e.Type.Enum.GENERIC_INSTANCE:return c instanceof e.ValueType?(Memory.copy(l,c,f.class.valueTypeSize),l):l.writePointer(c)}R(`couldn't write value ${c} to ${l} using an unhandled or unknown type ${f.name} (${f.enumValue}), please file an issue`)}e.write=i;function o(l,c){if(globalThis.Array.isArray(l)){let f=Memory.alloc(c.class.valueTypeSize),h=c.class.fields.filter(a=>!a.isStatic);for(let a=0;a<h.length;a++){let u=o(l[a],h[a].type);i(f.add(h[a].offset).sub(e.Object.headerSize),u,h[a].type)}return new e.ValueType(f,c)}else if(l instanceof NativePointer){if(c.isByReference)return new e.Reference(l,c);switch(c.enumValue){case e.Type.Enum.POINTER:return new e.Pointer(l,c.class.baseType);case e.Type.Enum.STRING:return new e.String(l);case e.Type.Enum.CLASS:case e.Type.Enum.GENERIC_INSTANCE:case e.Type.Enum.OBJECT:return new e.Object(l);case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return new e.Array(l);default:return l}}else return c.enumValue==e.Type.Enum.BOOLEAN?!!l:c.enumValue==e.Type.Enum.VALUE_TYPE&&c.class.isEnum?o([l],c):l}e.fromFridaValue=o;function s(l){if(typeof l=="boolean")return+l;if(l instanceof e.ValueType){if(l.type.class.isEnum)return l.field("value__").value;{let c=l.type.class.fields.filter(f=>!f.isStatic).map(f=>s(f.bind(l).value));return c.length==0?[0]:c}}else return l}e.toFridaValue=s})(E||(E={}));var E;(function(e){J(e,"module",()=>n()??R("Could not find IL2CPP module"));async function t(i=!1){let o=n()??await new Promise(s=>{let[l,c]=r(),f=setTimeout(()=>{Ht(`after 10 seconds, IL2CPP module '${l}' has not been loaded yet, is the app running?`)},1e4),h=Process.attachModuleObserver({onAdded(a){(a.name==l||c&&a.name==c)&&(clearTimeout(f),setImmediate(()=>{s(a),h.detach()}))}})});return Reflect.defineProperty(e,"module",{value:o}),e.exports.getCorlib().isNull()?await new Promise(s=>{let l=Interceptor.attach(e.exports.initialize,{onLeave(){l.detach(),i?s(!0):setImmediate(()=>s(!1))}})}):!1}e.initialize=t;function n(){let[i,o]=r();return Process.findModuleByName(i)??Process.findModuleByName(o??i)??(Process.platform=="darwin"?Process.findModuleByAddress(DebugSymbol.fromName("il2cpp_init").address):void 0)??void 0}function r(){if(e.$config.moduleName)return[e.$config.moduleName];switch(Process.platform){case"linux":return[Fn.apiLevel?"libil2cpp.so":"GameAssembly.so"];case"windows":return["GameAssembly.dll"];case"darwin":return["UnityFramework","GameAssembly.dylib"]}R(`${Process.platform} is not supported yet`)}})(E||(E={}));var E;(function(e){async function t(n,r="bind"){let i=null;try{let o=await e.initialize(r=="main");if(r=="main"&&!o)return t(()=>e.mainThread.schedule(n),"free");e.currentThread==null&&(i=e.domain.attach()),r=="bind"&&i!=null&&Script.bindWeak(globalThis,()=>i?.detach());let s=n();return s instanceof Promise?await s:s}catch(o){return Script.nextTick(s=>{throw s},o),Promise.reject(o)}finally{r=="free"&&i!=null&&i.detach()}}e.perform=t})(E||(E={}));var E;(function(e){class t{#e={depth:0,buffer:[],history:new Set,flush:()=>{if(this.#e.depth==0){let o=`
${this.#e.buffer.join(`
`)}
`;if(this.#f)Et(o);else{let s=ki(o);this.#e.history.has(s)||(this.#e.history.add(s),Et(o))}this.#e.buffer.length=0}}};#t=e.mainThread.id;#f=!1;#d;#c=[];#u;#n;#r;#i;#o;#s;#l;#a;constructor(o){this.#d=o}thread(o){return this.#t=o.id,this}verbose(o){return this.#f=o,this}domain(){return this.#u=e.domain,this}assemblies(...o){return this.#n=o,this}classes(...o){return this.#r=o,this}methods(...o){return this.#i=o,this}filterAssemblies(o){return this.#o=o,this}filterClasses(o){return this.#s=o,this}filterMethods(o){return this.#l=o,this}filterParameters(o){return this.#a=o,this}and(){let o=u=>{if(this.#a==null){this.#c.push(u);return}for(let d of u.parameters)if(this.#a(d)){this.#c.push(u);break}},s=u=>{for(let d of u)o(d)},l=u=>{if(this.#l==null){s(u.methods);return}for(let d of u.methods)this.#l(d)&&o(d)},c=u=>{for(let d of u)l(d)},f=u=>{if(this.#s==null){c(u.image.classes);return}for(let d of u.image.classes)this.#s(d)&&l(d)},h=u=>{for(let d of u)f(d)},a=u=>{if(this.#o==null){h(u.assemblies);return}for(let d of u.assemblies)this.#o(d)&&f(d)};return this.#i?s(this.#i):this.#r?c(this.#r):this.#n?h(this.#n):this.#u&&a(this.#u),this.#n=void 0,this.#r=void 0,this.#i=void 0,this.#o=void 0,this.#s=void 0,this.#l=void 0,this.#a=void 0,this}attach(){for(let o of this.#c)if(!o.virtualAddress.isNull())try{this.#d(o,this.#e,this.#t)}catch(s){switch(s.message){case/unable to intercept function at \w+; please file a bug/.exec(s.message)?.input:case"already replaced this function":break;default:throw s}}}}e.Tracer=t;function n(i=!1){let o=()=>(l,c,f)=>{let h=l.relativeVirtualAddress.toString(16).padStart(8,"0");Interceptor.attach(l.virtualAddress,{onEnter(){this.threadId==f&&c.buffer.push(`\x1B[2m0x${h}\x1B[0m ${"\u2502 ".repeat(c.depth++)}\u250C\u2500\x1B[35m${l.class.type.name}::\x1B[1m${l.name}\x1B[0m\x1B[0m`)},onLeave(){this.threadId==f&&(c.buffer.push(`\x1B[2m0x${h}\x1B[0m ${"\u2502 ".repeat(--c.depth)}\u2514\u2500\x1B[33m${l.class.type.name}::\x1B[1m${l.name}\x1B[0m\x1B[0m`),c.flush())}})},s=()=>(l,c,f)=>{let h=l.relativeVirtualAddress.toString(16).padStart(8,"0"),a=+!l.isStatic|+e.unityVersionIsBelow201830,u=function(...p){if(this.threadId==f){let S=l.isStatic?void 0:new e.Parameter("this",-1,l.class.type),_=S?[S].concat(l.parameters):l.parameters;c.buffer.push(`\x1B[2m0x${h}\x1B[0m ${"\u2502 ".repeat(c.depth++)}\u250C\u2500\x1B[35m${l.class.type.name}::\x1B[1m${l.name}\x1B[0m\x1B[0m(${_.map(x=>`\x1B[32m${x.name}\x1B[0m = \x1B[31m${e.fromFridaValue(p[x.position+a],x.type)}\x1B[0m`).join(", ")})`)}let m=l.nativeFunction(...p);return this.threadId==f&&(c.buffer.push(`\x1B[2m0x${h}\x1B[0m ${"\u2502 ".repeat(--c.depth)}\u2514\u2500\x1B[33m${l.class.type.name}::\x1B[1m${l.name}\x1B[0m\x1B[0m${m==null?"":` = \x1B[36m${e.fromFridaValue(m,l.returnType)}`}\x1B[0m`),c.flush()),m};l.revert();let d=new NativeCallback(u,l.returnType.fridaAlias,l.fridaSignature);Interceptor.replace(l.virtualAddress,d)};return new e.Tracer(i?s():o())}e.trace=n;function r(i){let o=e.domain.assemblies.flatMap(c=>c.image.classes.flatMap(f=>f.methods.filter(h=>!h.virtualAddress.isNull()))).sort((c,f)=>c.virtualAddress.compare(f.virtualAddress)),s=c=>{let f=0,h=o.length-1;for(;f<=h;){let a=Math.floor((f+h)/2),u=o[a].virtualAddress.compare(c);if(u==0)return o[a];u>0?h=a-1:f=a+1}return o[h]},l=()=>(c,f,h)=>{Interceptor.attach(c.virtualAddress,function(){if(this.threadId==h){let a=globalThis.Thread.backtrace(this.context,i);a.unshift(c.virtualAddress);for(let u of a)if(u.compare(e.module.base)>0&&u.compare(e.module.base.add(e.module.size))<0){let d=s(u);if(d){let p=u.sub(d.virtualAddress);p.compare(4095)<0&&f.buffer.push(`\x1B[2m0x${d.relativeVirtualAddress.toString(16).padStart(8,"0")}\x1B[0m\x1B[2m+0x${p.toString(16).padStart(3,"0")}\x1B[0m ${d.class.type.name}::\x1B[1m${d.name}\x1B[0m`)}}f.flush()}})};return new e.Tracer(l())}e.backtrace=r})(E||(E={}));var E;(function(e){class t extends H{static get headerSize(){return e.corlib.class("System.Array").instanceSize}get elements(){let o=e.string("v").object.method("ToCharArray",0).invoke().handle.offsetOf(s=>s.readS16()==118)??R("couldn't find the elements offset in the native array struct");return J(e.Array.prototype,"elements",function(){return new e.Pointer(this.handle.add(o),this.elementType)},b),this.elements}get elementSize(){return this.elementType.class.arrayElementSize}get elementType(){return this.object.class.type.class.baseType}get length(){return e.exports.arrayGetLength(this)}get object(){return new e.Object(this)}get(i){return(i<0||i>=this.length)&&R(`cannot get element at index ${i} as the array length is ${this.length}`),this.elements.get(i)}set(i,o){(i<0||i>=this.length)&&R(`cannot set element at index ${i} as the array length is ${this.length}`),this.elements.set(i,o)}toString(){return this.isNull()?"null":`[${this.elements.read(this.length,0)}]`}*[Symbol.iterator](){for(let i=0;i<this.length;i++)yield this.elements.get(i)}}w([b],t.prototype,"elementSize",null),w([b],t.prototype,"elementType",null),w([b],t.prototype,"length",null),w([b],t.prototype,"object",null),w([b],t,"headerSize",null),e.Array=t;function n(r,i){let o=typeof i=="number"?i:i.length,s=new e.Array(e.exports.arrayNew(r,o));return globalThis.Array.isArray(i)&&s.elements.write(i),s}e.array=n})(E||(E={}));var E;(function(e){let t=class extends H{get image(){if(e.exports.assemblyGetImage.isNull()){let r=this.object.tryMethod("GetType",1)?.invoke(e.string("<Module>"))?.asNullable()?.tryMethod("get_Module")?.invoke()??this.object.tryMethod("GetModules",1)?.invoke(!1)?.get(0)??R(`couldn't find the runtime module object of assembly ${this.name}`);return new e.Image(r.field("_impl").value)}return new e.Image(e.exports.assemblyGetImage(this))}get name(){return this.image.name.replace(".dll","")}get object(){for(let r of e.domain.object.method("GetAssemblies",1).invoke(!1))if(r.field("_mono_assembly").value.equals(this))return r;R("couldn't find the object of the native assembly struct")}};w([b],t.prototype,"name",null),w([b],t.prototype,"object",null),t=w([xt],t),e.Assembly=t})(E||(E={}));var E;(function(e){let t=class extends H{get actualInstanceSize(){let r=e.corlib.class("System.String"),i=r.handle.offsetOf(o=>o.readInt()==r.instanceSize-2)??R("couldn't find the actual instance size offset in the native class struct");return J(e.Class.prototype,"actualInstanceSize",function(){return this.handle.add(i).readS32()},b),this.actualInstanceSize}get arrayClass(){return new e.Class(e.exports.classGetArrayClass(this,1))}get arrayElementSize(){return e.exports.classGetArrayElementSize(this)}get assemblyName(){return e.exports.classGetAssemblyName(this).readUtf8String().replace(".dll","")}get declaringClass(){return new e.Class(e.exports.classGetDeclaringType(this)).asNullable()}get baseType(){return new e.Type(e.exports.classGetBaseType(this)).asNullable()}get elementClass(){return new e.Class(e.exports.classGetElementClass(this)).asNullable()}get fields(){return wt(r=>e.exports.classGetFields(this,r)).map(r=>new e.Field(r))}get flags(){return e.exports.classGetFlags(this)}get fullName(){return this.namespace?`${this.namespace}.${this.name}`:this.name}get genericClass(){let r=this.image.tryClass(this.fullName)?.asNullable();return r?.equals(this)?null:r??null}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let r=this.type.object.method("GetGenericArguments").invoke();return globalThis.Array.from(r).map(i=>new e.Class(e.exports.classFromObject(i)))}get hasReferences(){return!!e.exports.classHasReferences(this)}get hasStaticConstructor(){let r=this.tryMethod(".cctor");return r!=null&&!r.virtualAddress.isNull()}get image(){return new e.Image(e.exports.classGetImage(this))}get instanceSize(){return e.exports.classGetInstanceSize(this)}get isAbstract(){return!!e.exports.classIsAbstract(this)}get isBlittable(){return!!e.exports.classIsBlittable(this)}get isEnum(){return!!e.exports.classIsEnum(this)}get isGeneric(){return!!e.exports.classIsGeneric(this)}get isInflated(){return!!e.exports.classIsInflated(this)}get isInterface(){return!!e.exports.classIsInterface(this)}get isStruct(){return this.isValueType&&!this.isEnum}get isValueType(){return!!e.exports.classIsValueType(this)}get interfaces(){return wt(r=>e.exports.classGetInterfaces(this,r)).map(r=>new e.Class(r))}get methods(){return wt(r=>e.exports.classGetMethods(this,r)).map(r=>new e.Method(r))}get name(){return e.exports.classGetName(this).readUtf8String()}get namespace(){return e.exports.classGetNamespace(this).readUtf8String()||void 0}get nestedClasses(){return wt(r=>e.exports.classGetNestedClasses(this,r)).map(r=>new e.Class(r))}get parent(){return new e.Class(e.exports.classGetParent(this)).asNullable()}get pointerClass(){return new e.Class(e.exports.classFromObject(this.type.object.method("MakePointerType").invoke()))}get rank(){let r=0,i=this.name;for(let o=this.name.length-1;o>0;o--){let s=i[o];if(s=="]")r++;else{if(s=="["||r==0)break;if(s==",")r++;else break}}return r}get staticFieldsData(){return e.exports.classGetStaticFieldData(this)}get valueTypeSize(){return e.exports.classGetValueTypeSize(this,NULL)}get type(){return new e.Type(e.exports.classGetType(this))}alloc(){return new e.Object(e.exports.objectNew(this))}field(r){return this.tryField(r)??R(`couldn't find field ${r} in class ${this.type.name}`)}*hierarchy(r){let i=r?.includeCurrent??!0?this:this.parent;for(;i;)yield i,i=i.parent}inflate(...r){this.isGeneric||R(`cannot inflate class ${this.type.name} as it has no generic parameters`),this.generics.length!=r.length&&R(`cannot inflate class ${this.type.name} as it needs ${this.generics.length} generic parameter(s), not ${r.length}`);let i=r.map(l=>l.type.object),o=e.array(e.corlib.class("System.Type"),i),s=this.type.object.method("MakeGenericType",1).invoke(o);return new e.Class(e.exports.classFromObject(s))}initialize(){return e.exports.classInitialize(this),this}isAssignableFrom(r){return!!e.exports.classIsAssignableFrom(this,r)}isSubclassOf(r,i){return!!e.exports.classIsSubclassOf(this,r,+i)}method(r,i=-1){return this.tryMethod(r,i)??R(`couldn't find method ${r} in class ${this.type.name}`)}nested(r){return this.tryNested(r)??R(`couldn't find nested class ${r} in class ${this.type.name}`)}new(){let r=this.alloc(),i=Memory.alloc(Process.pointerSize);e.exports.objectInitialize(r,i);let o=i.readPointer();return o.isNull()||R(new e.Object(o).toString()),r}tryField(r){return new e.Field(e.exports.classGetFieldFromName(this,Memory.allocUtf8String(r))).asNullable()}tryMethod(r,i=-1){return new e.Method(e.exports.classGetMethodFromName(this,Memory.allocUtf8String(r),i)).asNullable()}tryNested(r){return this.nestedClasses.find(i=>i.name==r)}toString(){let r=[this.parent].concat(this.interfaces);return`// ${this.assemblyName}
${this.isEnum?"enum":this.isStruct?"struct":this.isInterface?"interface":"class"} ${this.type.name}${r?` : ${r.map(i=>i?.type.name).join(", ")}`:""}
{
    ${this.fields.join(`
    `)}
    ${this.methods.join(`
    `)}
}`}static enumerate(r){let i=new NativeCallback(o=>r(new e.Class(o)),"void",["pointer","pointer"]);return e.exports.classForEach(i,NULL)}};w([b],t.prototype,"arrayClass",null),w([b],t.prototype,"arrayElementSize",null),w([b],t.prototype,"assemblyName",null),w([b],t.prototype,"declaringClass",null),w([b],t.prototype,"baseType",null),w([b],t.prototype,"elementClass",null),w([b],t.prototype,"fields",null),w([b],t.prototype,"flags",null),w([b],t.prototype,"fullName",null),w([b],t.prototype,"generics",null),w([b],t.prototype,"hasReferences",null),w([b],t.prototype,"hasStaticConstructor",null),w([b],t.prototype,"image",null),w([b],t.prototype,"instanceSize",null),w([b],t.prototype,"isAbstract",null),w([b],t.prototype,"isBlittable",null),w([b],t.prototype,"isEnum",null),w([b],t.prototype,"isGeneric",null),w([b],t.prototype,"isInflated",null),w([b],t.prototype,"isInterface",null),w([b],t.prototype,"isValueType",null),w([b],t.prototype,"interfaces",null),w([b],t.prototype,"methods",null),w([b],t.prototype,"name",null),w([b],t.prototype,"namespace",null),w([b],t.prototype,"nestedClasses",null),w([b],t.prototype,"parent",null),w([b],t.prototype,"pointerClass",null),w([b],t.prototype,"rank",null),w([b],t.prototype,"staticFieldsData",null),w([b],t.prototype,"valueTypeSize",null),w([b],t.prototype,"type",null),t=w([xt],t),e.Class=t})(E||(E={}));var E;(function(e){function t(n,r){let i=e.corlib.class("System.Delegate"),o=e.corlib.class("System.MulticastDelegate");i.isAssignableFrom(n)||R(`cannot create a delegate for ${n.type.name} as it's a non-delegate class`),(n.equals(i)||n.equals(o))&&R(`cannot create a delegate for neither ${i.type.name} nor ${o.type.name}, use a subclass instead`);let s=n.alloc(),l=s.handle.toString(),c=s.tryMethod("Invoke")??R(`cannot create a delegate for ${n.type.name}, there is no Invoke method`);s.method(".ctor").invoke(s,c.handle);let f=c.wrap(r);return s.field("method_ptr").value=f,s.field("invoke_impl").value=f,e._callbacksToKeepAlive[l]=f,s}e.delegate=t,e._callbacksToKeepAlive={}})(E||(E={}));var E;(function(e){let t=class extends H{get assemblies(){let r=Bn(i=>e.exports.domainGetAssemblies(this,i));if(r.length==0){let i=this.object.method("GetAssemblies").overload().invoke();r=globalThis.Array.from(i).map(o=>o.field("_mono_assembly").value)}return r.map(i=>new e.Assembly(i))}get object(){return e.corlib.class("System.AppDomain").method("get_CurrentDomain").invoke()}assembly(r){return this.tryAssembly(r)??R(`couldn't find assembly ${r}`)}attach(){return new e.Thread(e.exports.threadAttach(this))}tryAssembly(r){return new e.Assembly(e.exports.domainGetAssemblyFromName(this,Memory.allocUtf8String(r))).asNullable()}};w([b],t.prototype,"assemblies",null),w([b],t.prototype,"object",null),t=w([xt],t),e.Domain=t,J(e,"domain",()=>new e.Domain(e.exports.domainGet()),b)})(E||(E={}));var E;(function(e){class t extends H{get class(){return new e.Class(e.exports.fieldGetClass(this))}get flags(){return e.exports.fieldGetFlags(this)}get isLiteral(){return(this.flags&64)!=0}get isStatic(){return(this.flags&16)!=0}get isThreadStatic(){let r=e.corlib.class("System.AppDomain").field("type_resolve_in_progress").offset;return J(e.Field.prototype,"isThreadStatic",function(){return this.offset==r},b),this.isThreadStatic}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return e.exports.fieldGetName(this).readUtf8String()}get offset(){return e.exports.fieldGetOffset(this)}get type(){return new e.Type(e.exports.fieldGetType(this))}get value(){this.isStatic||R(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`);let r=Memory.alloc(Process.pointerSize);return e.exports.fieldGetStaticValue(this.handle,r),e.read(r,this.type)}set value(r){this.isStatic||R(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`),(this.isThreadStatic||this.isLiteral)&&R(`cannot write the value of field ${this.name} as it's thread static or literal`);let i=r instanceof e.Object&&this.type.class.isValueType?r.unbox():r instanceof H?r.handle:r instanceof NativePointer?r:e.write(Memory.alloc(this.type.class.valueTypeSize),r,this.type);e.exports.fieldSetStaticValue(this.handle,i)}toString(){return`${this.isThreadStatic?"[ThreadStatic] ":""}${this.isStatic?"static ":""}${this.type.name} ${this.name}${this.isLiteral?` = ${this.type.class.isEnum?e.read(this.value.handle,this.type.class.baseType):this.value}`:""};${this.isThreadStatic||this.isLiteral?"":` // 0x${this.offset.toString(16)}`}`}bind(r){this.isStatic&&R(`cannot bind static field ${this.class.type.name}::${this.name} to an instance`);let i=this.offset-(r instanceof e.ValueType?e.Object.headerSize:0);return new Proxy(this,{get(o,s){return s=="value"?e.read(r.handle.add(i),o.type):Reflect.get(o,s)},set(o,s,l){return s=="value"?(e.write(r.handle.add(i),l,o.type),!0):Reflect.set(o,s,l)}})}}w([b],t.prototype,"class",null),w([b],t.prototype,"flags",null),w([b],t.prototype,"isLiteral",null),w([b],t.prototype,"isStatic",null),w([b],t.prototype,"isThreadStatic",null),w([b],t.prototype,"modifier",null),w([b],t.prototype,"name",null),w([b],t.prototype,"offset",null),w([b],t.prototype,"type",null),e.Field=t})(E||(E={}));var E;(function(e){class t{handle;constructor(r){this.handle=r}get target(){return new e.Object(e.exports.gcHandleGetTarget(this.handle)).asNullable()}free(){return e.exports.gcHandleFree(this.handle)}}e.GCHandle=t})(E||(E={}));var E;(function(e){let t=class extends H{get assembly(){return new e.Assembly(e.exports.imageGetAssembly(this))}get classCount(){return e.unityVersionIsBelow201830?this.classes.length:e.exports.imageGetClassCount(this)}get classes(){if(e.unityVersionIsBelow201830){let r=this.assembly.object.method("GetTypes").invoke(!1),i=globalThis.Array.from(r,s=>new e.Class(e.exports.classFromObject(s))),o=this.tryClass("<Module>");return o&&i.unshift(o),i}else return globalThis.Array.from(globalThis.Array(this.classCount),(r,i)=>new e.Class(e.exports.imageGetClass(this,i)))}get name(){return e.exports.imageGetName(this).readUtf8String()}class(r){return this.tryClass(r)??R(`couldn't find class ${r} in assembly ${this.name}`)}tryClass(r){let i=r.lastIndexOf("."),o=Memory.allocUtf8String(i==-1?"":r.slice(0,i)),s=Memory.allocUtf8String(r.slice(i+1));return new e.Class(e.exports.classFromName(this,o,s)).asNullable()}};w([b],t.prototype,"assembly",null),w([b],t.prototype,"classCount",null),w([b],t.prototype,"classes",null),w([b],t.prototype,"name",null),t=w([xt],t),e.Image=t,J(e,"corlib",()=>new e.Image(e.exports.getCorlib()),b)})(E||(E={}));var E;(function(e){class t extends H{static capture(){return new e.MemorySnapshot}constructor(i=e.exports.memorySnapshotCapture()){super(i)}get classes(){return wt(i=>e.exports.memorySnapshotGetClasses(this,i)).map(i=>new e.Class(i))}get objects(){return Bn(i=>e.exports.memorySnapshotGetObjects(this,i)).filter(i=>!i.isNull()).map(i=>new e.Object(i))}free(){e.exports.memorySnapshotFree(this)}}w([b],t.prototype,"classes",null),w([b],t.prototype,"objects",null),e.MemorySnapshot=t;function n(r){let i=e.MemorySnapshot.capture(),o=r(i);return i.free(),o}e.memorySnapshot=n})(E||(E={}));var E;(function(e){class t extends H{get class(){return new e.Class(e.exports.methodGetClass(this))}get flags(){return e.exports.methodGetFlags(this,NULL)}get implementationFlags(){let i=Memory.alloc(Process.pointerSize);return e.exports.methodGetFlags(this,i),i.readU32()}get fridaSignature(){let i=[];for(let o of this.parameters)i.push(o.type.fridaAlias);return(!this.isStatic||e.unityVersionIsBelow201830)&&i.unshift("pointer"),this.isInflated&&i.push("pointer"),i}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let i=this.object.method("GetGenericArguments").invoke();return globalThis.Array.from(i).map(o=>new e.Class(e.exports.classFromObject(o)))}get isExternal(){return(this.implementationFlags&4096)!=0}get isGeneric(){return!!e.exports.methodIsGeneric(this)}get isInflated(){return!!e.exports.methodIsInflated(this)}get isStatic(){return!e.exports.methodIsInstance(this)}get isSynchronized(){return(this.implementationFlags&32)!=0}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return e.exports.methodGetName(this).readUtf8String()}get nativeFunction(){return new NativeFunction(this.virtualAddress,this.returnType.fridaAlias,this.fridaSignature)}get object(){return new e.Object(e.exports.methodGetObject(this,NULL))}get parameterCount(){return e.exports.methodGetParameterCount(this)}get parameters(){return globalThis.Array.from(globalThis.Array(this.parameterCount),(i,o)=>{let s=e.exports.methodGetParameterName(this,o).readUtf8String(),l=e.exports.methodGetParameterType(this,o);return new e.Parameter(s,o,new e.Type(l))})}get relativeVirtualAddress(){return this.virtualAddress.sub(e.module.base)}get returnType(){return new e.Type(e.exports.methodGetReturnType(this))}get virtualAddress(){let i=e.corlib.class("System.Reflection.Module").initialize().field("FilterTypeName").value,o=i.field("method_ptr").value,l=i.field("method").value.offsetOf(c=>c.readPointer().equals(o))??R("couldn't find the virtual address offset in the native method struct");return J(e.Method.prototype,"virtualAddress",function(){return this.handle.add(l).readPointer()},b),e.corlib.class("System.Reflection.Module").method(".cctor").invoke(),this.virtualAddress}set implementation(i){try{Interceptor.replace(this.virtualAddress,this.wrap(i))}catch(o){switch(o.message){case"access violation accessing 0x0":R(`couldn't set implementation for method ${this.name} as it has a NULL virtual address`);case/unable to intercept function at \w+; please file a bug/.exec(o.message)?.input:Ht(`couldn't set implementation for method ${this.name} as it may be a thunk`);break;case"already replaced this function":Ht(`couldn't set implementation for method ${this.name} as it has already been replaced by a thunk`);break;default:throw o}}}inflate(...i){if(!this.isGeneric||this.generics.length!=i.length){for(let c of this.overloads())if(c.isGeneric&&c.generics.length==i.length)return c.inflate(...i);R(`could not find inflatable signature of method ${this.name} with ${i.length} generic parameter(s)`)}let o=i.map(c=>c.type.object),s=e.array(e.corlib.class("System.Type"),o),l=this.object.method("MakeGenericMethod",1).invoke(s);return new e.Method(l.field("mhandle").value)}invoke(...i){return this.isStatic||R(`cannot invoke non-static method ${this.name} as it must be invoked throught a Il2Cpp.Object, not a Il2Cpp.Class`),this.invokeRaw(NULL,...i)}invokeRaw(i,...o){let s=o.map(e.toFridaValue);(!this.isStatic||e.unityVersionIsBelow201830)&&s.unshift(i),this.isInflated&&s.push(this.handle);try{let l=this.nativeFunction(...s);return e.fromFridaValue(l,this.returnType)}catch(l){switch(l==null&&R("an unexpected native invocation exception occurred, this is due to parameter types mismatch"),l.message){case"bad argument count":R(`couldn't invoke method ${this.name} as it needs ${this.parameterCount} parameter(s), not ${o.length}`);case"expected a pointer":case"expected number":case"expected array with fields":R(`couldn't invoke method ${this.name} using incorrect parameter types`)}throw l}}overload(...i){return this.tryOverload(...i)??R(`couldn't find overloaded method ${this.name}(${i.map(s=>s instanceof e.Class?s.type.name:s)})`)}*overloads(){for(let i of this.class.hierarchy())for(let o of i.methods)this.name==o.name&&(yield o)}parameter(i){return this.tryParameter(i)??R(`couldn't find parameter ${i} in method ${this.name}`)}revert(){Interceptor.revert(this.virtualAddress),Interceptor.flush()}tryOverload(...i){let o=i.length*1,s=i.length*2,l;e:for(let c of this.overloads()){if(c.parameterCount!=i.length)continue;let f=0,h=0;for(let a of c.parameters){let u=i[h];if(u instanceof e.Class)if(a.type.is(u.type))f+=2;else if(a.type.class.isAssignableFrom(u))f+=1;else continue e;else if(a.type.name==u)f+=2;else continue e;h++}if(!(f<o)){if(f==s)return c;if(l==null||f>l[0])l=[f,c];else if(f==l[0]){let a=0;for(let u of l[1].parameters){if(u.type.class.isAssignableFrom(c.parameters[a].type.class)){l=[f,c];continue e}a++}}}}return l?.[1]}tryParameter(i){return this.parameters.find(o=>o.name==i)}toString(){return`${this.isStatic?"static ":""}${this.returnType.name} ${this.name}${this.generics.length>0?`<${this.generics.map(i=>i.type.name).join(",")}>`:""}(${this.parameters.join(", ")});${this.virtualAddress.isNull()?"":` // 0x${this.relativeVirtualAddress.toString(16).padStart(8,"0")}`}`}bind(i){return this.isStatic&&R(`cannot bind static method ${this.class.type.name}::${this.name} to an instance`),new Proxy(this,{get(o,s,l){switch(s){case"invoke":let c=i instanceof e.ValueType?o.class.isValueType?i.handle.sub(n()?e.Object.headerSize:0):R(`cannot invoke method ${o.class.type.name}::${o.name} against a value type, you must box it first`):o.class.isValueType?i.handle.add(n()?0:e.Object.headerSize):i.handle;return o.invokeRaw.bind(o,c);case"overloads":return function*(){for(let h of o[s]())h.isStatic||(yield h)};case"inflate":case"overload":case"tryOverload":let f=Reflect.get(o,s).bind(l);return function(...h){return f(...h)?.bind(i)}}return Reflect.get(o,s)}})}wrap(i){let o=+!this.isStatic|+e.unityVersionIsBelow201830;return new NativeCallback((...s)=>{let l=this.isStatic?this.class:this.class.isValueType?new e.ValueType(s[0].add(n()?e.Object.headerSize:0),this.class.type):new e.Object(s[0]),c=this.parameters.map((h,a)=>e.fromFridaValue(s[a+o],h.type)),f=i.call(l,...c);return e.toFridaValue(f)},this.returnType.fridaAlias,this.fridaSignature)}}w([b],t.prototype,"class",null),w([b],t.prototype,"flags",null),w([b],t.prototype,"implementationFlags",null),w([b],t.prototype,"fridaSignature",null),w([b],t.prototype,"generics",null),w([b],t.prototype,"isExternal",null),w([b],t.prototype,"isGeneric",null),w([b],t.prototype,"isInflated",null),w([b],t.prototype,"isStatic",null),w([b],t.prototype,"isSynchronized",null),w([b],t.prototype,"modifier",null),w([b],t.prototype,"name",null),w([b],t.prototype,"nativeFunction",null),w([b],t.prototype,"object",null),w([b],t.prototype,"parameterCount",null),w([b],t.prototype,"parameters",null),w([b],t.prototype,"relativeVirtualAddress",null),w([b],t.prototype,"returnType",null),e.Method=t;let n=()=>{let r=e.corlib.class("System.Int64").alloc();r.field("m_value").value=3735928559;let i=r.method("Equals",1).overload(r.class).invokeRaw(r,3735928559);return(n=()=>i)()}})(E||(E={}));var E;(function(e){class t extends H{static get headerSize(){return e.corlib.class("System.Object").instanceSize}get base(){return this.class.parent==null&&R(`class ${this.class.type.name} has no parent`),new Proxy(this,{get(r,i,o){return i=="class"?Reflect.get(r,i).parent:i=="base"?Reflect.getOwnPropertyDescriptor(e.Object.prototype,i).get.bind(o)():Reflect.get(r,i)}})}get class(){return new e.Class(e.exports.objectGetClass(this))}get monitor(){return new e.Object.Monitor(this)}get size(){return e.exports.objectGetSize(this)}field(r){return this.tryField(r)??R(`couldn't find non-static field ${r} in hierarchy of class ${this.class.type.name}`)}method(r,i=-1){return this.tryMethod(r,i)??R(`couldn't find non-static method ${r} in hierarchy of class ${this.class.type.name}`)}ref(r){return new e.GCHandle(e.exports.gcHandleNew(this,+r))}virtualMethod(r){return new e.Method(e.exports.objectGetVirtualMethod(this,r)).bind(this)}tryField(r){let i=this.class.tryField(r);if(i?.isStatic){for(let o of this.class.hierarchy({includeCurrent:!1}))for(let s of o.fields)if(s.name==r&&!s.isStatic)return s.bind(this);return}return i?.bind(this)}tryMethod(r,i=-1){let o=this.class.tryMethod(r,i);if(o?.isStatic){for(let s of this.class.hierarchy())for(let l of s.methods)if(l.name==r&&!l.isStatic&&(i<0||l.parameterCount==i))return l.bind(this);return}return o?.bind(this)}toString(){return this.isNull()?"null":this.method("ToString",0).invoke().content??"null"}unbox(){return this.class.isValueType?new e.ValueType(e.exports.objectUnbox(this),this.class.type):R(`couldn't unbox instances of ${this.class.type.name} as they are not value types`)}weakRef(r){return new e.GCHandle(e.exports.gcHandleNewWeakRef(this,+r))}}w([b],t.prototype,"class",null),w([b],t.prototype,"size",null),w([b],t,"headerSize",null),e.Object=t,function(n){class r{handle;constructor(o){this.handle=o}enter(){return e.exports.monitorEnter(this.handle)}exit(){return e.exports.monitorExit(this.handle)}pulse(){return e.exports.monitorPulse(this.handle)}pulseAll(){return e.exports.monitorPulseAll(this.handle)}tryEnter(o){return!!e.exports.monitorTryEnter(this.handle,o)}tryWait(o){return!!e.exports.monitorTryWait(this.handle,o)}wait(){return e.exports.monitorWait(this.handle)}}n.Monitor=r}(t=e.Object||(e.Object={}))})(E||(E={}));var E;(function(e){class t{name;position;type;constructor(r,i,o){this.name=r,this.position=i,this.type=o}toString(){return`${this.type.name} ${this.name}`}}e.Parameter=t})(E||(E={}));var E;(function(e){class t extends H{type;constructor(r,i){super(r),this.type=i}get(r){return e.read(this.handle.add(r*this.type.class.arrayElementSize),this.type)}read(r,i=0){let o=new globalThis.Array(r);for(let s=0;s<r;s++)o[s]=this.get(s+i);return o}set(r,i){e.write(this.handle.add(r*this.type.class.arrayElementSize),i,this.type)}toString(){return this.handle.toString()}write(r,i=0){for(let o=0;o<r.length;o++)this.set(o+i,r[o])}}e.Pointer=t})(E||(E={}));var E;(function(e){class t extends H{type;constructor(i,o){super(i),this.type=o}get value(){return e.read(this.handle,this.type)}set value(i){e.write(this.handle,i,this.type)}toString(){return this.isNull()?"null":`->${this.value}`}}e.Reference=t;function n(r,i){let o=Memory.alloc(Process.pointerSize);switch(typeof r){case"boolean":return new e.Reference(o.writeS8(+r),e.corlib.class("System.Boolean").type);case"number":switch(i?.enumValue){case e.Type.Enum.UBYTE:return new e.Reference(o.writeU8(r),i);case e.Type.Enum.BYTE:return new e.Reference(o.writeS8(r),i);case e.Type.Enum.CHAR:case e.Type.Enum.USHORT:return new e.Reference(o.writeU16(r),i);case e.Type.Enum.SHORT:return new e.Reference(o.writeS16(r),i);case e.Type.Enum.UINT:return new e.Reference(o.writeU32(r),i);case e.Type.Enum.INT:return new e.Reference(o.writeS32(r),i);case e.Type.Enum.ULONG:return new e.Reference(o.writeU64(r),i);case e.Type.Enum.LONG:return new e.Reference(o.writeS64(r),i);case e.Type.Enum.FLOAT:return new e.Reference(o.writeFloat(r),i);case e.Type.Enum.DOUBLE:return new e.Reference(o.writeDouble(r),i)}case"object":if(r instanceof e.ValueType||r instanceof e.Pointer)return new e.Reference(r.handle,r.type);if(r instanceof e.Object)return new e.Reference(o.writePointer(r),r.class.type);if(r instanceof e.String||r instanceof e.Array)return new e.Reference(o.writePointer(r),r.object.class.type);if(r instanceof NativePointer)switch(i?.enumValue){case e.Type.Enum.NUINT:case e.Type.Enum.NINT:return new e.Reference(o.writePointer(r),i)}else{if(r instanceof Int64)return new e.Reference(o.writeS64(r),e.corlib.class("System.Int64").type);if(r instanceof UInt64)return new e.Reference(o.writeU64(r),e.corlib.class("System.UInt64").type)}default:R(`couldn't create a reference to ${r} using an unhandled type ${i?.name}`)}}e.reference=n})(E||(E={}));var E;(function(e){class t extends H{get content(){return e.exports.stringGetChars(this).readUtf16String(this.length)}set content(i){let o=e.string("vfsfitvnm").handle.offsetOf(s=>s.readInt()==9)??R("couldn't find the length offset in the native string struct");globalThis.Object.defineProperty(e.String.prototype,"content",{set(s){e.exports.stringGetChars(this).writeUtf16String(s??""),this.handle.add(o).writeS32(s?.length??0)}}),this.content=i}get length(){return e.exports.stringGetLength(this)}get object(){return new e.Object(this)}toString(){return this.isNull()?"null":`"${this.content}"`}}e.String=t;function n(r){return new e.String(e.exports.stringNew(Memory.allocUtf8String(r??"")))}e.string=n})(E||(E={}));var E;(function(e){class t extends H{get id(){let r=function(){return this.internal.field("thread_id").value.toNumber()};if(Process.platform!="windows"){let i=Process.getCurrentThreadId(),s=ptr(r.apply(e.currentThread)).offsetOf(c=>c.readS32()==i,1024)??R("couldn't find the offset for determining the kernel id of a posix thread"),l=r;r=function(){return ptr(l.apply(this)).add(s).readS32()}}return J(e.Thread.prototype,"id",r,b),this.id}get internal(){return this.object.tryField("internal_thread")?.value??this.object}get isFinalizer(){return!e.exports.threadIsVm(this)}get managedId(){return this.object.method("get_ManagedThreadId").invoke()}get object(){return new e.Object(this)}get staticData(){return this.internal.field("static_data").value}get synchronizationContext(){let i=(this.object.tryMethod("GetMutableExecutionContext")??this.object.method("get_ExecutionContext")).invoke();return(i.tryField("_syncContext")?.value??i.tryMethod("get_SynchronizationContext")?.invoke()??this.tryLocalValue(e.corlib.class("System.Threading.SynchronizationContext")))?.asNullable()??null}detach(){return e.exports.threadDetach(this)}schedule(r){let i=this.synchronizationContext?.tryMethod("Post");return i==null?Process.runOnThread(this.id,r):new Promise(o=>{let s=e.delegate(e.corlib.class("System.Threading.SendOrPostCallback"),()=>{let l=r();setImmediate(()=>o(l))});Script.bindWeak(globalThis,()=>{s.field("method_ptr").value=s.field("invoke_impl").value=e.exports.domainGet}),i.invoke(s,NULL)})}tryLocalValue(r){for(let i=0;i<16;i++){let o=this.staticData.add(i*Process.pointerSize).readPointer();if(!o.isNull()){let s=new e.Object(o.readPointer()).asNullable();if(s?.class?.isSubclassOf(r,!1))return s}}}}w([b],t.prototype,"internal",null),w([b],t.prototype,"isFinalizer",null),w([b],t.prototype,"managedId",null),w([b],t.prototype,"object",null),w([b],t.prototype,"staticData",null),w([b],t.prototype,"synchronizationContext",null),e.Thread=t,J(e,"attachedThreads",()=>{if(e.exports.threadGetAttachedThreads.isNull()){let n=e.currentThread?.handle??R("Current thread is not attached to IL2CPP"),r=n.toMatchPattern(),i=[];for(let o of Process.enumerateRanges("rw-"))if(o.file==null){let s=Memory.scanSync(o.base,o.size,r);if(s.length==1){for(;;){let l=s[0].address.sub(s[0].size*i.length).readPointer();if(l.isNull()||!l.readPointer().equals(n.readPointer()))break;i.unshift(new e.Thread(l))}break}}return i}return Bn(e.exports.threadGetAttachedThreads).map(n=>new e.Thread(n))}),J(e,"currentThread",()=>new e.Thread(e.exports.threadGetCurrent()).asNullable()),J(e,"mainThread",()=>e.attachedThreads[0])})(E||(E={}));var E;(function(e){let t=class extends H{static get Enum(){let r=(o,s=l=>l)=>s(e.corlib.class(o)).type.enumValue,i={VOID:r("System.Void"),BOOLEAN:r("System.Boolean"),CHAR:r("System.Char"),BYTE:r("System.SByte"),UBYTE:r("System.Byte"),SHORT:r("System.Int16"),USHORT:r("System.UInt16"),INT:r("System.Int32"),UINT:r("System.UInt32"),LONG:r("System.Int64"),ULONG:r("System.UInt64"),NINT:r("System.IntPtr"),NUINT:r("System.UIntPtr"),FLOAT:r("System.Single"),DOUBLE:r("System.Double"),POINTER:r("System.IntPtr",o=>o.field("m_value")),VALUE_TYPE:r("System.Decimal"),OBJECT:r("System.Object"),STRING:r("System.String"),CLASS:r("System.Array"),ARRAY:r("System.Void",o=>o.arrayClass),NARRAY:r("System.Void",o=>new e.Class(e.exports.classGetArrayClass(o,2))),GENERIC_INSTANCE:r("System.Int32",o=>o.interfaces.find(s=>s.name.endsWith("`1")))};return Reflect.defineProperty(this,"Enum",{value:i}),ua({...i,VAR:r("System.Action`1",o=>o.generics[0]),MVAR:r("System.Array",o=>o.method("AsReadOnly",1).generics[0])})}get class(){return new e.Class(e.exports.typeGetClass(this))}get fridaAlias(){function r(i){let o=i.class.fields.filter(s=>!s.isStatic);return o.length==0?["char"]:o.map(s=>s.type.fridaAlias)}if(this.isByReference)return"pointer";switch(this.enumValue){case e.Type.Enum.VOID:return"void";case e.Type.Enum.BOOLEAN:return"bool";case e.Type.Enum.CHAR:return"uchar";case e.Type.Enum.BYTE:return"int8";case e.Type.Enum.UBYTE:return"uint8";case e.Type.Enum.SHORT:return"int16";case e.Type.Enum.USHORT:return"uint16";case e.Type.Enum.INT:return"int32";case e.Type.Enum.UINT:return"uint32";case e.Type.Enum.LONG:return"int64";case e.Type.Enum.ULONG:return"uint64";case e.Type.Enum.FLOAT:return"float";case e.Type.Enum.DOUBLE:return"double";case e.Type.Enum.NINT:case e.Type.Enum.NUINT:case e.Type.Enum.POINTER:case e.Type.Enum.STRING:case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return"pointer";case e.Type.Enum.VALUE_TYPE:return this.class.isEnum?this.class.baseType.fridaAlias:r(this);case e.Type.Enum.CLASS:case e.Type.Enum.OBJECT:case e.Type.Enum.GENERIC_INSTANCE:return this.class.isStruct?r(this):this.class.isEnum?this.class.baseType.fridaAlias:"pointer";default:return"pointer"}}get isByReference(){return this.name.endsWith("&")}get isPrimitive(){switch(this.enumValue){case e.Type.Enum.BOOLEAN:case e.Type.Enum.CHAR:case e.Type.Enum.BYTE:case e.Type.Enum.UBYTE:case e.Type.Enum.SHORT:case e.Type.Enum.USHORT:case e.Type.Enum.INT:case e.Type.Enum.UINT:case e.Type.Enum.LONG:case e.Type.Enum.ULONG:case e.Type.Enum.FLOAT:case e.Type.Enum.DOUBLE:case e.Type.Enum.NINT:case e.Type.Enum.NUINT:return!0;default:return!1}}get name(){let r=e.exports.typeGetName(this);try{return r.readUtf8String()}finally{e.free(r)}}get object(){return new e.Object(e.exports.typeGetObject(this))}get enumValue(){return e.exports.typeGetTypeEnum(this)}is(r){return e.exports.typeEquals.isNull()?this.object.method("Equals").invoke(r.object):!!e.exports.typeEquals(this,r)}toString(){return this.name}};w([b],t.prototype,"class",null),w([b],t.prototype,"fridaAlias",null),w([b],t.prototype,"isByReference",null),w([b],t.prototype,"isPrimitive",null),w([b],t.prototype,"name",null),w([b],t.prototype,"object",null),w([b],t.prototype,"enumValue",null),w([b],t,"Enum",null),t=w([xt],t),e.Type=t})(E||(E={}));var E;(function(e){class t extends H{type;constructor(r,i){super(r),this.type=i}box(){return new e.Object(e.exports.valueTypeBox(this.type.class,this))}field(r){return this.tryField(r)??R(`couldn't find non-static field ${r} in hierarchy of class ${this.type.name}`)}method(r,i=-1){return this.tryMethod(r,i)??R(`couldn't find non-static method ${r} in hierarchy of class ${this.type.name}`)}tryField(r){let i=this.type.class.tryField(r);if(i?.isStatic){for(let o of this.type.class.hierarchy())for(let s of o.fields)if(s.name==r&&!s.isStatic)return s.bind(this);return}return i?.bind(this)}tryMethod(r,i=-1){let o=this.type.class.tryMethod(r,i);if(o?.isStatic){for(let s of this.type.class.hierarchy())for(let l of s.methods)if(l.name==r&&!l.isStatic&&(i<0||l.parameterCount==i))return l.bind(this);return}return o?.bind(this)}toString(){let r=this.method("ToString",0);return this.isNull()?"null":r.class.isValueType?r.invoke().content??"null":this.box().toString()??"null"}}e.ValueType=t})(E||(E={}));globalThis.Il2Cpp=E;Il2Cpp.perform(function(){console.log("\u{1F527} Loading Robust GoodyHutHelper Instance Manager...");let e=!1,t=0,n=!1,r=0,i=0;function o(){try{console.log("\u{1F3AF} Installing GetCollectTime hook for instant collection completion...");let a=Il2Cpp.domain.assembly("Assembly-CSharp").image.class("GoodyHutHelper");if(!a){console.log("\u274C GoodyHutHelper class not found");return}let u=a.method("GetCollectTime");if(!u){console.log("\u274C GetCollectTime method not found");return}console.log(`\u{1F3AF} Found GetCollectTime method at: ${u.handle}`),u.implementation=function(){return t++,t%10===1&&console.log(`\u26A1 GetCollectTime hook triggered (call #${t}) - returning 0 for instant completion`),0},e=!0,console.log("\u2705 GetCollectTime hook installed successfully!"),console.log("\u{1F4A1} All GoodyHut collections will now complete instantly")}catch(h){console.log(`\u274C Failed to install GetCollectTime hook: ${h}`),e=!1}}function s(){try{console.log("\u{1F9F9} Installing GoodyHutHelperConfig cleanup hook..."),console.log(`\u{1F527} Platform: ${(At(),me(Tt)).platform()} ${(At(),me(Tt)).arch()}`);let a=Il2Cpp.domain.assembly("Assembly-CSharp").image.class("GoodyHutHelper");if(!a){console.log("\u274C GoodyHutHelper class not found for cleanup hook"),console.log("\u{1F4A1} This may be normal on some game versions or platforms");return}let u=a.method("CanCollect");if(!u){console.log("\u274C CanCollect method not found for cleanup hook"),console.log("\u{1F4A1} Trying alternative method names...");let p=["CanCollect","canCollect","IsCollectable","CanBeCollected"],m=null;for(let S of p)try{if(m=a.method(S),m){console.log(`\u2705 Found alternative method: ${S}`);break}}catch{continue}if(!m){console.log("\u274C No suitable method found for cleanup hook"),console.log("\u{1F4A1} Cleanup hook will be disabled, but automatic ruin selling will still work");return}}console.log(`\u{1F9F9} Found CanCollect method at: ${u.handle}`);let d=null;try{if(d=u.implementation,!d){console.log("\u26A0\uFE0F Direct implementation access failed, trying alternative approaches...");try{let p=u.nativeFunction;p&&(d=p,console.log("\u2705 Using native function pointer approach"))}catch(p){console.log(`\u26A0\uFE0F Native function pointer approach failed: ${p}`)}d||(d=function(){try{return u.invoke()}catch(p){return console.log(`\u26A0\uFE0F Original method invocation failed: ${p}`),!1}},console.log("\u2705 Using wrapper function approach"))}}catch(p){console.log(`\u26A0\uFE0F Method capture error: ${p.message||p}`),d=null}if(!d){console.log("\u274C Could not capture original CanCollect implementation with any approach"),console.log("\u{1F4A1} Cleanup hook will be disabled, but other functionality will continue");return}console.log("\u2705 Successfully captured original CanCollect implementation"),u.implementation=function(){r++;let p;try{if(d&&typeof d.call=="function")try{p=d.call(this)}catch(m){if(typeof d=="function")p=d();else throw m}else if(d&&typeof d=="function")p=d();else return console.log("\u26A0\uFE0F No valid original implementation available, returning safe default"),!0}catch(m){let S=m.message||m.toString();if(S.includes("abort")||S.includes("crash")||S.includes("segmentation")){console.log(`\u{1F6A8} Critical error in CanCollect hook: ${S}`),console.log("\u{1F6E1}\uFE0F Disabling cleanup hook to prevent further crashes");try{u.implementation=d||function(){return!0}}catch{}return!0}try{return console.log(`\u26A0\uFE0F CanCollect invocation failed, using safe fallback: ${S}`),!0}catch(_){return console.log(`\u274C All CanCollect fallbacks failed: ${_.message||_}`),!0}}if(p===!1)try{let m=this.field("m_config");if(m&&m.value&&m.value!==null){let _=m.value.field("cleanUp");_&&_.value===!1&&(_.value=!0,i++,i%10===1&&console.log(`\u{1F9F9} Auto-enabled cleanup for non-collectible instance (count: ${i})`))}}catch(m){r<=5&&console.log(`\u26A0\uFE0F Could not access GoodyHutHelperConfig: ${m}`)}return p},n=!0,console.log("\u2705 GoodyHutHelperConfig cleanup hook installed successfully!"),console.log("\u{1F4A1} Cleanup will be automatically enabled for non-collectible instances")}catch(h){console.log(`\u274C Failed to install cleanup hook: ${h}`),n=!1}}o(),s();class l{validInstances=[];collectibleInstances=[];batchSize=10;batchDelay=2e3;constructor(){this.initializeStoragePaths(),this.initializeRuinSellingTracer()}initializeRuinSellingTracer(){try{this.ruinSellingTracer={isTracing:()=>!1,startTracing:()=>console.log("\u{1F50D} Ruin selling tracer started"),stopTracing:()=>console.log("\u23F9\uFE0F Ruin selling tracer stopped"),traceMethodCall:(a,u,d,p,m)=>{let S=`trace_${Date.now()}_${Math.random().toString(36).substring(2,9)}`;return console.log(`\u{1F4DE} [${S}] ${a} called on entity ${m||"unknown"} (session: ${p})`),S},traceMethodCompletion:(a,u)=>{console.log(`\u2705 [${a}] Method completed with result: ${JSON.stringify(u)}`)},traceMethodError:(a,u)=>{console.log(`\u274C [${a}] Method failed: ${u.message}`)},getTraceHistory:()=>[],clearTraceHistory:()=>console.log("\u{1F5D1}\uFE0F Trace history cleared"),getPerformanceSummary:()=>({totalCalls:0,successfulCalls:0,failedCalls:0,averageExecutionTimeMs:0,minExecutionTimeMs:0,maxExecutionTimeMs:0,totalExecutionTimeMs:0}),generateStructuredLog:()=>"No trace data available"},console.log("\u2705 Ruin selling tracer initialized")}catch(a){console.log(`\u26A0\uFE0F Could not initialize ruin selling tracer: ${a.message||a}`),this.ruinSellingTracer=null}}attemptInstantCompletion=!0;buyThroughFailureCount=0;buyThroughSuccessCount=0;buyThroughFailureThreshold=20;attemptRuinSelling=!0;ruinSellSuccessCount=0;ruinSellFailureCount=0;stateFilePath="/sdcard/Android/data/com.nexonm.dominations.adk/files/goodyHutState.json";alternativeStatePaths=[];currentWorkingPath="";memoryOnlyMode=!1;memoryStorage={};stateExpirationHours=24;stateVersion=2;ruinSellingTracer=null;tracerIntegration=null;initializeStoragePaths(){try{let a=null,u=null,d=null;try{a=(At(),me(Tt))}catch(P){console.log(`\u26A0\uFE0F os module not available: ${P}`)}try{u=(In(),me(Ji))}catch(P){console.log(`\u26A0\uFE0F path module not available: ${P}`)}try{d=(yt(),me(mt))}catch(P){console.log(`\u26A0\uFE0F fs module not available: ${P}`)}let p=(...P)=>u&&typeof u.join=="function"?u.join(...P):P.filter(q=>q&&q.length>0).join("/").replace(/\/+/g,"/"),m=P=>u&&typeof u.resolve=="function"?u.resolve(P):P.startsWith("/")?P:`/${P}`,S=a&&typeof a.platform=="function"?a.platform():"linux",_=a&&typeof a.arch=="function"?a.arch():"unknown",x=a&&typeof a.homedir=="function"?a.homedir():"/data",A=a&&typeof a.tmpdir=="function"?a.tmpdir():"/tmp",O="";try{O=y.cwd()}catch{O="/data"}console.log(`\u{1F5A5}\uFE0F Detected platform: ${S} (${_})`),console.log(`\u{1F4C1} Process working directory: ${O}`),console.log(`\u{1F3E0} User home directory: ${x}`),console.log(`\u{1F4C2} System temp directory: ${A}`);let B=[],te="/sdcard/Android/data/com.nexonm.dominations.adk/files/goodyHutState.json";B.push(te),console.log(`\u{1F4F1} Using specified Android storage location: ${te}`),this.alternativeStatePaths=[];let Y=P=>{if(u&&typeof u.dirname=="function")return u.dirname(P);let q=P.lastIndexOf("/");return q>0?P.substring(0,q):"/"};for(let P of B)try{let q=Y(P);if(d&&typeof d.existsSync=="function"){if(!d.existsSync(q))try{typeof d.mkdirSync=="function"&&(d.mkdirSync(q,{recursive:!0}),console.log(`\u{1F4C1} Created directory: ${q}`))}catch(ci){console.log(`\u274C Cannot create directory ${q}: ${ci.message||ci}`);continue}}else console.log(`\u26A0\uFE0F fs module not available, cannot verify directory: ${q}`);this.alternativeStatePaths.push(P)}catch(q){console.log(`\u26A0\uFE0F Invalid path ${P}: ${q.message||q}`);continue}this.alternativeStatePaths.length===0&&(console.log("\u26A0\uFE0F No storage locations were successfully initialized, adding emergency fallbacks..."),this.alternativeStatePaths=["goodyHutState.json","/tmp/goodyHutState.json","/data/goodyHutState.json","./state/goodyHutState.json"]),console.log(`\u{1F4C1} Initialized ${this.alternativeStatePaths.length} potential storage locations:`),this.alternativeStatePaths.forEach((P,q)=>{console.log(`   ${q+1}. ${P}`)})}catch(a){console.log(`\u26A0\uFE0F Could not initialize alternative storage paths: ${a.message||a}`),this.alternativeStatePaths=["goodyHutState.json","/tmp/goodyHutState.json","/data/goodyHutState.json","./goodyHutState.json"],console.log(`\u{1F4C1} Using emergency fallback storage locations (${this.alternativeStatePaths.length} paths)`)}}findWritableStoragePath(){if(this.currentWorkingPath){if(this.testPathWritability(this.currentWorkingPath))return this.currentWorkingPath;console.log(`\u26A0\uFE0F Previously working path ${this.currentWorkingPath} is no longer writable`),this.currentWorkingPath=""}console.log(`\u{1F50D} Testing ${this.alternativeStatePaths.length} storage locations for write access...`);for(let a=0;a<this.alternativeStatePaths.length;a++){let u=this.alternativeStatePaths[a];if(console.log(`   Testing ${a+1}/${this.alternativeStatePaths.length}: ${u}`),this.testPathWritability(u))return this.currentWorkingPath=u,console.log(`\u2705 Found writable storage location: ${u}`),u}return console.log("\u274C No writable storage locations found!"),console.log("\u{1F4BE} Enabling memory-only storage mode"),console.log("\u26A0\uFE0F Progress will be lost when script restarts"),this.memoryOnlyMode=!0,this.provideTroubleshootingGuidance(),this.stateFilePath}testPathWritability(a){try{let u=null,d=!1;try{u=(yt(),me(mt)),d=u&&typeof u.writeFileSync=="function"}catch(_){console.log(`     \u26A0\uFE0F fs module not available: ${_}`),d=!1}if(!d)return console.log("     \u274C File system operations not available in this environment"),console.log("     \u{1F4A1} Falling back to memory-only storage"),!1;let m=(_=>{let x=_.lastIndexOf("/");return x>0?_.substring(0,x):"/"})(a);try{if(typeof u.existsSync=="function"&&!u.existsSync(m))if(typeof u.mkdirSync=="function")u.mkdirSync(m,{recursive:!0}),console.log(`     \u{1F4C1} Created directory: ${m}`);else return console.log("     \u26A0\uFE0F Cannot create directory - mkdirSync not available"),!1}catch(_){return console.log(`     \u274C Cannot create directory: ${_.message||_}`),!1}let S=JSON.stringify({test:!0,timestamp:Date.now(),platform:"frida-environment",testId:Math.random().toString(36).substring(7)});try{if(typeof u.writeFileSync=="function")u.writeFileSync(a,S,"utf8"),console.log("     \u2705 Write test successful");else return console.log("     \u274C writeFileSync not available"),!1}catch(_){return console.log(`     \u274C Write failed: ${_.message||_}`),!1}try{if(typeof u.readFileSync=="function"){let _=u.readFileSync(a,"utf8");if(JSON.parse(_).test!==!0)return console.log("     \u274C Read verification failed - data corruption"),!1;console.log("     \u2705 Read verification successful")}else return console.log("     \u274C readFileSync not available"),!1}catch(_){return console.log(`     \u274C Read failed: ${_.message||_}`),!1}try{typeof u.unlinkSync=="function"?(u.unlinkSync(a),console.log("     \u2705 Delete test successful")):console.log("     \u26A0\uFE0F unlinkSync not available - test file may remain")}catch(_){console.log(`     \u26A0\uFE0F Delete failed: ${_.message||_}`)}return!0}catch(u){let d=u.message||u.toString();return console.log(`     \u274C Failed: ${d}`),d.includes("not a function")?(console.log("     \u{1F527} Issue: File system API not available in Frida environment"),console.log("     \u{1F4A1} System will use memory-only storage")):d.includes("EACCES")||d.includes("permission denied")?console.log("     \u{1F527} Issue: Permission denied - insufficient access rights"):d.includes("ENOENT")||d.includes("no such file")?console.log("     \u{1F527} Issue: Path does not exist and cannot be created"):d.includes("ENOSPC")||d.includes("no space")?console.log("     \u{1F527} Issue: Insufficient disk space"):d.includes("EROFS")||d.includes("read-only")?console.log("     \u{1F527} Issue: File system is read-only"):console.log(`     \u{1F527} Issue: ${d}`),!1}}provideTroubleshootingGuidance(){let a=(At(),me(Tt)),u=a.platform();console.log(`
\u{1F527} TROUBLESHOOTING GUIDE for ${u.toUpperCase()}`),console.log("\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550"),u==="win32"?(console.log("\u{1F4CB} Windows-specific solutions:"),console.log('   1. Run script as Administrator (right-click \u2192 "Run as administrator")'),console.log("   2. Check folder permissions in File Explorer"),console.log("   3. Disable antivirus real-time protection temporarily"),console.log("   4. Try running from a different directory (e.g., Desktop)"),console.log("   5. Check if disk is full (run 'dir' to see free space)")):u==="darwin"?(console.log("\u{1F4CB} macOS-specific solutions:"),console.log("   1. Run with sudo: sudo node your-script.js"),console.log("   2. Check permissions: ls -la"),console.log("   3. Grant Full Disk Access in System Preferences \u2192 Security & Privacy"),console.log("   4. Try running from ~/Documents or ~/Desktop")):(console.log("\u{1F4CB} Linux/Unix-specific solutions:"),console.log("   1. Run with sudo: sudo node your-script.js"),console.log("   2. Check permissions: ls -la"),console.log("   3. Check disk space: df -h"),console.log("   4. Check if /tmp is mounted read-only: mount | grep tmp"),console.log("   5. Try creating ~/dominations directory manually")),console.log(`
\u{1F50D} General debugging steps:`),console.log(`   \u2022 Check current user: ${a.userInfo().username}`),console.log(`   \u2022 Check working directory: ${y.cwd()}`),console.log("   \u2022 Check available disk space"),console.log("   \u2022 Try running from a different location"),console.log("   \u2022 Check if antivirus/security software is blocking file access"),console.log(`\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550
`)}safeInvoke(a,u,...d){try{if(!a||a.isNull())return{error:"Null instance",value:null};let p=a.method(u);if(!p)return{error:`Method ${u} not found`,value:null};let m;try{d.length>0?m=p.invoke(...d):m=p.invoke()}catch(S){let _=String(S);if(_.includes("bad argument count")){console.log(`\u26A0\uFE0F ${u} invocation failed, trying parameter variations: ${_}`);let x=[[],[!0],[!1],[0],[1],[null],[!0,0],[!1,0]];for(let A of x)try{m=p.invoke(...A),A.length>0&&console.log(`\u2705 ${u} succeeded with parameters: [${A.join(", ")}]`);break}catch{continue}if(m===void 0)return console.log(`\u26A0\uFE0F ${u} invocation failed, using safe fallback: ${_}`),u==="CanCollect"?{error:null,value:!0}:u==="IsJobComplete"?{error:null,value:!1}:u==="CanBuyThrough"?{error:null,value:!1}:u.includes("Has")||u.includes("Can")?{error:null,value:!1}:u.includes("Get")&&u.includes("Amount")?{error:null,value:0}:u.includes("Get")&&u.includes("Type")?{error:null,value:"UNKNOWN"}:{error:`Parameter error: ${_}`,value:null}}else throw S}return{error:null,value:m}}catch(p){let m=String(p);return m.includes("access violation")||m.includes("0x0")?{error:"Access violation - invalid instance",value:null}:{error:`Method error: ${m}`,value:null}}}validateInstance(a,u){console.log(`Validating instance from EntityController ${u}...`);let d={instance:a,entityIndex:u,isValid:!1,canCollect:!1,canBuyThrough:!1,state:"UNKNOWN",rewardType:"UNKNOWN",rewardAmount:null},p=this.safeInvoke(a,"IsJobComplete");if(p.error)return d.error=p.error,d;let m=this.safeInvoke(a,"CanCollect");if(m.error)return d.error=m.error,d;let S=this.safeInvoke(a,"CanBuyThrough");if(S.error)return d.error=S.error,d;let _=this.safeInvoke(a,"GetRewardType"),x=this.safeInvoke(a,"GetRewardAmount");d.isValid=!0,d.canCollect=m.value,d.canBuyThrough=S.value,d.rewardType=_.error?"UNKNOWN":String(_.value),d.rewardAmount=x.error?null:x.value;let A=p.value;return A===!0&&d.canCollect===!0?d.state="IDLE_READY":A===!1&&d.canCollect===!1?d.state="COLLECTING":A===!0&&d.canCollect===!1?d.state="COMPLETED_AWAITING":d.state="INCONSISTENT",console.log(`\u2705 EntityController ${u}: Valid - ${d.state} (CanCollect: ${d.canCollect}, Reward: ${d.rewardAmount} ${d.rewardType})`),d}scanAndValidateInstances(){console.log("\u{1F50D} Starting comprehensive instance validation..."),this.validInstances=[],this.collectibleInstances=[];try{let a=Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController"),u=Il2Cpp.gc.choose(a);console.log(`Found ${u.length} EntityController instances`),console.log(`Validating GoodyHutHelper components...
`);let d=0,p=0,m=0,S=0;u.forEach((_,x)=>{try{let A=_.field("m_goodyHut");if(A&&A.value&&A.value!==null&&A.value.toString()!=="0x0"){let O=A.value,B=this.validateInstance(O,x);B.isValid?(this.validInstances.push(B),d++,B.canCollect&&(this.collectibleInstances.push(B),m++,B.rewardType==="GEMS"&&S++)):p++}}catch{p++}}),console.log(`
=== VALIDATION SUMMARY ===`),console.log(`\u2705 Valid instances: ${d}`),console.log(`\u274C Invalid instances: ${p}`),console.log(`\u{1F3AF} Collectible instances: ${m}`),console.log(`\u{1F48E} GEMS collectible instances: ${S}`),console.log(`\u{1F4CA} Success rate: ${Math.round(d/(d+p)*100)}%`)}catch(a){console.log(`\u274C Error during validation: ${a}`)}}showValidInstances(){console.log(`
=== VALID INSTANCES DETAILS ===`),this.validInstances.forEach((a,u)=>{console.log(`
[${u}] EntityController ${a.entityIndex}:`),console.log(`  State: ${a.state}`),console.log(`  CanCollect: ${a.canCollect}`),console.log(`  CanBuyThrough: ${a.canBuyThrough}`),console.log(`  Reward: ${a.rewardAmount} ${a.rewardType}`);let d=this.safeInvoke(a.instance,"GetJobTimeLeft"),p=this.safeInvoke(a.instance,"GetHealth"),m=this.safeInvoke(a.instance,"GetExplorations");d.error||console.log(`  TimeLeft: ${d.value}`),p.error||console.log(`  Health: ${p.value}`),m.error||console.log(`  Explorations: ${m.value}`)})}getBestCollectibleInstance(){if(this.collectibleInstances.length===0)return console.log("\u274C No collectible instances found"),null;let a=this.collectibleInstances.filter(d=>d.rewardType==="GEMS");if(a.length===0)return console.log("\u274C No collectible instances with GEMS rewards found"),console.log(`Available rewards: ${this.collectibleInstances.map(d=>`${d.rewardAmount} ${d.rewardType}`).join(", ")}`),null;console.log(`\u{1F48E} Found ${a.length} collectible instances with GEMS rewards`);let u=a.filter(d=>d.state==="IDLE_READY");return u.length>0?(console.log(`\u2705 Using IDLE_READY GEMS instance: ${u[0].rewardAmount} GEMS`),u[0]):(console.log(`\u2705 Using first GEMS collectible instance (${a[0].state}): ${a[0].rewardAmount} GEMS`),a[0])}shouldAttemptInstantCompletion(a){return this.attemptInstantCompletion?this.buyThroughFailureCount>=this.buyThroughFailureThreshold?(console.log(`\u26A0\uFE0F Disabling instant completion after ${this.buyThroughFailureCount} consecutive failures`),this.attemptInstantCompletion=!1,!1):!!a.canBuyThrough:!1}executeInstantCompletion(a){let u=this.safeInvoke(a.instance,"DoJobBuyThrough");return u.error?(this.buyThroughFailureCount++,u.error.includes("abort was called")?console.log("    \u26A0\uFE0F Instant completion blocked (insufficient currency or game restriction)"):console.log(`    \u26A0\uFE0F Instant completion failed: ${u.error}`),!1):(this.buyThroughSuccessCount++,this.buyThroughFailureCount=0,console.log("    \u{1F48E} Instant completion successful!"),!0)}testDoJobBuyThrough(a){console.log(`
\u{1F48E} Executing DoJobBuyThrough on EntityController ${a.entityIndex}...`);let u=this.safeInvoke(a.instance,"DoJobBuyThrough");return u.error?(console.log(`\u26A0\uFE0F DoJobBuyThrough not available: ${u.error}`),!1):(console.log("\u2705 DoJobBuyThrough executed successfully - instant completion!"),!0)}executeStartCollect(a){console.log(`
\u{1F680} Executing StartCollect on EntityController ${a.entityIndex}...`);let u=this.safeInvoke(a.instance,"CanCollect");if(u.error)return console.log(`\u274C Instance became invalid: ${u.error}`),!1;if(!u.value)return console.log("\u274C Instance no longer collectible"),!1;let d=this.safeInvoke(a.instance,"StartCollect");return d.error?(console.log(`\u274C StartCollect failed: ${d.error}`),!1):(console.log("\u2705 StartCollect executed successfully!"),setTimeout(()=>{this.safeInvoke(a.instance,"DoJobBuyThrough").error?console.log("\u26A0\uFE0F DoJobBuyThrough not available (this is normal if collection is already complete)"):console.log("\u{1F48E} DoJobBuyThrough executed successfully - instant completion!"),console.log(`\u{1F389} Collection process completed for ${a.rewardAmount} GEMS`)},500),!0)}checkForSellableRuins(a){try{let u=this.safeInvoke(a.instance,"HasRuins"),d=this.safeInvoke(a.instance,"HasDebris"),p=this.safeInvoke(a.instance,"CanSell"),m=this.safeInvoke(a.instance,"CanClear");return!u.error&&u.value===!0?{hasRuins:!0,ruinType:"ruins",sellMethod:"SellRuins"}:!d.error&&d.value===!0?{hasRuins:!0,ruinType:"debris",sellMethod:"ClearDebris"}:!p.error&&p.value===!0?{hasRuins:!0,ruinType:"sellable",sellMethod:"DoSell"}:!m.error&&m.value===!0?{hasRuins:!0,ruinType:"clearable",sellMethod:"DoClear"}:{hasRuins:!1}}catch(u){return console.log(`    \u26A0\uFE0F Error checking for ruins: ${u}`),{hasRuins:!1}}}executeRuinSelling(a,u){let d="";if(this.ruinSellingTracer&&this.ruinSellingTracer.isTracing()){let p=[{type:"ValidatedInstance",value:a,name:"validation"},{type:"RuinInfo",value:u,name:"ruinInfo"}];d=this.ruinSellingTracer.traceMethodCall(u.sellMethod,a.instance,p,`ruin-sell-${Date.now()}`,a.entityIndex)}try{console.log(`    \u{1F5D1}\uFE0F Attempting to sell/clear ${u.ruinType} using ${u.sellMethod}...`);let p=this.safeInvoke(a.instance,u.sellMethod);return p.error?(console.log(`    \u274C Ruin selling failed: ${p.error}`),this.ruinSellFailureCount++,d&&this.ruinSellingTracer&&this.ruinSellingTracer.traceMethodCompletion(d,{type:"boolean",value:!1,name:"failed",error:p.error}),!1):(console.log(`    \u2705 Successfully sold/cleared ${u.ruinType}!`),this.ruinSellSuccessCount++,d&&this.ruinSellingTracer&&this.ruinSellingTracer.traceMethodCompletion(d,{type:"boolean",value:!0,name:"success"}),!0)}catch(p){return console.log(`    \u274C Ruin selling error: ${p}`),this.ruinSellFailureCount++,d&&this.ruinSellingTracer&&this.ruinSellingTracer.traceMethodError(d,p),!1}}async processCompletedInstances(a){if(!this.attemptRuinSelling||a.state!=="COMPLETED_AWAITING")return!1;console.log(`    \u{1F50D} Processing completed instance (EntityController ${a.entityIndex}) for ruin selling...`);let u=this.checkForSellableRuins(a);if(!u.hasRuins)return console.log(`    \u2139\uFE0F No sellable ruins found for EntityController ${a.entityIndex}`),!1;if(console.log(`    \u{1F48E} Found ${u.ruinType} that can be sold/cleared`),this.executeRuinSelling(a,{ruinType:u.ruinType,sellMethod:u.sellMethod})){await new Promise(m=>setTimeout(m,500));let p=this.validateInstance(a.instance,a.entityIndex);return p.isValid&&p.state==="IDLE_READY"?(console.log("    \u{1F389} Instance cleared and ready for future collection!"),!0):(console.log(`    \u26A0\uFE0F Instance state after selling: ${p.state}`),!1)}return!1}async validateBatch(a,u,d){let p=Math.min(u+d,a.length),m=[];console.log(`\u{1F50D} Validating batch instances ${u+1}-${p}...`);for(let S=u;S<p;S++)try{let x=a[S].field("m_goodyHut");if(x&&x.value&&x.value!==null&&x.value.toString()!=="0x0"){let A=x.value,O=this.validateInstance(A,S);O.isValid&&O.canCollect&&O.rewardType==="GEMS"&&m.push(O)}}catch{}return m}async validateBatchWithState(a,u,d,p){let m=Math.min(u+d,a.length),S=[],_=0,x=0;console.log(`\u{1F50D} Validating batch instances ${u+1}-${m}...`);for(let A=u;A<m;A++){if(p.processedEntityIndices.has(A)){_++;continue}try{let B=a[A].field("m_goodyHut");if(B&&B.value&&B.value!==null&&B.value.toString()!=="0x0"){let te=B.value,Y=this.validateInstance(te,A);Y.isValid&&(Y.canCollect&&Y.rewardType==="GEMS"?S.push(Y):Y.state==="COMPLETED_AWAITING"&&Y.rewardType==="GEMS"&&(console.log(`  \u{1F5D1}\uFE0F Found completed GEMS instance (EntityController ${A}) - checking for ruins...`),await this.processCompletedInstances(Y)&&(x++,console.log(`  \u2705 Ruins cleared for EntityController ${A} - may be available for future collection`))))}}catch{}}return _>0&&console.log(`\u23ED\uFE0F Skipped ${_} already processed instances`),x>0&&console.log(`\u{1F5D1}\uFE0F Processed ruins for ${x} completed instances`),S}async collectBatch(a,u){if(a.length===0)return console.log(`\u{1F4E6} Batch ${u}: No GEMS instances to collect`),{collectionsStarted:0,instantCompletions:0};console.log(`\u{1F680} Batch ${u}: Starting collection of ${a.length} GEMS instances...`);let d=0,p=0,m=a.map(async(A,O)=>{try{console.log(`  \u{1F48E} [${O+1}/${a.length}] Collecting ${A.rewardAmount} GEMS from EntityController ${A.entityIndex}`);let B=this.safeInvoke(A.instance,"StartCollect");if(B.error)return console.log(`    \u274C StartCollect failed: ${B.error}`),{started:!1,instantCompleted:!1};console.log("    \u2705 Collection started successfully"),d++;let te=!1;return e?(console.log("    \u26A1 Collection completing instantly via GetCollectTime hook"),te=!0,p++):(await new Promise(Y=>setTimeout(Y,100)),this.shouldAttemptInstantCompletion(A)?(te=this.executeInstantCompletion(A),te&&p++):console.log("    \u23F3 Instant completion skipped (will complete naturally)")),console.log(`    \u{1F389} Collection initiated for ${A.rewardAmount} GEMS`),{started:!0,instantCompleted:te}}catch(B){return console.log(`    \u274C Collection error: ${B}`),{started:!1,instantCompleted:!1}}}),S=await Promise.all(m),_=S.filter(A=>A.started).length,x=S.filter(A=>A.instantCompleted).length;return console.log(`\u{1F4E6} Batch ${u} completed: ${_}/${a.length} collections started, ${x}/${a.length} instant completions`),{collectionsStarted:_,instantCompletions:x}}async processBatchCollection(a=!1){if(console.log("\u{1F504} Starting batch collection process..."),!a){let p=this.getProgressStatus();p.hasProgress&&console.log(`\u{1F4CA} ${p.summary}`)}let u=a?null:this.loadState(),d=u!==null;if(!u)u=this.createFreshState(),console.log(a?"\u{1F504} Force reset: Starting fresh collection session":"\u{1F195} Starting fresh collection session");else{let p=Math.round((Date.now()-u.sessionStats.startTime)/1e3/60);console.log(`\u{1F4C2} Resuming from saved progress: batch ${u.currentBatchIndex+1}/${u.totalBatches} (saved ${p}m ago)`),console.log(`\u23ED\uFE0F Already processed: ${u.processedEntityIndices.size} instances in ${u.completedBatches.length} completed batches`),this.buyThroughFailureCount=u.buyThroughStats.failureCount,this.buyThroughSuccessCount=u.buyThroughStats.successCount,this.attemptInstantCompletion=u.buyThroughStats.enabled,u.ruinSellStats&&(this.ruinSellFailureCount=u.ruinSellStats.failureCount,this.ruinSellSuccessCount=u.ruinSellStats.successCount,this.attemptRuinSelling=u.ruinSellStats.enabled)}console.log(e?"\u26A1 GetCollectTime hook is active - collections will complete instantly":"\u26A0\uFE0F GetCollectTime hook not available - falling back to DoJobBuyThrough method");try{let p=Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController"),m=Il2Cpp.gc.choose(p);console.log(`\u{1F4CA} Found ${m.length} EntityController instances`);let S=Math.ceil(m.length/this.batchSize);if(u.totalBatches=S,d){let A=S-u.completedBatches.length;console.log(`\u{1F4E6} Resuming: ${A} batches remaining of ${S} total`),console.log(`\u23ED\uFE0F Skipping ${u.processedEntityIndices.size} already processed instances`)}else console.log(`\u{1F4E6} Processing in ${S} batches of ${this.batchSize} instances each
`);let _=u.sessionStats.totalCollectionsStarted,x=u.sessionStats.totalInstantCompletions;for(let A=u.currentBatchIndex;A<S;A++){let O=A*this.batchSize,B=A+1;if(u.completedBatches.includes(B)){console.log(`\u23ED\uFE0F Skipping already completed batch ${B}/${S}`);continue}console.log(`
=== BATCH ${B}/${S} ===`);let te=await this.validateBatchWithState(m,O,this.batchSize,u);if(console.log(`\u2705 Found ${te.length} valid GEMS instances in batch ${B}`),te.length>0){let Y=await this.collectBatch(te,B);_+=Y.collectionsStarted,x+=Y.instantCompletions,te.forEach(P=>{u.processedEntityIndices.add(P.entityIndex)})}u.completedBatches.push(B),u.currentBatchIndex=A,u.sessionStats.totalCollectionsStarted=_,u.sessionStats.totalInstantCompletions=x,u.sessionStats.totalRuinsProcessed=this.ruinSellSuccessCount,u.buyThroughStats.successCount=this.buyThroughSuccessCount,u.buyThroughStats.failureCount=this.buyThroughFailureCount,u.buyThroughStats.enabled=this.attemptInstantCompletion,u.ruinSellStats.successCount=this.ruinSellSuccessCount,u.ruinSellStats.failureCount=this.ruinSellFailureCount,u.ruinSellStats.enabled=this.attemptRuinSelling,u.hookStats.callCount=t,u.cleanupHookStats.callCount=r,u.cleanupHookStats.autoEnabledCount=i,this.saveState(u),this.buyThroughFailureCount>0&&console.log(`\u26A0\uFE0F Instant completion status: ${this.buyThroughSuccessCount} successes, ${this.buyThroughFailureCount} failures`),A<S-1&&(console.log(`\u23F3 Waiting ${this.batchDelay/1e3}s before next batch...`),await new Promise(Y=>setTimeout(Y,this.batchDelay)))}if(console.log(`
\u{1F389} Batch collection completed!`),console.log(`\u{1F4CA} Collections started: ${_}`),console.log(`\u{1F48E} Instant completions: ${x}`),console.log(`\u{1F5D1}\uFE0F Ruins processed: ${this.ruinSellSuccessCount}`),e?console.log(`\u26A1 Completion method: GetCollectTime hook (${t} hook calls)`):x>0?console.log("\u{1F48E} Completion method: DoJobBuyThrough"):_>0&&console.log("\u23F3 Completion method: Natural completion over time"),x===0&&_>0&&!e&&(console.log("\u2139\uFE0F Note: Collections were started but instant completion was not available"),console.log("\u2139\uFE0F Collections will complete naturally over time")),!e&&(this.buyThroughSuccessCount>0||this.buyThroughFailureCount>0)){let A=Math.round(this.buyThroughSuccessCount/(this.buyThroughSuccessCount+this.buyThroughFailureCount)*100);console.log(`\u{1F4C8} DoJobBuyThrough success rate: ${A}% (${this.buyThroughSuccessCount}/${this.buyThroughSuccessCount+this.buyThroughFailureCount})`)}this.clearState(),console.log("\u2728 Session completed - progress cleared")}catch(p){console.log(`\u274C Batch collection error: ${p}`)}}setInstantCompletionEnabled(a){this.attemptInstantCompletion=a,console.log(`\u{1F48E} Instant completion ${a?"enabled":"disabled"}`)}getInstantCompletionStats(){return{enabled:this.attemptInstantCompletion,successes:this.buyThroughSuccessCount,failures:this.buyThroughFailureCount}}setRuinSellingEnabled(a){this.attemptRuinSelling=a,console.log(`\u{1F5D1}\uFE0F Ruin selling ${a?"enabled":"disabled"}`)}getRuinSellingStats(){return{enabled:this.attemptRuinSelling,successes:this.ruinSellSuccessCount,failures:this.ruinSellFailureCount}}getHookStatus(){return{installed:e,callCount:t}}getCleanupHookStatus(){return{installed:n,callCount:r,autoEnabledCount:i}}reinstallHook(){console.log("\u{1F504} Attempting to reinstall GetCollectTime hook..."),o()}reinstallCleanupHook(){console.log("\u{1F504} Attempting to reinstall GoodyHutHelperConfig cleanup hook..."),s()}saveState(a){this.alternativeStatePaths.length===0&&this.initializeStoragePaths();let u={...a,version:this.stateVersion,processedEntityIndices:Array.from(a.processedEntityIndices)},d=JSON.stringify(u,null,2),p=!1,m=null;try{let S=this.findWritableStoragePath(),_=(yt(),me(mt));_.writeFileSync(S,d,"utf8");let x=_.readFileSync(S,"utf8");if(JSON.parse(x).version===this.stateVersion)console.log(`\u{1F4BE} State saved to ${S}: ${a.processedEntityIndices.size} processed instances, batch ${a.currentBatchIndex+1}/${a.totalBatches}`),p=!0;else throw new Error("State verification failed - data corruption detected")}catch(S){m=S,console.log(`\u274C Failed to save state to file: ${S.message||S}`),S.message&&S.message.includes("EACCES")?(console.log("\u{1F527} Permission Error: The file system is read-only or lacks write permissions"),console.log("\u{1F4A1} Try running the script with elevated permissions or check folder permissions")):S.message&&S.message.includes("ENOSPC")?console.log("\u{1F4BD} Disk Space Error: Insufficient disk space to save state file"):S.message&&S.message.includes("ENOENT")&&console.log("\u{1F4C1} Path Error: Directory does not exist or is inaccessible")}if(!p)try{globalThis.persistentState=d,console.log("\u{1F4BE} Fallback: State saved to memory (will be lost on restart)"),console.log("\u26A0\uFE0F File persistence is not working - progress will not survive script restarts"),console.log("\u{1F527} To fix: Ensure write permissions to workspace directory or run with elevated privileges")}catch(S){console.log("\u274C Critical: Both file and memory storage failed!"),console.log(`\u274C File error: ${m}`),console.log(`\u274C Memory error: ${S}`)}}loadState(){try{let a=null,u=null;if(this.memoryOnlyMode)if(this.memoryStorage&&Object.keys(this.memoryStorage).length>0)u=this.memoryStorage,console.log("\u{1F4C2} State loaded from memory storage");else return console.log("\u{1F4C2} No state found in memory storage"),null;else{this.alternativeStatePaths.length===0&&this.initializeStoragePaths();let _=null,x=!1;try{_=(yt(),me(mt)),x=_&&typeof _.readFileSync=="function"&&typeof _.existsSync=="function"}catch{console.log("\u26A0\uFE0F fs module not available, checking memory fallback"),x=!1}if(x)for(let A of this.alternativeStatePaths)try{if(_.existsSync(A)){a=_.readFileSync(A,"utf8"),console.log(`\u{1F4C2} State loaded from: ${A}`);break}}catch{continue}a||(this.memoryStorage&&Object.keys(this.memoryStorage).length>0?(u=this.memoryStorage,console.log("\u{1F4C2} State loaded from memory storage (file system unavailable)")):(a=globalThis.persistentState,a&&console.log("\u{1F4C2} Loading state from global memory fallback (no file found)")))}let d=null;if(u)d=u;else if(a)d=JSON.parse(a);else return null;if(d.version&&d.version!==this.stateVersion)return console.log(`\u26A0\uFE0F State version mismatch (saved: ${d.version}, current: ${this.stateVersion}), starting fresh`),this.clearState(),null;let m=(Date.now()-d.timestamp)/(1e3*60*60);if(m>this.stateExpirationHours)return console.log(`\u23F0 Saved state expired (${Math.round(m)}h old), starting fresh`),this.clearState(),null;if(!d.processedEntityIndices||!Array.isArray(d.processedEntityIndices))return console.log("\u274C Invalid state format, starting fresh"),this.clearState(),null;let S={...d,processedEntityIndices:new Set(d.processedEntityIndices)};return this.validateStateConsistency(S)?(console.log(`\u{1F4C2} State loaded: ${S.processedEntityIndices.size} processed instances, batch ${S.currentBatchIndex+1}/${S.totalBatches}`),S):(console.log("\u274C State consistency check failed, starting fresh"),this.clearState(),null)}catch(a){return console.log(`\u274C Failed to load state: ${a}`),console.log("\u{1F504} Starting with fresh state"),this.clearState(),null}}clearState(){try{let a=0,u=(yt(),me(mt));for(let d of this.alternativeStatePaths)try{u.existsSync(d)&&(u.unlinkSync(d),a++,console.log(`\u{1F5D1}\uFE0F Cleared state file: ${d}`))}catch(p){console.log(`\u26A0\uFE0F Could not clear ${d}: ${p.message||p}`)}delete globalThis.persistentState,this.currentWorkingPath="",a>0?console.log(`\u{1F5D1}\uFE0F Saved state cleared (${a} files removed)`):console.log("\u{1F5D1}\uFE0F Saved state cleared (no files found)")}catch(a){console.log(`\u274C Failed to clear state: ${a}`)}}createFreshState(){return{version:this.stateVersion,timestamp:Date.now(),processedEntityIndices:new Set,completedBatches:[],totalBatches:0,currentBatchIndex:0,hookStats:{installed:e,callCount:t},cleanupHookStats:{installed:n,callCount:r,autoEnabledCount:i},buyThroughStats:{successCount:this.buyThroughSuccessCount,failureCount:this.buyThroughFailureCount,enabled:this.attemptInstantCompletion},ruinSellStats:{successCount:this.ruinSellSuccessCount,failureCount:this.ruinSellFailureCount,enabled:this.attemptRuinSelling},sessionStats:{totalCollectionsStarted:0,totalInstantCompletions:0,totalRuinsProcessed:0,startTime:Date.now()}}}validateStateConsistency(a){try{if(!a.processedEntityIndices||!a.completedBatches||!a.sessionStats)return console.log("\u274C State validation failed: Missing required fields"),!1;if(a.currentBatchIndex<0||a.totalBatches<0)return console.log("\u274C State validation failed: Invalid batch indices"),!1;if(a.completedBatches.length>a.totalBatches)return console.log("\u274C State validation failed: More completed batches than total"),!1;let u=Date.now();if(a.timestamp>u||a.sessionStats.startTime>u)return console.log("\u274C State validation failed: Future timestamps detected"),!1;for(let d of a.completedBatches)if(d<1||d>a.totalBatches)return console.log(`\u274C State validation failed: Invalid batch number ${d}`),!1;return!0}catch(u){return console.log(`\u274C State validation error: ${u}`),!1}}getProgressStatus(){let a=this.loadState();if(!a)return{hasProgress:!1,summary:"No saved progress found"};let u=a.processedEntityIndices.size,d=a.completedBatches.length,p=a.totalBatches,m=Math.round((Date.now()-a.sessionStats.startTime)/1e3/60),S=p-d;return{hasProgress:!0,summary:`Progress: ${u} instances processed, ${d}/${p} batches completed (${S} remaining, saved ${m}m ago)`,details:{processedInstances:u,completedBatches:d,totalBatches:p,remainingBatches:S,sessionAgeMinutes:m,collectionsStarted:a.sessionStats.totalCollectionsStarted,instantCompletions:a.sessionStats.totalInstantCompletions}}}resetProgress(){this.clearState(),console.log("\u{1F504} All progress reset - next collection will start fresh")}showProgressDetails(){let a=this.loadState();if(!a){console.log("\u{1F4CA} No saved progress found"),console.log("\u{1F4A1} Use goodyManager.batchCollection() to start a new collection session");return}let u=Math.round((Date.now()-a.sessionStats.startTime)/1e3/60),d=a.totalBatches-a.completedBatches.length,p=Math.round(a.completedBatches.length/a.totalBatches*100);console.log("=== SAVED PROGRESS DETAILS ==="),console.log(`\u{1F4C5} Session started: ${new Date(a.sessionStats.startTime).toLocaleString()} (${u}m ago)`),console.log(`\u{1F4E6} Batches: ${a.completedBatches.length}/${a.totalBatches} completed (${p}% done)`),console.log(`\u23F3 Remaining: ${d} batches to process`),console.log(`\u{1F3AF} Instances: ${a.processedEntityIndices.size} processed`),console.log(`\u{1F4CA} Collections: ${a.sessionStats.totalCollectionsStarted} started, ${a.sessionStats.totalInstantCompletions} instant`),console.log(`\u{1F5D1}\uFE0F Ruins processed: ${a.sessionStats.totalRuinsProcessed||0}`),console.log(`\u26A1 Hook: ${a.hookStats.installed?"Active":"Inactive"} (${a.hookStats.callCount} calls)`),console.log(`\u{1F48E} DoJobBuyThrough: ${a.buyThroughStats.successCount} success, ${a.buyThroughStats.failureCount} failures`),a.ruinSellStats&&console.log(`\u{1F5D1}\uFE0F Ruin Selling: ${a.ruinSellStats.successCount} success, ${a.ruinSellStats.failureCount} failures`),a.cleanupHookStats&&console.log(`\u{1F9F9} Cleanup Hook: ${a.cleanupHookStats.installed?"Active":"Inactive"} (${a.cleanupHookStats.callCount} calls, ${a.cleanupHookStats.autoEnabledCount} auto-enabled)`),a.completedBatches.length>0&&console.log(`\u2705 Completed batches: ${a.completedBatches.join(", ")}`),console.log(`
\u{1F4A1} Use goodyManager.resume() to continue from where you left off`),console.log("\u{1F4A1} Use goodyManager.reset() to clear progress and start fresh")}async resumeCollection(){let a=this.getProgressStatus();if(!a.hasProgress)return console.log("\u{1F4CA} No saved progress found - starting fresh collection"),this.processBatchCollection(!1);console.log("\u{1F504} Resuming collection from saved progress..."),console.log(`\u{1F4CA} ${a.summary}`);let u=a.details;return u&&u.remainingBatches>0&&console.log(`\u23F3 Estimated remaining work: ${u.remainingBatches} batches`),this.processBatchCollection(!1)}async resetAndStartFresh(){let a=this.getProgressStatus();return a.hasProgress&&(console.log("\u26A0\uFE0F Clearing existing progress and starting fresh..."),console.log(`\u{1F4CA} ${a.summary}`),console.log("\u{1F5D1}\uFE0F This progress will be lost")),this.processBatchCollection(!0)}startRuinSellingTracer(){this.ruinSellingTracer&&this.ruinSellingTracer.startTracing()}stopRuinSellingTracer(){this.ruinSellingTracer&&this.ruinSellingTracer.stopTracing()}getRuinSellingTraceHistory(){return this.ruinSellingTracer?this.ruinSellingTracer.getTraceHistory():[]}getRuinSellingTraceSummary(){return this.ruinSellingTracer?this.ruinSellingTracer.getPerformanceSummary():null}getRuinSellingTraceLog(){return this.ruinSellingTracer?this.ruinSellingTracer.generateStructuredLog():"Tracer not available"}async sellRuins(){console.log("\u{1F5D1}\uFE0F Starting manual ruin selling operation..."),console.log("\u{1F50D} Scanning for completed GoodyHut instances with sellable ruins...");let a=this.getAllEntityInstances();if(!a||a.length===0)return console.log("\u274C No GoodyHut instances found"),{processed:0,successful:0,failed:0};let u=0,d=0,p=0,m=[];console.log(`\u{1F50D} Scanning ${a.length} total instances...`);for(let S=0;S<a.length;S++)try{let x=a[S].field("m_goodyHut");if(x&&x.value&&x.value!==null&&x.value.toString()!=="0x0"){let A=x.value,O=this.validateInstance(A,S);O.isValid&&O.state==="COMPLETED_AWAITING"&&O.rewardType==="GEMS"&&(m.push(O),console.log(`   Found completed GEMS instance: EntityController ${S} (${O.rewardAmount} gems)`))}}catch{continue}if(m.length===0)return console.log("\u2139\uFE0F No completed GEMS instances found that might have sellable ruins"),{processed:0,successful:0,failed:0};console.log(`\u{1F3AF} Found ${m.length} completed GEMS instances to process`),console.log("\u{1F5D1}\uFE0F Processing ruin selling operations...");for(let S of m){u++,console.log(`   Processing ${u}/${m.length}: EntityController ${S.entityIndex}`);try{let _=this.checkForSellableRuins(S);if(!_.hasRuins){console.log("     \u2139\uFE0F No sellable ruins found");continue}if(console.log(`     \u{1F48E} Found ${_.ruinType} - attempting to sell/clear...`),this.executeRuinSelling(S,{ruinType:_.ruinType,sellMethod:_.sellMethod})){d++,console.log(`     \u2705 Successfully sold/cleared ${_.ruinType}`),await new Promise(O=>setTimeout(O,500));let A=this.validateInstance(S.instance,S.entityIndex);A.isValid&&A.state==="IDLE_READY"&&console.log("     \u{1F389} Instance cleared and ready for future collection!")}else p++,console.log(`     \u274C Failed to sell/clear ${_.ruinType}`)}catch(_){p++,console.log(`     \u274C Error processing instance: ${_.message||_}`)}u<m.length&&await new Promise(_=>setTimeout(_,200))}return this.ruinSellSuccessCount+=d,this.ruinSellFailureCount+=p,console.log(`
\u{1F5D1}\uFE0F Manual ruin selling operation completed!`),console.log(`\u{1F4CA} Results: ${u} processed, ${d} successful, ${p} failed`),d>0&&(console.log(`\u2705 Successfully sold ruins for ${d} instances`),console.log("\u{1F4A1} These instances may now be available for future collection cycles")),p>0&&console.log(`\u26A0\uFE0F ${p} instances could not be processed (no ruins or operation failed)`),console.log(`\u{1F4C8} Total ruin selling stats: ${this.ruinSellSuccessCount} successful, ${this.ruinSellFailureCount} failed`),{processed:u,successful:d,failed:p}}getAllEntityInstances(){try{console.log("\u{1F50D} Discovering EntityController instances using Il2Cpp.gc.choose...");let u=Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController");if(!u)return console.log("\u274C EntityController class not found"),[];let d=Il2Cpp.gc.choose(u);return!d||d.length===0?(console.log("\u274C No EntityController instances found via Il2Cpp.gc.choose"),[]):(console.log(`\u2705 Successfully discovered ${d.length} EntityController instances`),console.log("\u{1F4CA} Using same instance discovery method as successful batch processing"),d)}catch(a){return console.log(`\u274C Critical error accessing EntityController instances: ${a.message||a}`),console.log("\u{1F4A1} Falling back to empty array - manual ruin selling will not be available"),[]}}}let c=new l;globalThis.goodyManager={scan:()=>c.scanAndValidateInstances(),showValid:()=>c.showValidInstances(),getBest:()=>c.getBestCollectibleInstance(),startCollection:()=>{let h=c.getBestCollectibleInstance();return h?c.executeStartCollect(h):(console.log("\u274C No collectible instances available"),!1)},batchCollection:()=>(console.log("\u{1F504} Starting batch collection (will resume from saved progress if available)..."),c.processBatchCollection()),resume:()=>(console.log("\u{1F4C2} Resuming collection from saved progress..."),c.resumeCollection()),reset:()=>(console.log("\u{1F504} Resetting progress and starting fresh..."),c.resetAndStartFresh()),enableInstantCompletion:()=>c.setInstantCompletionEnabled(!0),disableInstantCompletion:()=>c.setInstantCompletionEnabled(!1),getStats:()=>c.getInstantCompletionStats(),enableRuinSelling:()=>c.setRuinSellingEnabled(!0),disableRuinSelling:()=>c.setRuinSellingEnabled(!1),getRuinStats:()=>c.getRuinSellingStats(),sellRuins:()=>(console.log("\u{1F5D1}\uFE0F Manually triggering ruin selling operation..."),c.sellRuins()),startTracing:()=>{c.startRuinSellingTracer(),console.log("\u{1F50D} Ruin selling tracer started")},stopTracing:()=>{c.stopRuinSellingTracer(),console.log("\u23F9\uFE0F Ruin selling tracer stopped")},getTraceHistory:()=>c.getRuinSellingTraceHistory(),getTraceSummary:()=>{let h=c.getRuinSellingTraceSummary();return h?(console.log("\u{1F4CA} Ruin Selling Trace Summary:"),console.log(`   Total Calls: ${h.totalCalls}`),console.log(`   Successful: ${h.successfulCalls}`),console.log(`   Failed: ${h.failedCalls}`),console.log(`   Average Time: ${h.averageExecutionTimeMs.toFixed(2)}ms`)):console.log("\u274C Ruin selling tracer not available"),h},showTraceLog:()=>{let h=c.getRuinSellingTraceLog();return console.log(h),h},getHookStatus:()=>c.getHookStatus(),reinstallHook:()=>c.reinstallHook(),getCleanupHookStatus:()=>c.getCleanupHookStatus(),reinstallCleanupHook:()=>c.reinstallCleanupHook(),getProgress:()=>{let h=c.getProgressStatus();return console.log(h.hasProgress?`\u{1F4CA} ${h.summary}`:"\u{1F4CA} No saved progress found"),h},showProgress:()=>c.showProgressDetails(),resetProgress:()=>c.resetProgress(),help:()=>{console.log("=== GoodyHut Instance Manager Commands ==="),console.log("goodyManager.scan() - Scan and validate all instances"),console.log("goodyManager.showValid() - Show details of valid instances"),console.log("goodyManager.getBest() - Get best collectible instance (GEMS only)"),console.log("goodyManager.startCollection() - Start collection on best GEMS instance"),console.log("goodyManager.batchCollection() - Process all GEMS instances in batches (auto-resume)"),console.log(""),console.log("=== Enhanced Progress Management ==="),console.log("goodyManager.resume() - Resume from saved progress with detailed status"),console.log("goodyManager.reset() - Clear ALL progress and start completely fresh"),console.log("goodyManager.getProgress() - Check current progress status"),console.log("goodyManager.showProgress() - Show detailed progress with recommendations"),console.log("goodyManager.resetProgress() - Clear saved progress without starting collection"),console.log(""),console.log("=== Instant Completion Controls ==="),console.log("goodyManager.enableInstantCompletion() - Enable DoJobBuyThrough attempts"),console.log("goodyManager.disableInstantCompletion() - Disable DoJobBuyThrough attempts"),console.log("goodyManager.getStats() - Show instant completion statistics"),console.log(""),console.log("=== Ruin Selling Controls ==="),console.log("goodyManager.enableRuinSelling() - Enable automatic ruin selling for completed instances"),console.log("goodyManager.disableRuinSelling() - Disable automatic ruin selling"),console.log("goodyManager.getRuinStats() - Show ruin selling statistics"),console.log("goodyManager.sellRuins() - Manually trigger ruin selling for completed instances"),console.log("                              (Scans all instances, processes completed ones on-demand)"),console.log(""),console.log("=== Ruin Selling Tracer Controls ==="),console.log("goodyManager.startTracing() - Start tracing ruin selling method calls"),console.log("goodyManager.stopTracing() - Stop tracing operations"),console.log("goodyManager.getTraceHistory() - Get detailed trace history"),console.log("goodyManager.getTraceSummary() - Show performance summary"),console.log("goodyManager.showTraceLog() - Display structured trace log"),console.log(""),console.log("=== Hook Management ==="),console.log("goodyManager.getHookStatus() - Check GetCollectTime hook status"),console.log("goodyManager.reinstallHook() - Reinstall GetCollectTime hook if needed"),console.log("goodyManager.getCleanupHookStatus() - Check GoodyHutHelperConfig cleanup hook status"),console.log("goodyManager.reinstallCleanupHook() - Reinstall cleanup hook if needed"),console.log(""),console.log("=== Key Features ==="),console.log("\u{1F48E} GEMS ONLY: Only instances with GEMS rewards will be collected"),console.log("\u{1F4E6} BATCH MODE: Collections will start even if instant completion fails"),console.log("\u26A1 HOOK MODE: GetCollectTime hook provides instant completion without premium currency"),console.log("\u{1F9F9} CLEANUP HOOK: Automatically enables cleanup flag for non-collectible instances"),console.log("\uFFFD\uFE0F RUIN SELLING: Automatically sells ruins from completed instances to clear them"),console.log("\uFFFD\u{1F4BE} PERSISTENT STATE: Progress saved to goodyHutState.json, survives script restarts"),console.log("\u{1F504} SMART RESUME: Automatically skips already processed instances"),console.log("\u23F0 AUTO-EXPIRE: Saved progress expires after 24 hours"),console.log("\u{1F6E1}\uFE0F ERROR RECOVERY: Handles corrupted state files gracefully"),console.log("\u{1F3AF} MANUAL CLEANUP: On-demand ruin selling with goodyManager.sellRuins() command"),console.log("\u{1F4F1} CROSS-PLATFORM: Works on Windows, macOS, Linux, and Android/ARM64"),console.log("\u{1F527} ADAPTIVE STORAGE: Finds writable locations automatically across different platforms"),console.log(""),console.log("=== Quick Start ==="),console.log("1. goodyManager.batchCollection() - Start/resume collection (includes automatic ruin selling)"),console.log("2. goodyManager.showProgress() - Check current status and progress details"),console.log("3. goodyManager.sellRuins() - Manually clean up completed instances (optional)"),console.log("4. goodyManager.reset() - Force fresh start if needed"),console.log(""),console.log("=== Manual Ruin Selling Workflow ==="),console.log("\u2022 goodyManager.sellRuins() - Scan and process all completed instances"),console.log("\u2022 Returns: { processed: X, successful: Y, failed: Z }"),console.log("\u2022 Works independently of batch collection"),console.log("\u2022 Useful for on-demand cleanup without full collection cycle"),console.log(""),console.log("=== Troubleshooting ==="),console.log("\u2022 If no instances found: Check if you're in the game world view"),console.log("\u2022 If hook fails: Try goodyManager.reinstallHook() or goodyManager.reinstallCleanupHook()"),console.log("\u2022 If progress lost: Check file permissions for goodyHutState.json"),console.log("\u2022 If batch fails: Try goodyManager.reset() to start fresh"),console.log("\u2022 If storage fails: System will try multiple locations and fallback to memory"),console.log("\u2022 If ruin selling fails: Check goodyManager.getRuinStats() for details"),console.log("\u2022 For help: goodyManager.help()"),console.log(""),console.log("=== Platform-Specific Notes ==="),console.log("\u2022 Android/ARM64: System automatically detects and uses appropriate storage locations"),console.log("\u2022 Limited permissions: System will find writable directories automatically"),console.log("\u2022 Frida environment: Safe fallbacks when Node.js modules are unavailable"),console.log(""),console.log("\u{1F4A1} The system automatically saves progress and can resume after script restarts!"),console.log("\u{1F4A1} Only GEMS instances will be processed - other rewards are ignored for efficiency!"),console.log("\u{1F4A1} Use goodyManager.showProgress() to see detailed statistics and next steps!"),console.log("\u{1F4A1} Manual ruin selling (sellRuins) works independently of batch collection!")}},console.log("\u{1F527} Robust GoodyHutHelper Instance Manager loaded!"),console.log(`\u26A1 GetCollectTime hook status: ${e?"ACTIVE":"FAILED"}`),console.log(`\u{1F9F9} Cleanup hook status: ${n?"ACTIVE":"FAILED"}`);let f=c.getProgressStatus();f.hasProgress?(console.log(`\u{1F4CA} ${f.summary}`),console.log("\u{1F4A1} Use goodyManager.resume() to continue or goodyManager.reset() to start fresh")):(console.log("\u{1F4CA} No saved progress found"),console.log("\u{1F4A1} Use goodyManager.batchCollection() to start collecting GEMS")),console.log("Use goodyManager.help() for all available commands")});
