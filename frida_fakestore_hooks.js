/**
 * Unity FakeStore Automation Hooks
 * Hooks Unity's In-App Purchasing FakeStore system for automatic purchase completion
 */

// Global configuration
const CONFIG = {
    AUTO_PURCHASE: true,
    BYPASS_UI: true,
    FORCE_SUCCESS: true,
    LOG_PURCHASES: true,
    INSTANT_COMPLETION: true
};

console.log("[+] Unity FakeStore Automation Script Loading...");

Java.perform(function() {
    try {
        // Hook FakeStore class
        hookFakeStore();
        
        // Hook purchase callbacks
        hookPurchaseCallbacks();
        
        // Hook UI system
        hookUISystem();
        
        // Memory manipulation hooks
        setupMemoryHooks();
        
        console.log("[+] All FakeStore hooks installed successfully!");
        
    } catch (error) {
        console.log("[-] Error setting up hooks: " + error.message);
        // Fallback to memory-based hooks
        setupMemoryFallback();
    }
});

function hookFakeStore() {
    try {
        var FakeStore = Java.use("UnityEngine.Purchasing.FakeStore");
        
        // Hook main purchase method
        FakeStore.Purchase.overload('java.lang.String', 'java.lang.String').implementation = function(productJSON, developerPayload) {
            if (CONFIG.LOG_PURCHASES) {
                console.log("[+] Purchase intercepted:");
                console.log("    Product: " + productJSON);
                console.log("    Payload: " + developerPayload);
            }
            
            if (CONFIG.AUTO_PURCHASE) {
                console.log("[+] Auto-completing purchase...");
                // Force successful purchase
                this.FakePurchase(JSON.parse(productJSON), developerPayload);
                return;
            }
            
            // Call original
            this.Purchase(productJSON, developerPayload);
        };
        
        // Hook FakePurchase method
        FakeStore.FakePurchase.implementation = function(product, developerPayload) {
            console.log("[+] FakePurchase hooked - Product: " + product.id);
            
            if (CONFIG.FORCE_SUCCESS) {
                // Manipulate purchase to always succeed
                console.log("[+] Forcing purchase success for: " + product.id);
                
                // Add to purchased products list
                var purchasedList = this.m_PurchasedProducts.value;
                if (!purchasedList.contains(product.id)) {
                    purchasedList.add(product.id);
                }
                
                // Set purchase flags
                this.purchaseCalled.value = true;
                
                // Trigger success callback immediately
                if (this.m_Biller.value) {
                    var receipt = '{"productId":"' + product.id + '","transactionId":"fake_' + Date.now() + '"}';
                    this.m_Biller.value.OnPurchaseSucceeded(product.id, receipt, "fake_transaction_" + Date.now());
                }
                return;
            }
            
            // Call original
            this.FakePurchase(product, developerPayload);
        };
        
        // Hook Initialize method
        FakeStore.Initialize.implementation = function(biller) {
            console.log("[+] FakeStore Initialize hooked");
            
            // Call original
            this.Initialize(biller);
            
            // Set optimal configuration for automation
            if (CONFIG.BYPASS_UI) {
                // Set UIMode to silent (assuming 0 = silent)
                this.UIMode.value = 0;
                console.log("[+] UIMode set to silent");
            }
        };
        
        // Hook RestoreTransactions
        FakeStore.RestoreTransactions.implementation = function(callback) {
            console.log("[+] RestoreTransactions hooked");
            
            if (CONFIG.FORCE_SUCCESS) {
                // Always restore successfully
                this.restoreCalled.value = true;
                if (callback) {
                    callback.invoke(true, "Restore completed successfully");
                }
                return;
            }
            
            this.RestoreTransactions(callback);
        };
        
        console.log("[+] FakeStore main hooks installed");
        
    } catch (error) {
        console.log("[-] Failed to hook FakeStore: " + error.message);
    }
}

function hookPurchaseCallbacks() {
    try {
        // Hook the compiler-generated callback classes
        
        // Purchase callback
        var PurchaseCallback = Java.use("FakeStore.<>c__DisplayClass15_0");
        PurchaseCallback["<FakePurchase>g__handleAllowPurchase|0"].implementation = function(allow, failureReason) {
            if (CONFIG.FORCE_SUCCESS) {
                console.log("[+] Purchase callback hooked - forcing success");
                this["<FakePurchase>g__handleAllowPurchase|0"](true, 0); // Force allow=true, no failure
                return;
            }
            
            console.log("[+] Purchase callback: allow=" + allow + ", reason=" + failureReason);
            this["<FakePurchase>g__handleAllowPurchase|0"](allow, failureReason);
        };
        
        // Product retrieval callback
        var RetrievalCallback = Java.use("FakeStore.<>c__DisplayClass13_0");
        RetrievalCallback["<StoreRetrieveProducts>g__handleAllowInitializeOrRetrieveProducts|0"].implementation = function(allow, failureReason) {
            if (CONFIG.FORCE_SUCCESS) {
                console.log("[+] Product retrieval callback hooked - forcing success");
                this["<StoreRetrieveProducts>g__handleAllowInitializeOrRetrieveProducts|0"](true, 0);
                return;
            }
            
            this["<StoreRetrieveProducts>g__handleAllowInitializeOrRetrieveProducts|0"](allow, failureReason);
        };
        
        console.log("[+] Purchase callback hooks installed");
        
    } catch (error) {
        console.log("[-] Failed to hook callbacks: " + error.message);
    }
}

function hookUISystem() {
    try {
        var FakeStore = Java.use("UnityEngine.Purchasing.FakeStore");
        
        // Hook StartUI method to bypass dialogs
        FakeStore.StartUI.implementation = function(model, dialogType, callback) {
            if (CONFIG.BYPASS_UI) {
                console.log("[+] StartUI bypassed - auto-approving dialog type: " + dialogType);
                
                // Immediately call success callback
                if (callback) {
                    callback.invoke(true, model); // Approve with original model
                }
                return true;
            }
            
            return this.StartUI(model, dialogType, callback);
        };
        
        console.log("[+] UI bypass hooks installed");
        
    } catch (error) {
        console.log("[-] Failed to hook UI system: " + error.message);
    }
}

function setupMemoryHooks() {
    // Memory-based hooks for direct manipulation
    console.log("[+] Setting up memory manipulation hooks...");
    
    // Hook memory allocations to find FakeStore instances
    Interceptor.attach(Module.findExportByName("libc.so", "malloc"), {
        onEnter: function(args) {
            this.size = args[0].toInt32();
        },
        onLeave: function(retval) {
            if (this.size > 0x80 && this.size < 0x200) {
                // Potential FakeStore instance
                var ptr = retval;
                
                // Check if this looks like a FakeStore (has the right vtable pattern)
                try {
                    var vtable = ptr.readPointer();
                    if (vtable && vtable.readPointer()) {
                        // Store reference for later manipulation
                        this.potentialFakeStore = ptr;
                    }
                } catch (e) {
                    // Ignore read errors
                }
            }
        }
    });
}

function setupMemoryFallback() {
    console.log("[+] Setting up memory fallback hooks...");
    
    // Direct memory manipulation approach
    var baseAddr = Module.findBaseAddress("libil2cpp.so");
    if (!baseAddr) {
        console.log("[-] Could not find libil2cpp.so base address");
        return;
    }
    
    console.log("[+] libil2cpp.so base: " + baseAddr);
    
    // Hook known FakeStore method addresses if available
    var fakePurchaseAddr = baseAddr.add(0x4496A90); // From RVA in dump
    
    try {
        Interceptor.attach(fakePurchaseAddr, {
            onEnter: function(args) {
                console.log("[+] FakePurchase called via memory hook");
                
                if (CONFIG.FORCE_SUCCESS) {
                    // Manipulate return to force success
                    this.replace = true;
                }
            },
            onLeave: function(retval) {
                if (this.replace) {
                    console.log("[+] Forcing FakePurchase success via memory manipulation");
                    // Force success return
                }
            }
        });
        
        console.log("[+] Memory fallback hooks installed");
        
    } catch (error) {
        console.log("[-] Memory fallback failed: " + error.message);
    }
}

// Utility functions
function manipulateFakeStoreInstance(instance) {
    try {
        // Direct memory manipulation of FakeStore instance
        
        // Set UIMode to silent (offset 0x80)
        instance.add(0x80).writeU32(0);
        
        // Set purchaseCalled flag (offset 0x70)
        instance.add(0x70).writeU8(1);
        
        // Set restoreCalled flag (offset 0x71)
        instance.add(0x71).writeU8(1);
        
        console.log("[+] FakeStore instance manipulated for automation");
        
    } catch (error) {
        console.log("[-] Failed to manipulate FakeStore instance: " + error.message);
    }
}

// Export configuration for runtime modification
global.FakeStoreConfig = CONFIG;
global.manipulateFakeStore = manipulateFakeStoreInstance;

console.log("[+] Unity FakeStore Automation Script Loaded!");
console.log("[+] Use global.FakeStoreConfig to modify behavior at runtime");
