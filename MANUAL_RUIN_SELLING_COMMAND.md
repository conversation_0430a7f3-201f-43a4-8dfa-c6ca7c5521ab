# Manual Ruin Selling Command Feature

## New Command Implementation ✅

### **Command Added**: `goodyManager.sellRuins()`
A new user command that allows manual triggering of ruin selling operations for completed GoodyHut instances, providing on-demand cleanup without requiring a full batch collection cycle.

## ✅ Implementation Complete

### 1. **New Command Interface** - IMPLEMENTED
```javascript
// Manual ruin selling command
goodyManager.sellRuins()
// Returns: Promise<{ processed: number, successful: number, failed: number }>
```

### 2. **SellRuins() Method Integration** - IMPLEMENTED
- **Target Method**: Uses existing `SellRuins()` method from decompiled code
- **Token Reference**: 0x6002B93, Address RVA "0x209DE3C"
- **Integration**: Works through existing `executeRuinSelling()` method
- **Method Selection**: Automatically chooses appropriate selling method based on ruin type

### 3. **Instance Validation Logic** - IMPLEMENTED
- **Reuses Existing Logic**: Same validation as batch processing
- **Target State**: "COMPLETED_AWAITING" instances with GEMS rewards
- **Comprehensive Scanning**: Checks all current GoodyHut instances
- **Smart Filtering**: Only processes instances with sellable ruins

### 4. **Progress Tracking and Statistics** - IMPLEMENTED
- **Real-time Feedback**: Shows progress during execution
- **Success/Failure Tracking**: Counts successful and failed operations
- **Global Statistics**: Updates existing ruin selling counters
- **State Persistence**: Statistics saved across script restarts

### 5. **User Interface Integration** - IMPLEMENTED
- **Global Command**: Added to `goodyManager` interface
- **Help Documentation**: Included in help system with clear description
- **Progress Feedback**: Detailed logging during execution
- **Summary Results**: Clear results display after completion

## 🔧 Technical Implementation Details

### **Command Workflow**
```typescript
async sellRuins(): Promise<{ processed: number, successful: number, failed: number }> {
    // 1. Scan all EntityController instances
    const entityInstances = this.getAllEntityInstances();
    
    // 2. Validate and filter for completed GEMS instances
    const completedInstances = instances.filter(instance => 
        validation.state === "COMPLETED_AWAITING" && 
        validation.rewardType === "GEMS"
    );
    
    // 3. Process each instance for ruin selling
    for (const instance of completedInstances) {
        const ruinInfo = this.checkForSellableRuins(instance);
        if (ruinInfo.hasRuins) {
            const success = this.executeRuinSelling(instance, ruinInfo);
            // Track results and update statistics
        }
    }
    
    // 4. Return comprehensive results
    return { processed, successful, failed };
}
```

### **Instance Discovery**
- **EntityController Access**: Uses `EntityController.s_instances` field
- **List Iteration**: Safely iterates through all instances
- **Error Handling**: Skips invalid instances gracefully
- **Memory Safe**: Proper Il2Cpp object handling

### **Ruin Detection and Selling**
- **Multiple Methods**: `HasRuins()`, `HasDebris()`, `CanSell()`, `CanClear()`
- **Method Selection**: Automatically chooses `SellRuins()`, `ClearDebris()`, `DoSell()`, or `DoClear()`
- **Verification**: Re-validates instances after selling to confirm success
- **State Updates**: Tracks when instances become available for future collection

## 📊 Expected Command Output

### **Successful Operation**
```
🗑️ Manually triggering ruin selling operation...
🗑️ Starting manual ruin selling operation...
🔍 Scanning for completed GoodyHut instances with sellable ruins...
🔍 Scanning 1247 total instances...
   Found completed GEMS instance: EntityController 45 (25 gems)
   Found completed GEMS instance: EntityController 123 (50 gems)
   Found completed GEMS instance: EntityController 267 (25 gems)
🎯 Found 3 completed GEMS instances to process
🗑️ Processing ruin selling operations...
   Processing 1/3: EntityController 45
     💎 Found ruins - attempting to sell/clear...
     ✅ Successfully sold/cleared ruins
     🎉 Instance cleared and ready for future collection!
   Processing 2/3: EntityController 123
     💎 Found debris - attempting to sell/clear...
     ✅ Successfully sold/cleared debris
     🎉 Instance cleared and ready for future collection!
   Processing 3/3: EntityController 267
     ℹ️ No sellable ruins found

🗑️ Manual ruin selling operation completed!
📊 Results: 3 processed, 2 successful, 1 failed
✅ Successfully sold ruins for 2 instances
💡 These instances may now be available for future collection cycles
⚠️ 1 instances could not be processed (no ruins or operation failed)
📈 Total ruin selling stats: 47 successful, 8 failed
```

### **No Instances Found**
```
🗑️ Starting manual ruin selling operation...
🔍 Scanning for completed GoodyHut instances with sellable ruins...
🔍 Scanning 1247 total instances...
ℹ️ No completed GEMS instances found that might have sellable ruins

🗑️ Manual ruin selling operation completed!
📊 Results: 0 processed, 0 successful, 0 failed
```

### **Error Handling**
```
🗑️ Starting manual ruin selling operation...
❌ No GoodyHut instances found

🗑️ Manual ruin selling operation completed!
📊 Results: 0 processed, 0 successful, 0 failed
```

## 🎮 User Interface Integration

### **Command Usage**
```javascript
// Basic usage
goodyManager.sellRuins()

// With result handling
goodyManager.sellRuins().then(result => {
    console.log(`Processed: ${result.processed}, Successful: ${result.successful}, Failed: ${result.failed}`);
});

// Check statistics after
goodyManager.getRuinStats()
```

### **Help Documentation**
```
=== Ruin Selling Controls ===
goodyManager.enableRuinSelling() - Enable automatic ruin selling for completed instances
goodyManager.disableRuinSelling() - Disable automatic ruin selling
goodyManager.getRuinStats() - Show ruin selling statistics
goodyManager.sellRuins() - Manually trigger ruin selling for completed instances

=== Key Features ===
🎯 MANUAL CLEANUP: On-demand ruin selling with goodyManager.sellRuins() command
```

## 🔄 Integration with Existing Systems

### **Automatic vs Manual Ruin Selling**
- **Automatic**: Occurs during batch processing, integrated into validation workflow
- **Manual**: On-demand execution, independent of batch collection cycles
- **Shared Logic**: Both use same ruin detection and selling methods
- **Statistics**: Both update the same global counters and state persistence

### **State Persistence**
- **Statistics Tracking**: Manual operations update global ruin selling counters
- **Progress Saving**: Results are included in state persistence system
- **Cross-Session**: Statistics survive script restarts
- **Reporting**: Manual operations appear in progress displays

### **Complementary Functionality**
- **Batch Collection**: Automatic ruin selling during normal collection cycles
- **Manual Cleanup**: Targeted ruin selling without full batch processing
- **Progress Monitoring**: Both contribute to overall ruin selling statistics
- **User Control**: Users can choose automatic, manual, or both approaches

## 🎯 Use Cases and Benefits

### **When to Use Manual Ruin Selling**
1. **Quick Cleanup**: Clear ruins without running full batch collection
2. **Targeted Operations**: Process only completed instances without collecting new ones
3. **Troubleshooting**: Test ruin selling functionality independently
4. **Maintenance**: Regular cleanup of accumulated completed instances
5. **Preparation**: Clear instances before starting new collection cycles

### **Benefits Over Automatic**
- **Immediate Execution**: No need to wait for batch processing
- **Focused Operation**: Only processes ruin selling, no collection attempts
- **User Control**: Execute exactly when needed
- **Independent Testing**: Verify ruin selling works without other operations
- **Flexible Timing**: Run at any time regardless of collection state

### **Complementary to Automatic**
- **Comprehensive Coverage**: Automatic handles ongoing, manual handles on-demand
- **Efficiency**: Automatic during collection, manual for maintenance
- **User Choice**: Different approaches for different situations
- **Complete Solution**: Together provide full ruin management capability

## 📁 Files Updated

- **`src/robust-instance-handler.ts`** - Added `sellRuins()` method and integration
- **`MANUAL_RUIN_SELLING_COMMAND.md`** - This comprehensive feature documentation
- **`dist/robust-handler.js`** - Compiled with new command functionality

## ✅ Feature Complete

The manual ruin selling command is now fully implemented and provides:

1. **On-demand ruin selling** for completed GoodyHut instances
2. **Integration with existing SellRuins() method** from decompiled code
3. **Comprehensive progress tracking** and statistics
4. **User-friendly interface** with clear feedback and results
5. **Seamless integration** with existing automatic ruin selling system

Users can now trigger ruin cleanup operations manually using `goodyManager.sellRuins()` without needing to run a full batch collection cycle! 🎉
