import "frida-il2cpp-bridge";

Il2Cpp.perform(function(){
    console.log("🔧 Loading Robust GoodyHutHelper Instance Manager...");

    // Install GetCollectTime hook for instant collection completion
    let getCollectTimeHookInstalled = false;
    let getCollectTimeHookCallCount = 0;

    // GoodyHutHelperConfig cleanup hook tracking
    let cleanupHookInstalled = false;
    let cleanupHookCallCount = 0;
    let cleanupAutoEnabledCount = 0;

    function installGetCollectTimeHook(): void {
        try {
            console.log("🎯 Installing GetCollectTime hook for instant collection completion...");

            const AssemblyCSharp = Il2Cpp.domain.assembly("Assembly-CSharp").image;
            const GoodyHutHelper = AssemblyCSharp.class("GoodyHutHelper");

            if (!GoodyHutHelper) {
                console.log("❌ GoodyHutHelper class not found");
                return;
            }

            // Find the GetCollectTime method
            const getCollectTimeMethod = GoodyHutHelper.method("GetCollectTime");
            if (!getCollectTimeMethod) {
                console.log("❌ GetCollectTime method not found");
                return;
            }

            console.log(`🎯 Found GetCollectTime method at: ${getCollectTimeMethod.handle}`);

            // Install the hook
            getCollectTimeMethod.implementation = function() {
                getCollectTimeHookCallCount++;

                // Log occasionally to avoid spam (every 10th call)
                if (getCollectTimeHookCallCount % 10 === 1) {
                    console.log(`⚡ GetCollectTime hook triggered (call #${getCollectTimeHookCallCount}) - returning 0 for instant completion`);
                }

                // Return 0 to force instant completion
                return 0.0;
            };

            getCollectTimeHookInstalled = true;
            console.log("✅ GetCollectTime hook installed successfully!");
            console.log("💡 All GoodyHut collections will now complete instantly");

        } catch (error) {
            console.log(`❌ Failed to install GetCollectTime hook: ${error}`);
            getCollectTimeHookInstalled = false;
        }
    }

    // Install GoodyHutHelperConfig cleanup hook with enhanced ARM64/Android support
    function installCleanupHook(): void {
        try {
            console.log("🧹 Installing GoodyHutHelperConfig cleanup hook...");
            console.log(`🔧 Platform: ${require('os').platform()} ${require('os').arch()}`);

            const AssemblyCSharp = Il2Cpp.domain.assembly("Assembly-CSharp").image;
            const GoodyHutHelper = AssemblyCSharp.class("GoodyHutHelper");

            if (!GoodyHutHelper) {
                console.log("❌ GoodyHutHelper class not found for cleanup hook");
                console.log("💡 This may be normal on some game versions or platforms");
                return;
            }

            // Find the CanCollect method to hook
            const canCollectMethod = GoodyHutHelper.method("CanCollect");
            if (!canCollectMethod) {
                console.log("❌ CanCollect method not found for cleanup hook");
                console.log("💡 Trying alternative method names...");

                // Try alternative method names that might exist
                const alternativeNames = ["CanCollect", "canCollect", "IsCollectable", "CanBeCollected"];
                let foundMethod: any = null;

                for (const altName of alternativeNames) {
                    try {
                        foundMethod = GoodyHutHelper.method(altName);
                        if (foundMethod) {
                            console.log(`✅ Found alternative method: ${altName}`);
                            break;
                        }
                    } catch (e) {
                        continue;
                    }
                }

                if (!foundMethod) {
                    console.log("❌ No suitable method found for cleanup hook");
                    console.log("💡 Cleanup hook will be disabled, but automatic ruin selling will still work");
                    return;
                }
            }

            console.log(`🧹 Found CanCollect method at: ${canCollectMethod.handle}`);

            // Enhanced method implementation capture with multiple approaches
            let originalCanCollectImpl: any = null;

            try {
                // Approach 1: Direct implementation access
                originalCanCollectImpl = canCollectMethod.implementation;

                if (!originalCanCollectImpl) {
                    console.log("⚠️ Direct implementation access failed, trying alternative approaches...");

                    // Approach 2: Try to get the native function pointer
                    try {
                        const nativePtr = (canCollectMethod as any).nativeFunction;
                        if (nativePtr) {
                            originalCanCollectImpl = nativePtr;
                            console.log("✅ Using native function pointer approach");
                        }
                    } catch (e) {
                        console.log(`⚠️ Native function pointer approach failed: ${e}`);
                    }

                    // Approach 3: Create a wrapper that calls the original method
                    if (!originalCanCollectImpl) {
                        originalCanCollectImpl = function() {
                            try {
                                return canCollectMethod.invoke();
                            } catch (e) {
                                console.log(`⚠️ Original method invocation failed: ${e}`);
                                return false; // Safe default
                            }
                        };
                        console.log("✅ Using wrapper function approach");
                    }
                }
            } catch (captureError: any) {
                console.log(`⚠️ Method capture error: ${captureError.message || captureError}`);
                originalCanCollectImpl = null;
            }

            // Final validation
            if (!originalCanCollectImpl) {
                console.log("❌ Could not capture original CanCollect implementation with any approach");
                console.log("💡 Cleanup hook will be disabled, but other functionality will continue");
                return;
            }

            console.log("✅ Successfully captured original CanCollect implementation");

            // Install the hook to automatically enable cleanup when CanCollect returns false
            canCollectMethod.implementation = function() {
                cleanupHookCallCount++;

                let originalResult: any;

                try {
                    // Safer method invocation with multiple fallback approaches
                    if (originalCanCollectImpl && typeof (originalCanCollectImpl as any).call === 'function') {
                        try {
                            originalResult = (originalCanCollectImpl as any).call(this);
                        } catch (callError: any) {
                            // If .call() fails, try direct invocation
                            if (typeof originalCanCollectImpl === 'function') {
                                originalResult = originalCanCollectImpl();
                            } else {
                                throw callError;
                            }
                        }
                    } else if (originalCanCollectImpl && typeof originalCanCollectImpl === 'function') {
                        // Direct function call
                        originalResult = originalCanCollectImpl();
                    } else {
                        // Last resort: return true to avoid crashes
                        console.log(`⚠️ No valid original implementation available, returning safe default`);
                        return true; // Safe default that won't crash
                    }
                } catch (invocationError: any) {
                    // Critical error handling to prevent game crashes
                    const errorMsg = invocationError.message || invocationError.toString();

                    if (errorMsg.includes('abort') || errorMsg.includes('crash') || errorMsg.includes('segmentation')) {
                        console.log(`🚨 Critical error in CanCollect hook: ${errorMsg}`);
                        console.log(`🛡️ Disabling cleanup hook to prevent further crashes`);

                        // Restore original method to prevent further crashes
                        try {
                            canCollectMethod.implementation = originalCanCollectImpl || function() { return true; };
                        } catch (restoreError) {
                            // If we can't restore, at least return a safe value
                        }

                        return true; // Safe default
                    }

                    // For non-critical errors, try one more fallback
                    try {
                        console.log(`⚠️ CanCollect invocation failed, using safe fallback: ${errorMsg}`);
                        return true; // Safe default that allows processing to continue
                    } catch (fallbackError: any) {
                        console.log(`❌ All CanCollect fallbacks failed: ${fallbackError.message || fallbackError}`);
                        return true; // Ultimate safe default
                    }
                }

                // If CanCollect returns false, try to enable cleanup
                if (originalResult === false) {
                    try {
                        // Try to access the GoodyHutHelperConfig
                        const configField = this.field("m_config");
                        if (configField && configField.value && configField.value !== null) {
                            const config = configField.value as Il2Cpp.Object;

                            // Check current cleanup state
                            const cleanupField = config.field("cleanUp");
                            if (cleanupField && cleanupField.value === false) {
                                // Set cleanup to true (offset 0x30 as per C# decompiled code)
                                cleanupField.value = true;
                                cleanupAutoEnabledCount++;

                                // Log occasionally to avoid spam (every 10th auto-enable)
                                if (cleanupAutoEnabledCount % 10 === 1) {
                                    console.log(`🧹 Auto-enabled cleanup for non-collectible instance (count: ${cleanupAutoEnabledCount})`);
                                }
                            }
                        }
                    } catch (configError) {
                        // Silently handle config access errors to avoid spam
                        // Only log on first few errors
                        if (cleanupHookCallCount <= 5) {
                            console.log(`⚠️ Could not access GoodyHutHelperConfig: ${configError}`);
                        }
                    }
                }

                return originalResult;
            };

            cleanupHookInstalled = true;
            console.log("✅ GoodyHutHelperConfig cleanup hook installed successfully!");
            console.log("💡 Cleanup will be automatically enabled for non-collectible instances");

        } catch (error) {
            console.log(`❌ Failed to install cleanup hook: ${error}`);
            cleanupHookInstalled = false;
        }
    }

    // Install both hooks immediately
    installGetCollectTimeHook();
    installCleanupHook();

    // Robust GoodyHutHelper Instance Handler
    // Handles memory access violations and validates instances

    interface ValidatedInstance {
        instance: Il2Cpp.Object;
        entityIndex: number;
        isValid: boolean;
        canCollect: boolean;
        canBuyThrough: boolean;
        state: string;
        rewardType: string;
        rewardAmount: any;
        error?: string;
    }

    // State persistence interface
    interface CollectionState {
        version: number;
        timestamp: number;
        processedEntityIndices: Set<number>;
        completedBatches: number[];
        totalBatches: number;
        currentBatchIndex: number;
        hookStats: {
            installed: boolean;
            callCount: number;
        };
        cleanupHookStats: {
            installed: boolean;
            callCount: number;
            autoEnabledCount: number;
        };
        buyThroughStats: {
            successCount: number;
            failureCount: number;
            enabled: boolean;
        };
        ruinSellStats: {
            successCount: number;
            failureCount: number;
            enabled: boolean;
        };
        sessionStats: {
            totalCollectionsStarted: number;
            totalInstantCompletions: number;
            totalRuinsProcessed: number;
            startTime: number;
        };
    }

class GoodyHutInstanceManager {
    private validInstances: ValidatedInstance[] = [];
    private collectibleInstances: ValidatedInstance[] = [];
    private batchSize: number = 10;
    private batchDelay: number = 2000; // 2 seconds between batches (increased for stability)

    constructor() {
        // Initialize storage paths on creation
        this.initializeStoragePaths();
        // Initialize ruin selling tracer
        this.initializeRuinSellingTracer();
    }

    // Initialize ruin selling tracer
    private initializeRuinSellingTracer(): void {
        try {
            // Create tracer instance with configuration
            this.ruinSellingTracer = {
                isTracing: () => false,
                startTracing: () => console.log('🔍 Ruin selling tracer started'),
                stopTracing: () => console.log('⏹️ Ruin selling tracer stopped'),
                traceMethodCall: (methodName: string, instance: any, parameters: any[], sessionId: string, entityIndex?: number) => {
                    const traceId = `trace_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                    console.log(`📞 [${traceId}] ${methodName} called on entity ${entityIndex || 'unknown'} (session: ${sessionId})`);
                    return traceId;
                },
                traceMethodCompletion: (traceId: string, returnValue?: any) => {
                    console.log(`✅ [${traceId}] Method completed with result: ${JSON.stringify(returnValue)}`);
                },
                traceMethodError: (traceId: string, error: Error) => {
                    console.log(`❌ [${traceId}] Method failed: ${error.message}`);
                },
                getTraceHistory: () => [],
                clearTraceHistory: () => console.log('🗑️ Trace history cleared'),
                getPerformanceSummary: () => ({
                    totalCalls: 0,
                    successfulCalls: 0,
                    failedCalls: 0,
                    averageExecutionTimeMs: 0,
                    minExecutionTimeMs: 0,
                    maxExecutionTimeMs: 0,
                    totalExecutionTimeMs: 0
                }),
                generateStructuredLog: () => 'No trace data available'
            };

            console.log('✅ Ruin selling tracer initialized');
        } catch (error: any) {
            console.log(`⚠️ Could not initialize ruin selling tracer: ${error.message || error}`);
            this.ruinSellingTracer = null;
        }
    }

    // DoJobBuyThrough configuration and tracking
    private attemptInstantCompletion: boolean = true;
    private buyThroughFailureCount: number = 0;
    private buyThroughSuccessCount: number = 0;
    private buyThroughFailureThreshold: number = 20; // Stop attempting after 20 consecutive failures

    // Ruin selling configuration and tracking
    private attemptRuinSelling: boolean = true;
    private ruinSellSuccessCount: number = 0;
    private ruinSellFailureCount: number = 0;

    // State persistence
    private stateFilePath: string = "/sdcard/Android/data/com.nexonm.dominations.adk/files/goodyHutState.json";
    private alternativeStatePaths: string[] = [];
    private currentWorkingPath: string = "";
    private memoryOnlyMode: boolean = false;
    private memoryStorage: any = {};
    private stateExpirationHours: number = 24; // Reset state after 24 hours
    private stateVersion: number = 2; // Increment when state structure changes

    // Ruin selling tracer integration
    private ruinSellingTracer: any = null;
    private tracerIntegration: any = null;

    // Initialize alternative storage paths for cross-platform compatibility
    private initializeStoragePaths(): void {
        try {
            // Safe module loading with fallbacks for Frida environment
            let os: any = null;
            let path: any = null;
            let fs: any = null;

            try {
                os = require('os');
            } catch (e) {
                console.log(`⚠️ os module not available: ${e}`);
            }

            try {
                path = require('path');
            } catch (e) {
                console.log(`⚠️ path module not available: ${e}`);
            }

            try {
                fs = require('fs');
            } catch (e) {
                console.log(`⚠️ fs module not available: ${e}`);
            }

            // Fallback path operations for environments without Node.js path module
            const pathJoin = (...parts: string[]): string => {
                if (path && typeof path.join === 'function') {
                    return path.join(...parts);
                }
                // Fallback: manual path joining
                return parts.filter(p => p && p.length > 0).join('/').replace(/\/+/g, '/');
            };

            const pathResolve = (p: string): string => {
                if (path && typeof path.resolve === 'function') {
                    return path.resolve(p);
                }
                // Fallback: return as-is or make absolute
                return p.startsWith('/') ? p : `/${p}`;
            };

            // Platform detection with fallbacks
            const platform = os && typeof os.platform === 'function' ? os.platform() : 'linux';
            const arch = os && typeof os.arch === 'function' ? os.arch() : 'unknown';
            const homedir = os && typeof os.homedir === 'function' ? os.homedir() : '/data';
            const tmpdir = os && typeof os.tmpdir === 'function' ? os.tmpdir() : '/tmp';

            let workingDir = '';
            try {
                workingDir = process.cwd();
            } catch (e) {
                workingDir = '/data';
            }

            console.log(`🖥️ Detected platform: ${platform} (${arch})`);
            console.log(`📁 Process working directory: ${workingDir}`);
            console.log(`🏠 User home directory: ${homedir}`);
            console.log(`📂 System temp directory: ${tmpdir}`);

            // Use only the specified Android location
            const potentialPaths: string[] = [];

            // Primary location: Android app external files directory
            const androidPath = "/sdcard/Android/data/com.nexonm.dominations.adk/files/goodyHutState.json";
            potentialPaths.push(androidPath);

            console.log(`📱 Using specified Android storage location: ${androidPath}`);

            // Filter and validate paths with safe operations
            this.alternativeStatePaths = [];

            // Safe directory name extraction
            const getDirname = (filePath: string): string => {
                if (path && typeof path.dirname === 'function') {
                    return path.dirname(filePath);
                }
                // Fallback: manual dirname extraction
                const lastSlash = filePath.lastIndexOf('/');
                return lastSlash > 0 ? filePath.substring(0, lastSlash) : '/';
            };

            for (const testPath of potentialPaths) {
                try {
                    const dir = getDirname(testPath);

                    // Check if directory exists or can be created (with fs module safety)
                    if (fs && typeof fs.existsSync === 'function') {
                        if (!fs.existsSync(dir)) {
                            try {
                                if (typeof fs.mkdirSync === 'function') {
                                    fs.mkdirSync(dir, { recursive: true });
                                    console.log(`📁 Created directory: ${dir}`);
                                }
                            } catch (mkdirError: any) {
                                console.log(`❌ Cannot create directory ${dir}: ${mkdirError.message || mkdirError}`);
                                continue; // Skip this path
                            }
                        }
                    } else {
                        console.log(`⚠️ fs module not available, cannot verify directory: ${dir}`);
                    }

                    // Add to valid paths list
                    this.alternativeStatePaths.push(testPath);

                } catch (pathError: any) {
                    console.log(`⚠️ Invalid path ${testPath}: ${pathError.message || pathError}`);
                    continue;
                }
            }

            // Ensure we always have at least one storage location
            if (this.alternativeStatePaths.length === 0) {
                console.log("⚠️ No storage locations were successfully initialized, adding emergency fallbacks...");

                // Emergency fallbacks that should work on most systems
                this.alternativeStatePaths = [
                    "goodyHutState.json",                    // Current directory
                    "/tmp/goodyHutState.json",               // Standard temp (Linux/Unix)
                    "/data/goodyHutState.json",              // Android fallback
                    "./state/goodyHutState.json"             // Relative subdirectory
                ];
            }

            console.log(`📁 Initialized ${this.alternativeStatePaths.length} potential storage locations:`);
            this.alternativeStatePaths.forEach((p, i) => {
                console.log(`   ${i + 1}. ${p}`);
            });

        } catch (error: any) {
            console.log(`⚠️ Could not initialize alternative storage paths: ${error.message || error}`);
            // Absolute fallback to ensure we always have storage options
            this.alternativeStatePaths = [
                "goodyHutState.json",
                "/tmp/goodyHutState.json",
                "/data/goodyHutState.json",
                "./goodyHutState.json"
            ];
            console.log(`📁 Using emergency fallback storage locations (${this.alternativeStatePaths.length} paths)`);
        }
    }

    // Find the first writable storage location
    private findWritableStoragePath(): string {
        if (this.currentWorkingPath) {
            // Verify cached path is still writable
            if (this.testPathWritability(this.currentWorkingPath)) {
                return this.currentWorkingPath;
            } else {
                console.log(`⚠️ Previously working path ${this.currentWorkingPath} is no longer writable`);
                this.currentWorkingPath = ""; // Clear cache
            }
        }

        console.log(`🔍 Testing ${this.alternativeStatePaths.length} storage locations for write access...`);

        for (let i = 0; i < this.alternativeStatePaths.length; i++) {
            const testPath = this.alternativeStatePaths[i];
            console.log(`   Testing ${i + 1}/${this.alternativeStatePaths.length}: ${testPath}`);

            if (this.testPathWritability(testPath)) {
                this.currentWorkingPath = testPath;
                console.log(`✅ Found writable storage location: ${testPath}`);
                return testPath;
            }
        }

        // If no writable path found, enable memory-only mode
        console.log(`❌ No writable storage locations found!`);
        console.log(`💾 Enabling memory-only storage mode`);
        console.log(`⚠️ Progress will be lost when script restarts`);

        this.memoryOnlyMode = true;
        this.provideTroubleshootingGuidance();
        return this.stateFilePath; // Return original as fallback (won't be used in memory mode)
    }

    // Test if a specific path is writable with Frida-compatible operations
    private testPathWritability(testPath: string): boolean {
        try {
            // Try to load fs module safely
            let fs: any = null;
            let fsAvailable = false;

            try {
                fs = require('fs');
                fsAvailable = (fs && typeof fs.writeFileSync === 'function');
            } catch (fsError) {
                console.log(`     ⚠️ fs module not available: ${fsError}`);
                fsAvailable = false;
            }

            if (!fsAvailable) {
                console.log(`     ❌ File system operations not available in this environment`);
                console.log(`     💡 Falling back to memory-only storage`);
                return false;
            }

            // Safe directory name extraction
            const getDirname = (filePath: string): string => {
                const lastSlash = filePath.lastIndexOf('/');
                return lastSlash > 0 ? filePath.substring(0, lastSlash) : '/';
            };

            // Ensure directory exists with safe operations
            const dir = getDirname(testPath);

            try {
                if (typeof fs.existsSync === 'function' && !fs.existsSync(dir)) {
                    if (typeof fs.mkdirSync === 'function') {
                        fs.mkdirSync(dir, { recursive: true });
                        console.log(`     📁 Created directory: ${dir}`);
                    } else {
                        console.log(`     ⚠️ Cannot create directory - mkdirSync not available`);
                        return false;
                    }
                }
            } catch (mkdirError: any) {
                console.log(`     ❌ Cannot create directory: ${mkdirError.message || mkdirError}`);
                return false;
            }

            // Test write capability with safe operations
            const testData = JSON.stringify({
                test: true,
                timestamp: Date.now(),
                platform: 'frida-environment',
                testId: Math.random().toString(36).substring(7)
            });

            try {
                if (typeof fs.writeFileSync === 'function') {
                    fs.writeFileSync(testPath, testData, 'utf8');
                    console.log(`     ✅ Write test successful`);
                } else {
                    console.log(`     ❌ writeFileSync not available`);
                    return false;
                }
            } catch (writeError: any) {
                console.log(`     ❌ Write failed: ${writeError.message || writeError}`);
                return false;
            }

            // Test read capability with safe operations
            try {
                if (typeof fs.readFileSync === 'function') {
                    const readData = fs.readFileSync(testPath, 'utf8');
                    const parsed = JSON.parse(readData);

                    if (parsed.test !== true) {
                        console.log(`     ❌ Read verification failed - data corruption`);
                        return false;
                    }

                    console.log(`     ✅ Read verification successful`);
                } else {
                    console.log(`     ❌ readFileSync not available`);
                    return false;
                }
            } catch (readError: any) {
                console.log(`     ❌ Read failed: ${readError.message || readError}`);
                return false;
            }

            // Test delete capability with safe operations
            try {
                if (typeof fs.unlinkSync === 'function') {
                    fs.unlinkSync(testPath);
                    console.log(`     ✅ Delete test successful`);
                } else {
                    console.log(`     ⚠️ unlinkSync not available - test file may remain`);
                    // Don't fail the test just because we can't clean up
                }
            } catch (deleteError: any) {
                console.log(`     ⚠️ Delete failed: ${deleteError.message || deleteError}`);
                // Don't fail the test just because we can't clean up
            }

            return true;

        } catch (error: any) {
            const errorMsg = error.message || error.toString();
            console.log(`     ❌ Failed: ${errorMsg}`);

            // Provide specific error analysis
            if (errorMsg.includes('not a function')) {
                console.log(`     🔧 Issue: File system API not available in Frida environment`);
                console.log(`     💡 System will use memory-only storage`);
            } else if (errorMsg.includes('EACCES') || errorMsg.includes('permission denied')) {
                console.log(`     🔧 Issue: Permission denied - insufficient access rights`);
            } else if (errorMsg.includes('ENOENT') || errorMsg.includes('no such file')) {
                console.log(`     🔧 Issue: Path does not exist and cannot be created`);
            } else if (errorMsg.includes('ENOSPC') || errorMsg.includes('no space')) {
                console.log(`     🔧 Issue: Insufficient disk space`);
            } else if (errorMsg.includes('EROFS') || errorMsg.includes('read-only')) {
                console.log(`     🔧 Issue: File system is read-only`);
            } else {
                console.log(`     🔧 Issue: ${errorMsg}`);
            }

            return false;
        }
    }

    // Provide detailed troubleshooting guidance based on platform
    private provideTroubleshootingGuidance(): void {
        const os = require('os');
        const platform = os.platform();

        console.log(`\n🔧 TROUBLESHOOTING GUIDE for ${platform.toUpperCase()}`);
        console.log(`═══════════════════════════════════════════════════`);

        if (platform === 'win32') {
            console.log(`📋 Windows-specific solutions:`);
            console.log(`   1. Run script as Administrator (right-click → "Run as administrator")`);
            console.log(`   2. Check folder permissions in File Explorer`);
            console.log(`   3. Disable antivirus real-time protection temporarily`);
            console.log(`   4. Try running from a different directory (e.g., Desktop)`);
            console.log(`   5. Check if disk is full (run 'dir' to see free space)`);

        } else if (platform === 'darwin') {
            console.log(`📋 macOS-specific solutions:`);
            console.log(`   1. Run with sudo: sudo node your-script.js`);
            console.log(`   2. Check permissions: ls -la`);
            console.log(`   3. Grant Full Disk Access in System Preferences → Security & Privacy`);
            console.log(`   4. Try running from ~/Documents or ~/Desktop`);

        } else {
            console.log(`📋 Linux/Unix-specific solutions:`);
            console.log(`   1. Run with sudo: sudo node your-script.js`);
            console.log(`   2. Check permissions: ls -la`);
            console.log(`   3. Check disk space: df -h`);
            console.log(`   4. Check if /tmp is mounted read-only: mount | grep tmp`);
            console.log(`   5. Try creating ~/dominations directory manually`);
        }

        console.log(`\n🔍 General debugging steps:`);
        console.log(`   • Check current user: ${os.userInfo().username}`);
        console.log(`   • Check working directory: ${process.cwd()}`);
        console.log(`   • Check available disk space`);
        console.log(`   • Try running from a different location`);
        console.log(`   • Check if antivirus/security software is blocking file access`);
        console.log(`═══════════════════════════════════════════════════\n`);
    }

    // Safe method invocation with null pointer protection and parameter handling
    safeInvoke(instance: Il2Cpp.Object, methodName: string, ...args: any[]): any {
        try {
            // First check if instance is null or invalid
            if (!instance || instance.isNull()) {
                return { error: "Null instance", value: null };
            }

            // Try to get the method
            const method = instance.method(methodName);
            if (!method) {
                return { error: `Method ${methodName} not found`, value: null };
            }

            // Try to invoke the method with different parameter approaches
            let result: any;

            try {
                // First try with provided arguments
                if (args.length > 0) {
                    result = (method as any).invoke(...args);
                } else {
                    result = method.invoke();
                }
            } catch (paramError: any) {
                const paramErrorMsg = String(paramError);

                // Handle "bad argument count" errors by trying different parameter combinations
                if (paramErrorMsg.includes("bad argument count")) {
                    console.log(`⚠️ ${methodName} invocation failed, trying parameter variations: ${paramErrorMsg}`);

                    // Try common parameter patterns for GoodyHut methods
                    const parameterAttempts = [
                        [], // No parameters
                        [true], // Boolean parameter
                        [false], // Boolean parameter (opposite)
                        [0], // Integer parameter
                        [1], // Integer parameter
                        [null], // Null parameter
                        [true, 0], // Boolean + integer
                        [false, 0], // Boolean + integer
                    ];

                    for (const params of parameterAttempts) {
                        try {
                            result = (method as any).invoke(...params);
                            // If successful, log the working parameters for future reference
                            if (params.length > 0) {
                                console.log(`✅ ${methodName} succeeded with parameters: [${params.join(', ')}]`);
                            }
                            break;
                        } catch (attemptError) {
                            continue; // Try next parameter combination
                        }
                    }

                    // If all parameter attempts failed, return safe fallback
                    if (result === undefined) {
                        console.log(`⚠️ ${methodName} invocation failed, using safe fallback: ${paramErrorMsg}`);

                        // Return safe defaults based on method name
                        if (methodName === "CanCollect") {
                            return { error: null, value: true }; // Assume collectible for safety
                        } else if (methodName === "IsJobComplete") {
                            return { error: null, value: false }; // Assume not complete for safety
                        } else if (methodName === "CanBuyThrough") {
                            return { error: null, value: false }; // Assume can't buy through for safety
                        } else if (methodName.includes("Has") || methodName.includes("Can")) {
                            return { error: null, value: false }; // Boolean methods default to false
                        } else if (methodName.includes("Get") && methodName.includes("Amount")) {
                            return { error: null, value: 0 }; // Amount methods default to 0
                        } else if (methodName.includes("Get") && methodName.includes("Type")) {
                            return { error: null, value: "UNKNOWN" }; // Type methods default to UNKNOWN
                        } else {
                            return { error: `Parameter error: ${paramErrorMsg}`, value: null };
                        }
                    }
                } else {
                    throw paramError; // Re-throw non-parameter errors
                }
            }

            return { error: null, value: result };

        } catch (error) {
            // Catch access violations and other errors
            const errorMsg = String(error);
            if (errorMsg.includes("access violation") || errorMsg.includes("0x0")) {
                return { error: "Access violation - invalid instance", value: null };
            }
            return { error: `Method error: ${errorMsg}`, value: null };
        }
    }
    
    // Validate a single GoodyHutHelper instance
    validateInstance(instance: Il2Cpp.Object, entityIndex: number): ValidatedInstance {
        console.log(`Validating instance from EntityController ${entityIndex}...`);
        
        const result: ValidatedInstance = {
            instance,
            entityIndex,
            isValid: false,
            canCollect: false,
            canBuyThrough: false,
            state: "UNKNOWN",
            rewardType: "UNKNOWN",
            rewardAmount: null
        };
        
        // Test basic method calls
        const isJobCompleteResult = this.safeInvoke(instance, "IsJobComplete");
        if (isJobCompleteResult.error) {
            result.error = isJobCompleteResult.error;
            return result;
        }

        const canCollectResult = this.safeInvoke(instance, "CanCollect");
        if (canCollectResult.error) {
            result.error = canCollectResult.error;
            return result;
        }

        const canBuyThroughResult = this.safeInvoke(instance, "CanBuyThrough");
        if (canBuyThroughResult.error) {
            result.error = canBuyThroughResult.error;
            return result;
        }

        // Get reward information
        const rewardTypeResult = this.safeInvoke(instance, "GetRewardType");
        const rewardAmountResult = this.safeInvoke(instance, "GetRewardAmount");

        // If we get here, instance is valid
        result.isValid = true;
        result.canCollect = canCollectResult.value;
        result.canBuyThrough = canBuyThroughResult.value;
        result.rewardType = rewardTypeResult.error ? "UNKNOWN" : String(rewardTypeResult.value);
        result.rewardAmount = rewardAmountResult.error ? null : rewardAmountResult.value;
        
        // Determine state
        const isJobComplete = isJobCompleteResult.value;
        if (isJobComplete === true && result.canCollect === true) {
            result.state = "IDLE_READY";
        } else if (isJobComplete === false && result.canCollect === false) {
            result.state = "COLLECTING";
        } else if (isJobComplete === true && result.canCollect === false) {
            result.state = "COMPLETED_AWAITING";
        } else {
            result.state = "INCONSISTENT";
        }
        
        console.log(`✅ EntityController ${entityIndex}: Valid - ${result.state} (CanCollect: ${result.canCollect}, Reward: ${result.rewardAmount} ${result.rewardType})`);
        return result;
    }
    
    // Scan all EntityController instances and validate GoodyHutHelper components
    scanAndValidateInstances(): void {
        console.log("🔍 Starting comprehensive instance validation...");
        
        this.validInstances = [];
        this.collectibleInstances = [];
        
        try {
            const EntityController = Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController");
            const entityInstances = Il2Cpp.gc.choose(EntityController);
            
            console.log(`Found ${entityInstances.length} EntityController instances`);
            console.log("Validating GoodyHutHelper components...\n");
            
            let validCount = 0;
            let invalidCount = 0;
            let collectibleCount = 0;
            let gemsCount = 0;

            entityInstances.forEach((entity: any, index: number) => {
                try {
                    const goodyHut = entity.field("m_goodyHut");
                    if (goodyHut && goodyHut.value && goodyHut.value !== null && goodyHut.value.toString() !== "0x0") {
                        const goodyHutInstance = goodyHut.value as Il2Cpp.Object;
                        const validation = this.validateInstance(goodyHutInstance, index);

                        if (validation.isValid) {
                            this.validInstances.push(validation);
                            validCount++;

                            if (validation.canCollect) {
                                this.collectibleInstances.push(validation);
                                collectibleCount++;

                                if (validation.rewardType === "GEMS") {
                                    gemsCount++;
                                }
                            }
                        } else {
                            invalidCount++;
                        }
                    }
                } catch (error) {
                    invalidCount++;
                }
            });

            console.log("\n=== VALIDATION SUMMARY ===");
            console.log(`✅ Valid instances: ${validCount}`);
            console.log(`❌ Invalid instances: ${invalidCount}`);
            console.log(`🎯 Collectible instances: ${collectibleCount}`);
            console.log(`💎 GEMS collectible instances: ${gemsCount}`);
            console.log(`📊 Success rate: ${Math.round((validCount / (validCount + invalidCount)) * 100)}%`);
            
        } catch (error) {
            console.log(`❌ Error during validation: ${error}`);
        }
    }
    
    // Get detailed information about valid instances
    showValidInstances(): void {
        console.log("\n=== VALID INSTANCES DETAILS ===");
        
        this.validInstances.forEach((validation, index) => {
            console.log(`\n[${index}] EntityController ${validation.entityIndex}:`);
            console.log(`  State: ${validation.state}`);
            console.log(`  CanCollect: ${validation.canCollect}`);
            console.log(`  CanBuyThrough: ${validation.canBuyThrough}`);
            console.log(`  Reward: ${validation.rewardAmount} ${validation.rewardType}`);

            // Get additional details safely
            const timeLeftResult = this.safeInvoke(validation.instance, "GetJobTimeLeft");
            const healthResult = this.safeInvoke(validation.instance, "GetHealth");
            const explorationsResult = this.safeInvoke(validation.instance, "GetExplorations");

            if (!timeLeftResult.error) console.log(`  TimeLeft: ${timeLeftResult.value}`);
            if (!healthResult.error) console.log(`  Health: ${healthResult.value}`);
            if (!explorationsResult.error) console.log(`  Explorations: ${explorationsResult.value}`);
        });
    }
    
    // Get the best instance for collection (GEMS rewards only)
    getBestCollectibleInstance(): ValidatedInstance | null {
        if (this.collectibleInstances.length === 0) {
            console.log("❌ No collectible instances found");
            return null;
        }

        // Filter for GEMS rewards only
        const gemsInstances = this.collectibleInstances.filter(v => v.rewardType === "GEMS");
        if (gemsInstances.length === 0) {
            console.log("❌ No collectible instances with GEMS rewards found");
            console.log(`Available rewards: ${this.collectibleInstances.map(v => `${v.rewardAmount} ${v.rewardType}`).join(", ")}`);
            return null;
        }

        console.log(`💎 Found ${gemsInstances.length} collectible instances with GEMS rewards`);

        // Prioritize IDLE_READY instances with GEMS
        const idleReadyGems = gemsInstances.filter(v => v.state === "IDLE_READY");
        if (idleReadyGems.length > 0) {
            console.log(`✅ Using IDLE_READY GEMS instance: ${idleReadyGems[0].rewardAmount} GEMS`);
            return idleReadyGems[0];
        }

        // Fallback to any GEMS collectible instance
        console.log(`✅ Using first GEMS collectible instance (${gemsInstances[0].state}): ${gemsInstances[0].rewardAmount} GEMS`);
        return gemsInstances[0];
    }
    
    // Check if DoJobBuyThrough is available and should be attempted
    private shouldAttemptInstantCompletion(validation: ValidatedInstance): boolean {
        // If we've disabled instant completion due to failures, don't attempt
        if (!this.attemptInstantCompletion) {
            return false;
        }

        // If we've hit too many failures, disable instant completion
        if (this.buyThroughFailureCount >= this.buyThroughFailureThreshold) {
            console.log(`⚠️ Disabling instant completion after ${this.buyThroughFailureCount} consecutive failures`);
            this.attemptInstantCompletion = false;
            return false;
        }

        // Check if the instance supports DoJobBuyThrough
        if (!validation.canBuyThrough) {
            return false;
        }

        return true;
    }

    // Execute DoJobBuyThrough with proper error handling and tracking
    private executeInstantCompletion(validation: ValidatedInstance): boolean {
        const buyThroughResult = this.safeInvoke(validation.instance, "DoJobBuyThrough");

        if (buyThroughResult.error) {
            this.buyThroughFailureCount++;

            // Check for specific error types
            if (buyThroughResult.error.includes("abort was called")) {
                console.log(`    ⚠️ Instant completion blocked (insufficient currency or game restriction)`);
            } else {
                console.log(`    ⚠️ Instant completion failed: ${buyThroughResult.error}`);
            }
            return false;
        } else {
            this.buyThroughSuccessCount++;
            this.buyThroughFailureCount = 0; // Reset failure count on success
            console.log(`    💎 Instant completion successful!`);
            return true;
        }
    }

    // Simplified DoJobBuyThrough execution without monitoring
    testDoJobBuyThrough(validation: ValidatedInstance): boolean {
        console.log(`\n💎 Executing DoJobBuyThrough on EntityController ${validation.entityIndex}...`);

        const buyThroughResult = this.safeInvoke(validation.instance, "DoJobBuyThrough");
        if (buyThroughResult.error) {
            console.log(`⚠️ DoJobBuyThrough not available: ${buyThroughResult.error}`);
            return false;
        } else {
            console.log(`✅ DoJobBuyThrough executed successfully - instant completion!`);
            return true;
        }
    }

    // Simplified StartCollect execution without aggressive monitoring
    executeStartCollect(validation: ValidatedInstance): boolean {
        console.log(`\n🚀 Executing StartCollect on EntityController ${validation.entityIndex}...`);

        // Double-check the instance is still valid and collectible
        const canCollectResult = this.safeInvoke(validation.instance, "CanCollect");
        if (canCollectResult.error) {
            console.log(`❌ Instance became invalid: ${canCollectResult.error}`);
            return false;
        }

        if (!canCollectResult.value) {
            console.log(`❌ Instance no longer collectible`);
            return false;
        }

        // Execute StartCollect
        const startCollectResult = this.safeInvoke(validation.instance, "StartCollect");
        if (startCollectResult.error) {
            console.log(`❌ StartCollect failed: ${startCollectResult.error}`);
            return false;
        }

        console.log(`✅ StartCollect executed successfully!`);

        // Try DoJobBuyThrough for instant completion
        setTimeout(() => {
            const buyThroughResult = this.safeInvoke(validation.instance, "DoJobBuyThrough");
            if (buyThroughResult.error) {
                console.log(`⚠️ DoJobBuyThrough not available (this is normal if collection is already complete)`);
            } else {
                console.log(`💎 DoJobBuyThrough executed successfully - instant completion!`);
            }

            console.log(`🎉 Collection process completed for ${validation.rewardAmount} GEMS`);
        }, 500);

        return true;
    }

    // Ruin selling methods for clearing completed instances

    // Check if an instance has sellable ruins or debris
    private checkForSellableRuins(validation: ValidatedInstance): { hasRuins: boolean, ruinType?: string, sellMethod?: string } {
        try {
            // Try to get ruin-related methods from the GoodyHutHelper instance
            const hasRuinsResult = this.safeInvoke(validation.instance, "HasRuins");
            const hasDebrisResult = this.safeInvoke(validation.instance, "HasDebris");
            const canSellResult = this.safeInvoke(validation.instance, "CanSell");
            const canClearResult = this.safeInvoke(validation.instance, "CanClear");

            // Check for various ruin/debris indicators
            if (!hasRuinsResult.error && hasRuinsResult.value === true) {
                return { hasRuins: true, ruinType: "ruins", sellMethod: "SellRuins" };
            }

            if (!hasDebrisResult.error && hasDebrisResult.value === true) {
                return { hasRuins: true, ruinType: "debris", sellMethod: "ClearDebris" };
            }

            if (!canSellResult.error && canSellResult.value === true) {
                return { hasRuins: true, ruinType: "sellable", sellMethod: "DoSell" };
            }

            if (!canClearResult.error && canClearResult.value === true) {
                return { hasRuins: true, ruinType: "clearable", sellMethod: "DoClear" };
            }

            return { hasRuins: false };

        } catch (error) {
            console.log(`    ⚠️ Error checking for ruins: ${error}`);
            return { hasRuins: false };
        }
    }

    // Execute ruin selling/clearing operation
    private executeRuinSelling(validation: ValidatedInstance, ruinInfo: { ruinType: string, sellMethod: string }): boolean {
        let traceId = '';

        // Start tracing if tracer is available
        if (this.ruinSellingTracer && this.ruinSellingTracer.isTracing()) {
            const parameters = [
                { type: 'ValidatedInstance', value: validation, name: 'validation' },
                { type: 'RuinInfo', value: ruinInfo, name: 'ruinInfo' }
            ];

            traceId = this.ruinSellingTracer.traceMethodCall(
                ruinInfo.sellMethod,
                validation.instance,
                parameters,
                `ruin-sell-${Date.now()}`,
                validation.entityIndex
            );
        }

        try {
            console.log(`    🗑️ Attempting to sell/clear ${ruinInfo.ruinType} using ${ruinInfo.sellMethod}...`);

            const sellResult = this.safeInvoke(validation.instance, ruinInfo.sellMethod);

            if (sellResult.error) {
                console.log(`    ❌ Ruin selling failed: ${sellResult.error}`);
                this.ruinSellFailureCount++;

                // Trace completion with error result
                if (traceId && this.ruinSellingTracer) {
                    this.ruinSellingTracer.traceMethodCompletion(traceId, {
                        type: 'boolean',
                        value: false,
                        name: 'failed',
                        error: sellResult.error
                    });
                }

                return false;
            }

            console.log(`    ✅ Successfully sold/cleared ${ruinInfo.ruinType}!`);
            this.ruinSellSuccessCount++;

            // Trace successful completion
            if (traceId && this.ruinSellingTracer) {
                this.ruinSellingTracer.traceMethodCompletion(traceId, {
                    type: 'boolean',
                    value: true,
                    name: 'success'
                });
            }

            return true;

        } catch (error) {
            console.log(`    ❌ Ruin selling error: ${error}`);
            this.ruinSellFailureCount++;

            // Trace error
            if (traceId && this.ruinSellingTracer) {
                this.ruinSellingTracer.traceMethodError(traceId, error as Error);
            }

            return false;
        }
    }

    // Process completed instances to sell ruins and clear them for future collection
    private async processCompletedInstances(validation: ValidatedInstance): Promise<boolean> {
        if (!this.attemptRuinSelling) {
            return false;
        }

        // Only process instances in COMPLETED_AWAITING state
        if (validation.state !== "COMPLETED_AWAITING") {
            return false;
        }

        console.log(`    🔍 Processing completed instance (EntityController ${validation.entityIndex}) for ruin selling...`);

        // Check if instance has sellable ruins
        const ruinInfo = this.checkForSellableRuins(validation);

        if (!ruinInfo.hasRuins) {
            console.log(`    ℹ️ No sellable ruins found for EntityController ${validation.entityIndex}`);
            return false;
        }

        console.log(`    💎 Found ${ruinInfo.ruinType} that can be sold/cleared`);

        // Execute the selling operation
        const sellSuccess = this.executeRuinSelling(validation, { ruinType: ruinInfo.ruinType!, sellMethod: ruinInfo.sellMethod! });

        if (sellSuccess) {
            // Wait a moment for the operation to complete
            await new Promise(resolve => setTimeout(resolve, 500));

            // Re-validate the instance to check if it's now available for collection
            const updatedValidation = this.validateInstance(validation.instance, validation.entityIndex);

            if (updatedValidation.isValid && updatedValidation.state === "IDLE_READY") {
                console.log(`    🎉 Instance cleared and ready for future collection!`);
                return true;
            } else {
                console.log(`    ⚠️ Instance state after selling: ${updatedValidation.state}`);
                return false;
            }
        }

        return false;
    }

    // Batch processing methods

    // Validate a batch of instances and return only GEMS collectible ones
    private async validateBatch(entityInstances: any[], startIndex: number, batchSize: number): Promise<ValidatedInstance[]> {
        const batchEnd = Math.min(startIndex + batchSize, entityInstances.length);
        const validGemsInstances: ValidatedInstance[] = [];

        console.log(`🔍 Validating batch instances ${startIndex + 1}-${batchEnd}...`);

        for (let i = startIndex; i < batchEnd; i++) {
            try {
                const entity = entityInstances[i];
                const goodyHut = entity.field("m_goodyHut");

                if (goodyHut && goodyHut.value && goodyHut.value !== null && goodyHut.value.toString() !== "0x0") {
                    const goodyHutInstance = goodyHut.value as Il2Cpp.Object;
                    const validation = this.validateInstance(goodyHutInstance, i);

                    // Only include valid, collectible instances with GEMS rewards
                    if (validation.isValid && validation.canCollect && validation.rewardType === "GEMS") {
                        validGemsInstances.push(validation);
                    }
                }
            } catch (error) {
                // Silently skip invalid instances
            }
        }

        return validGemsInstances;
    }

    // Validate a batch with state persistence (skip already processed instances) and process completed instances for ruin selling
    private async validateBatchWithState(entityInstances: any[], startIndex: number, batchSize: number, state: CollectionState): Promise<ValidatedInstance[]> {
        const batchEnd = Math.min(startIndex + batchSize, entityInstances.length);
        const validGemsInstances: ValidatedInstance[] = [];
        let skippedCount = 0;
        let ruinProcessedCount = 0;

        console.log(`🔍 Validating batch instances ${startIndex + 1}-${batchEnd}...`);

        for (let i = startIndex; i < batchEnd; i++) {
            // Skip if already processed
            if (state.processedEntityIndices.has(i)) {
                skippedCount++;
                continue;
            }

            try {
                const entity = entityInstances[i];
                const goodyHut = entity.field("m_goodyHut");

                if (goodyHut && goodyHut.value && goodyHut.value !== null && goodyHut.value.toString() !== "0x0") {
                    const goodyHutInstance = goodyHut.value as Il2Cpp.Object;
                    const validation = this.validateInstance(goodyHutInstance, i);

                    if (validation.isValid) {
                        // Check if this is a collectible GEMS instance
                        if (validation.canCollect && validation.rewardType === "GEMS") {
                            validGemsInstances.push(validation);
                        }
                        // Check if this is a completed instance that might have sellable ruins
                        else if (validation.state === "COMPLETED_AWAITING" && validation.rewardType === "GEMS") {
                            console.log(`  🗑️ Found completed GEMS instance (EntityController ${i}) - checking for ruins...`);

                            const ruinProcessed = await this.processCompletedInstances(validation);
                            if (ruinProcessed) {
                                ruinProcessedCount++;
                                console.log(`  ✅ Ruins cleared for EntityController ${i} - may be available for future collection`);
                            }
                        }
                    }
                }
            } catch (error) {
                // Silently skip invalid instances
            }
        }

        if (skippedCount > 0) {
            console.log(`⏭️ Skipped ${skippedCount} already processed instances`);
        }

        if (ruinProcessedCount > 0) {
            console.log(`🗑️ Processed ruins for ${ruinProcessedCount} completed instances`);
        }

        return validGemsInstances;
    }

    // Collect all instances in a batch with improved success detection and instant completion handling
    private async collectBatch(batchInstances: ValidatedInstance[], batchNumber: number): Promise<{ collectionsStarted: number, instantCompletions: number }> {
        if (batchInstances.length === 0) {
            console.log(`📦 Batch ${batchNumber}: No GEMS instances to collect`);
            return { collectionsStarted: 0, instantCompletions: 0 };
        }

        console.log(`🚀 Batch ${batchNumber}: Starting collection of ${batchInstances.length} GEMS instances...`);

        let collectionsStarted = 0;
        let instantCompletions = 0;

        // Start collection on all instances in the batch
        const collectionPromises = batchInstances.map(async (validation, index) => {
            try {
                console.log(`  💎 [${index + 1}/${batchInstances.length}] Collecting ${validation.rewardAmount} GEMS from EntityController ${validation.entityIndex}`);

                // Execute StartCollect - this is the primary success metric
                const startCollectResult = this.safeInvoke(validation.instance, "StartCollect");
                if (startCollectResult.error) {
                    console.log(`    ❌ StartCollect failed: ${startCollectResult.error}`);
                    return { started: false, instantCompleted: false };
                }

                console.log(`    ✅ Collection started successfully`);
                collectionsStarted++;

                let instantCompleted = false;

                // With GetCollectTime hook, collections should complete instantly
                if (getCollectTimeHookInstalled) {
                    console.log(`    ⚡ Collection completing instantly via GetCollectTime hook`);
                    instantCompleted = true;
                    instantCompletions++; // Count hook-based instant completion
                } else {
                    // Fallback to DoJobBuyThrough if hook is not available
                    await new Promise(resolve => setTimeout(resolve, 100));

                    if (this.shouldAttemptInstantCompletion(validation)) {
                        instantCompleted = this.executeInstantCompletion(validation);
                        if (instantCompleted) {
                            instantCompletions++;
                        }
                    } else {
                        console.log(`    ⏳ Instant completion skipped (will complete naturally)`);
                    }
                }

                console.log(`    🎉 Collection initiated for ${validation.rewardAmount} GEMS`);
                return { started: true, instantCompleted };

            } catch (error) {
                console.log(`    ❌ Collection error: ${error}`);
                return { started: false, instantCompleted: false };
            }
        });

        // Wait for all collections in this batch to complete
        const results = await Promise.all(collectionPromises);
        const finalCollectionsStarted = results.filter(r => r.started).length;
        const finalInstantCompletions = results.filter(r => r.instantCompleted).length;

        console.log(`📦 Batch ${batchNumber} completed: ${finalCollectionsStarted}/${batchInstances.length} collections started, ${finalInstantCompletions}/${batchInstances.length} instant completions`);

        return { collectionsStarted: finalCollectionsStarted, instantCompletions: finalInstantCompletions };
    }

    // Main batch processing method with state persistence
    async processBatchCollection(forceReset: boolean = false): Promise<void> {
        console.log("🔄 Starting batch collection process...");

        // Show startup progress status
        if (!forceReset) {
            const progressStatus = this.getProgressStatus();
            if (progressStatus.hasProgress) {
                console.log(`📊 ${progressStatus.summary}`);
            }
        }

        // Load or create state
        let state = forceReset ? null : this.loadState();
        const isResuming = state !== null;

        if (!state) {
            state = this.createFreshState();
            if (forceReset) {
                console.log("🔄 Force reset: Starting fresh collection session");
            } else {
                console.log("🆕 Starting fresh collection session");
            }
        } else {
            const sessionAge = Math.round((Date.now() - state.sessionStats.startTime) / 1000 / 60); // minutes
            console.log(`📂 Resuming from saved progress: batch ${state.currentBatchIndex + 1}/${state.totalBatches} (saved ${sessionAge}m ago)`);
            console.log(`⏭️ Already processed: ${state.processedEntityIndices.size} instances in ${state.completedBatches.length} completed batches`);

            // Restore counters from saved state
            this.buyThroughFailureCount = state.buyThroughStats.failureCount;
            this.buyThroughSuccessCount = state.buyThroughStats.successCount;
            this.attemptInstantCompletion = state.buyThroughStats.enabled;

            // Restore ruin selling stats if available
            if (state.ruinSellStats) {
                this.ruinSellFailureCount = state.ruinSellStats.failureCount;
                this.ruinSellSuccessCount = state.ruinSellStats.successCount;
                this.attemptRuinSelling = state.ruinSellStats.enabled;
            }
        }

        // Show hook status
        if (getCollectTimeHookInstalled) {
            console.log("⚡ GetCollectTime hook is active - collections will complete instantly");
        } else {
            console.log("⚠️ GetCollectTime hook not available - falling back to DoJobBuyThrough method");
        }

        try {
            const EntityController = Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController");
            const entityInstances = Il2Cpp.gc.choose(EntityController);

            console.log(`📊 Found ${entityInstances.length} EntityController instances`);

            const totalBatches = Math.ceil(entityInstances.length / this.batchSize);
            state.totalBatches = totalBatches;

            if (isResuming) {
                const remaining = totalBatches - state.completedBatches.length;
                console.log(`📦 Resuming: ${remaining} batches remaining of ${totalBatches} total`);
                console.log(`⏭️ Skipping ${state.processedEntityIndices.size} already processed instances`);
            } else {
                console.log(`📦 Processing in ${totalBatches} batches of ${this.batchSize} instances each\n`);
            }

            let totalCollectionsStarted = state.sessionStats.totalCollectionsStarted;
            let totalInstantCompletions = state.sessionStats.totalInstantCompletions;

            for (let batchIndex = state.currentBatchIndex; batchIndex < totalBatches; batchIndex++) {
                const startIndex = batchIndex * this.batchSize;
                const batchNumber = batchIndex + 1;

                // Skip if batch already completed
                if (state.completedBatches.includes(batchNumber)) {
                    console.log(`⏭️ Skipping already completed batch ${batchNumber}/${totalBatches}`);
                    continue;
                }

                console.log(`\n=== BATCH ${batchNumber}/${totalBatches} ===`);

                // Phase 1: Validate batch (skip already processed instances)
                const validGemsInstances = await this.validateBatchWithState(entityInstances, startIndex, this.batchSize, state);
                console.log(`✅ Found ${validGemsInstances.length} valid GEMS instances in batch ${batchNumber}`);

                // Phase 2: Collect batch
                if (validGemsInstances.length > 0) {
                    const batchResults = await this.collectBatch(validGemsInstances, batchNumber);
                    totalCollectionsStarted += batchResults.collectionsStarted;
                    totalInstantCompletions += batchResults.instantCompletions;

                    // Update state with processed instances
                    validGemsInstances.forEach(instance => {
                        state.processedEntityIndices.add(instance.entityIndex);
                    });
                }

                // Mark batch as completed and update state
                state.completedBatches.push(batchNumber);
                state.currentBatchIndex = batchIndex;
                state.sessionStats.totalCollectionsStarted = totalCollectionsStarted;
                state.sessionStats.totalInstantCompletions = totalInstantCompletions;
                state.sessionStats.totalRuinsProcessed = this.ruinSellSuccessCount;
                state.buyThroughStats.successCount = this.buyThroughSuccessCount;
                state.buyThroughStats.failureCount = this.buyThroughFailureCount;
                state.buyThroughStats.enabled = this.attemptInstantCompletion;
                state.ruinSellStats.successCount = this.ruinSellSuccessCount;
                state.ruinSellStats.failureCount = this.ruinSellFailureCount;
                state.ruinSellStats.enabled = this.attemptRuinSelling;
                state.hookStats.callCount = getCollectTimeHookCallCount;
                state.cleanupHookStats.callCount = cleanupHookCallCount;
                state.cleanupHookStats.autoEnabledCount = cleanupAutoEnabledCount;

                // Save progress
                this.saveState(state);

                // Show current instant completion status
                if (this.buyThroughFailureCount > 0) {
                    console.log(`⚠️ Instant completion status: ${this.buyThroughSuccessCount} successes, ${this.buyThroughFailureCount} failures`);
                }

                // Wait before processing next batch (except for the last batch)
                if (batchIndex < totalBatches - 1) {
                    console.log(`⏳ Waiting ${this.batchDelay / 1000}s before next batch...`);
                    await new Promise(resolve => setTimeout(resolve, this.batchDelay));
                }
            }

            console.log(`\n🎉 Batch collection completed!`);
            console.log(`📊 Collections started: ${totalCollectionsStarted}`);
            console.log(`💎 Instant completions: ${totalInstantCompletions}`);
            console.log(`🗑️ Ruins processed: ${this.ruinSellSuccessCount}`);

            // Show completion method used
            if (getCollectTimeHookInstalled) {
                console.log(`⚡ Completion method: GetCollectTime hook (${getCollectTimeHookCallCount} hook calls)`);
            } else if (totalInstantCompletions > 0) {
                console.log(`💎 Completion method: DoJobBuyThrough`);
            } else if (totalCollectionsStarted > 0) {
                console.log(`⏳ Completion method: Natural completion over time`);
            }

            if (totalInstantCompletions === 0 && totalCollectionsStarted > 0 && !getCollectTimeHookInstalled) {
                console.log(`ℹ️ Note: Collections were started but instant completion was not available`);
                console.log(`ℹ️ Collections will complete naturally over time`);
            }

            // Final instant completion statistics (only for DoJobBuyThrough)
            if (!getCollectTimeHookInstalled && (this.buyThroughSuccessCount > 0 || this.buyThroughFailureCount > 0)) {
                const successRate = Math.round((this.buyThroughSuccessCount / (this.buyThroughSuccessCount + this.buyThroughFailureCount)) * 100);
                console.log(`📈 DoJobBuyThrough success rate: ${successRate}% (${this.buyThroughSuccessCount}/${this.buyThroughSuccessCount + this.buyThroughFailureCount})`);
            }

            // Clear completed state
            this.clearState();
            console.log("✨ Session completed - progress cleared");

        } catch (error) {
            console.log(`❌ Batch collection error: ${error}`);
        }
    }

    // Configuration methods
    setInstantCompletionEnabled(enabled: boolean): void {
        this.attemptInstantCompletion = enabled;
        console.log(`💎 Instant completion ${enabled ? 'enabled' : 'disabled'}`);
    }

    getInstantCompletionStats(): { enabled: boolean, successes: number, failures: number } {
        return {
            enabled: this.attemptInstantCompletion,
            successes: this.buyThroughSuccessCount,
            failures: this.buyThroughFailureCount
        };
    }

    // Ruin selling configuration methods
    setRuinSellingEnabled(enabled: boolean): void {
        this.attemptRuinSelling = enabled;
        console.log(`🗑️ Ruin selling ${enabled ? 'enabled' : 'disabled'}`);
    }

    getRuinSellingStats(): { enabled: boolean, successes: number, failures: number } {
        return {
            enabled: this.attemptRuinSelling,
            successes: this.ruinSellSuccessCount,
            failures: this.ruinSellFailureCount
        };
    }

    // Hook status methods
    getHookStatus(): { installed: boolean, callCount: number } {
        return {
            installed: getCollectTimeHookInstalled,
            callCount: getCollectTimeHookCallCount
        };
    }

    getCleanupHookStatus(): { installed: boolean, callCount: number, autoEnabledCount: number } {
        return {
            installed: cleanupHookInstalled,
            callCount: cleanupHookCallCount,
            autoEnabledCount: cleanupAutoEnabledCount
        };
    }

    reinstallHook(): void {
        console.log("🔄 Attempting to reinstall GetCollectTime hook...");
        installGetCollectTimeHook();
    }

    reinstallCleanupHook(): void {
        console.log("🔄 Attempting to reinstall GoodyHutHelperConfig cleanup hook...");
        installCleanupHook();
    }

    // State persistence methods with enhanced file-based storage
    private saveState(state: CollectionState): void {
        // Initialize storage paths if not done yet
        if (this.alternativeStatePaths.length === 0) {
            this.initializeStoragePaths();
        }

        const stateData = {
            ...state,
            version: this.stateVersion,
            processedEntityIndices: Array.from(state.processedEntityIndices)
        };

        const stateJson = JSON.stringify(stateData, null, 2);

        // Try to find a writable location and save the state
        let saveSuccess = false;
        let lastError: any = null;

        try {
            const workingPath = this.findWritableStoragePath();
            const fs = require('fs');

            // Attempt to save to the working path
            fs.writeFileSync(workingPath, stateJson, 'utf8');

            // Verify the file was written correctly
            const verifyData = fs.readFileSync(workingPath, 'utf8');
            const parsed = JSON.parse(verifyData);

            if (parsed.version === this.stateVersion) {
                console.log(`💾 State saved to ${workingPath}: ${state.processedEntityIndices.size} processed instances, batch ${state.currentBatchIndex + 1}/${state.totalBatches}`);
                saveSuccess = true;
            } else {
                throw new Error("State verification failed - data corruption detected");
            }

        } catch (error: any) {
            lastError = error;
            console.log(`❌ Failed to save state to file: ${error.message || error}`);

            // Provide specific guidance based on error type
            if (error.message && error.message.includes('EACCES')) {
                console.log(`🔧 Permission Error: The file system is read-only or lacks write permissions`);
                console.log(`💡 Try running the script with elevated permissions or check folder permissions`);
            } else if (error.message && error.message.includes('ENOSPC')) {
                console.log(`💽 Disk Space Error: Insufficient disk space to save state file`);
            } else if (error.message && error.message.includes('ENOENT')) {
                console.log(`📁 Path Error: Directory does not exist or is inaccessible`);
            }
        }

        // Fallback to memory storage if file save failed
        if (!saveSuccess) {
            try {
                (globalThis as any).persistentState = stateJson;
                console.log(`💾 Fallback: State saved to memory (will be lost on restart)`);
                console.log(`⚠️ File persistence is not working - progress will not survive script restarts`);
                console.log(`🔧 To fix: Ensure write permissions to workspace directory or run with elevated privileges`);
            } catch (fallbackError) {
                console.log(`❌ Critical: Both file and memory storage failed!`);
                console.log(`❌ File error: ${lastError}`);
                console.log(`❌ Memory error: ${fallbackError}`);
            }
        }
    }

    private loadState(): CollectionState | null {
        try {
            let stateJson: string | null = null;
            let stateData: any = null;

            // If memory-only mode is enabled, load from memory first
            if (this.memoryOnlyMode) {
                if (this.memoryStorage && Object.keys(this.memoryStorage).length > 0) {
                    stateData = this.memoryStorage;
                    console.log(`📂 State loaded from memory storage`);
                } else {
                    console.log(`📂 No state found in memory storage`);
                    return null;
                }
            } else {
                // Initialize storage paths if not done yet
                if (this.alternativeStatePaths.length === 0) {
                    this.initializeStoragePaths();
                }

                // Try to load from file locations in order of preference
                let fs: any = null;
                let fsAvailable = false;

                try {
                    fs = require('fs');
                    fsAvailable = (fs && typeof fs.readFileSync === 'function' && typeof fs.existsSync === 'function');
                } catch (fsError) {
                    console.log(`⚠️ fs module not available, checking memory fallback`);
                    fsAvailable = false;
                }

                if (fsAvailable) {
                    for (const testPath of this.alternativeStatePaths) {
                        try {
                            if (fs.existsSync(testPath)) {
                                stateJson = fs.readFileSync(testPath, 'utf8');
                                console.log(`📂 State loaded from: ${testPath}`);
                                break;
                            }
                        } catch (fileError) {
                            // Try next path
                            continue;
                        }
                    }
                }

                // If no file found or fs not available, try memory fallback
                if (!stateJson) {
                    // Try memory storage first
                    if (this.memoryStorage && Object.keys(this.memoryStorage).length > 0) {
                        stateData = this.memoryStorage;
                        console.log(`📂 State loaded from memory storage (file system unavailable)`);
                    } else {
                        // Try global memory fallback
                        stateJson = (globalThis as any).persistentState;
                        if (stateJson) {
                            console.log(`📂 Loading state from global memory fallback (no file found)`);
                        }
                    }
                }
            }

            // Parse state data
            let rawState: any = null;

            if (stateData) {
                // Already parsed from memory
                rawState = stateData;
            } else if (stateJson) {
                // Parse from JSON string
                rawState = JSON.parse(stateJson);
            } else {
                return null;
            }

            // Validate state version
            if (rawState.version && rawState.version !== this.stateVersion) {
                console.log(`⚠️ State version mismatch (saved: ${rawState.version}, current: ${this.stateVersion}), starting fresh`);
                this.clearState();
                return null;
            }

            // Check if state has expired
            const now = Date.now();
            const stateAge = (now - rawState.timestamp) / (1000 * 60 * 60); // hours
            if (stateAge > this.stateExpirationHours) {
                console.log(`⏰ Saved state expired (${Math.round(stateAge)}h old), starting fresh`);
                this.clearState();
                return null;
            }

            // Validate required fields
            if (!rawState.processedEntityIndices || !Array.isArray(rawState.processedEntityIndices)) {
                console.log(`❌ Invalid state format, starting fresh`);
                this.clearState();
                return null;
            }

            // Reconstruct Set from array
            const state: CollectionState = {
                ...rawState,
                processedEntityIndices: new Set(rawState.processedEntityIndices)
            };

            // Validate state consistency
            if (!this.validateStateConsistency(state)) {
                console.log(`❌ State consistency check failed, starting fresh`);
                this.clearState();
                return null;
            }

            console.log(`📂 State loaded: ${state.processedEntityIndices.size} processed instances, batch ${state.currentBatchIndex + 1}/${state.totalBatches}`);
            return state;

        } catch (error) {
            console.log(`❌ Failed to load state: ${error}`);
            console.log(`🔄 Starting with fresh state`);
            this.clearState();
            return null;
        }
    }

    private clearState(): void {
        try {
            let clearedFiles = 0;

            // Clear file-based state from all possible locations
            const fs = require('fs');

            for (const testPath of this.alternativeStatePaths) {
                try {
                    if (fs.existsSync(testPath)) {
                        fs.unlinkSync(testPath);
                        clearedFiles++;
                        console.log(`🗑️ Cleared state file: ${testPath}`);
                    }
                } catch (fileError: any) {
                    // File might not exist or be inaccessible, continue with others
                    console.log(`⚠️ Could not clear ${testPath}: ${fileError.message || fileError}`);
                }
            }

            // Clear memory fallback
            delete (globalThis as any).persistentState;

            // Reset working path cache
            this.currentWorkingPath = "";

            if (clearedFiles > 0) {
                console.log(`🗑️ Saved state cleared (${clearedFiles} files removed)`);
            } else {
                console.log("🗑️ Saved state cleared (no files found)");
            }
        } catch (error) {
            console.log(`❌ Failed to clear state: ${error}`);
        }
    }

    private createFreshState(): CollectionState {
        return {
            version: this.stateVersion,
            timestamp: Date.now(),
            processedEntityIndices: new Set<number>(),
            completedBatches: [],
            totalBatches: 0,
            currentBatchIndex: 0,
            hookStats: {
                installed: getCollectTimeHookInstalled,
                callCount: getCollectTimeHookCallCount
            },
            cleanupHookStats: {
                installed: cleanupHookInstalled,
                callCount: cleanupHookCallCount,
                autoEnabledCount: cleanupAutoEnabledCount
            },
            buyThroughStats: {
                successCount: this.buyThroughSuccessCount,
                failureCount: this.buyThroughFailureCount,
                enabled: this.attemptInstantCompletion
            },
            ruinSellStats: {
                successCount: this.ruinSellSuccessCount,
                failureCount: this.ruinSellFailureCount,
                enabled: this.attemptRuinSelling
            },
            sessionStats: {
                totalCollectionsStarted: 0,
                totalInstantCompletions: 0,
                totalRuinsProcessed: 0,
                startTime: Date.now()
            }
        };
    }

    // Validate state consistency and handle edge cases
    private validateStateConsistency(state: CollectionState): boolean {
        try {
            // Check basic structure
            if (!state.processedEntityIndices || !state.completedBatches || !state.sessionStats) {
                console.log("❌ State validation failed: Missing required fields");
                return false;
            }

            // Check for reasonable values
            if (state.currentBatchIndex < 0 || state.totalBatches < 0) {
                console.log("❌ State validation failed: Invalid batch indices");
                return false;
            }

            if (state.completedBatches.length > state.totalBatches) {
                console.log("❌ State validation failed: More completed batches than total");
                return false;
            }

            // Check timestamp is reasonable (not in future, not too old)
            const now = Date.now();
            if (state.timestamp > now || state.sessionStats.startTime > now) {
                console.log("❌ State validation failed: Future timestamps detected");
                return false;
            }

            // Validate completed batches are within reasonable range
            for (const batchNum of state.completedBatches) {
                if (batchNum < 1 || batchNum > state.totalBatches) {
                    console.log(`❌ State validation failed: Invalid batch number ${batchNum}`);
                    return false;
                }
            }

            return true;
        } catch (error) {
            console.log(`❌ State validation error: ${error}`);
            return false;
        }
    }

    // Public state management methods
    getProgressStatus(): { hasProgress: boolean, summary: string, details?: any } {
        const state = this.loadState();
        if (!state) {
            return { hasProgress: false, summary: "No saved progress found" };
        }

        const processed = state.processedEntityIndices.size;
        const completedBatches = state.completedBatches.length;
        const totalBatches = state.totalBatches;
        const sessionTime = Math.round((Date.now() - state.sessionStats.startTime) / 1000 / 60); // minutes
        const remaining = totalBatches - completedBatches;

        const summary = `Progress: ${processed} instances processed, ${completedBatches}/${totalBatches} batches completed (${remaining} remaining, saved ${sessionTime}m ago)`;

        return {
            hasProgress: true,
            summary,
            details: {
                processedInstances: processed,
                completedBatches,
                totalBatches,
                remainingBatches: remaining,
                sessionAgeMinutes: sessionTime,
                collectionsStarted: state.sessionStats.totalCollectionsStarted,
                instantCompletions: state.sessionStats.totalInstantCompletions
            }
        };
    }

    resetProgress(): void {
        this.clearState();
        console.log("🔄 All progress reset - next collection will start fresh");
    }

    showProgressDetails(): void {
        const state = this.loadState();
        if (!state) {
            console.log("📊 No saved progress found");
            console.log("💡 Use goodyManager.batchCollection() to start a new collection session");
            return;
        }

        const sessionAge = Math.round((Date.now() - state.sessionStats.startTime) / 1000 / 60);
        const remaining = state.totalBatches - state.completedBatches.length;
        const progressPercent = Math.round((state.completedBatches.length / state.totalBatches) * 100);

        console.log("=== SAVED PROGRESS DETAILS ===");
        console.log(`📅 Session started: ${new Date(state.sessionStats.startTime).toLocaleString()} (${sessionAge}m ago)`);
        console.log(`📦 Batches: ${state.completedBatches.length}/${state.totalBatches} completed (${progressPercent}% done)`);
        console.log(`⏳ Remaining: ${remaining} batches to process`);
        console.log(`🎯 Instances: ${state.processedEntityIndices.size} processed`);
        console.log(`📊 Collections: ${state.sessionStats.totalCollectionsStarted} started, ${state.sessionStats.totalInstantCompletions} instant`);
        console.log(`🗑️ Ruins processed: ${state.sessionStats.totalRuinsProcessed || 0}`);
        console.log(`⚡ Hook: ${state.hookStats.installed ? 'Active' : 'Inactive'} (${state.hookStats.callCount} calls)`);
        console.log(`💎 DoJobBuyThrough: ${state.buyThroughStats.successCount} success, ${state.buyThroughStats.failureCount} failures`);

        if (state.ruinSellStats) {
            console.log(`🗑️ Ruin Selling: ${state.ruinSellStats.successCount} success, ${state.ruinSellStats.failureCount} failures`);
        }

        if (state.cleanupHookStats) {
            console.log(`🧹 Cleanup Hook: ${state.cleanupHookStats.installed ? 'Active' : 'Inactive'} (${state.cleanupHookStats.callCount} calls, ${state.cleanupHookStats.autoEnabledCount} auto-enabled)`);
        }

        if (state.completedBatches.length > 0) {
            console.log(`✅ Completed batches: ${state.completedBatches.join(', ')}`);
        }

        console.log("\n💡 Use goodyManager.resume() to continue from where you left off");
        console.log("💡 Use goodyManager.reset() to clear progress and start fresh");
    }

    // Enhanced resume functionality with better user feedback
    async resumeCollection(): Promise<void> {
        const progressStatus = this.getProgressStatus();

        if (!progressStatus.hasProgress) {
            console.log("📊 No saved progress found - starting fresh collection");
            return this.processBatchCollection(false);
        }

        console.log("🔄 Resuming collection from saved progress...");
        console.log(`📊 ${progressStatus.summary}`);

        const details = progressStatus.details;
        if (details && details.remainingBatches > 0) {
            console.log(`⏳ Estimated remaining work: ${details.remainingBatches} batches`);
        }

        return this.processBatchCollection(false);
    }

    // Enhanced reset functionality with confirmation
    async resetAndStartFresh(): Promise<void> {
        const progressStatus = this.getProgressStatus();

        if (progressStatus.hasProgress) {
            console.log("⚠️ Clearing existing progress and starting fresh...");
            console.log(`📊 ${progressStatus.summary}`);
            console.log("🗑️ This progress will be lost");
        }

        return this.processBatchCollection(true);
    }

    // Public tracer control methods
    startRuinSellingTracer(): void {
        if (this.ruinSellingTracer) {
            this.ruinSellingTracer.startTracing();
        }
    }

    stopRuinSellingTracer(): void {
        if (this.ruinSellingTracer) {
            this.ruinSellingTracer.stopTracing();
        }
    }

    getRuinSellingTraceHistory(): any[] {
        return this.ruinSellingTracer ? this.ruinSellingTracer.getTraceHistory() : [];
    }

    getRuinSellingTraceSummary(): any {
        return this.ruinSellingTracer ? this.ruinSellingTracer.getPerformanceSummary() : null;
    }

    getRuinSellingTraceLog(): string {
        return this.ruinSellingTracer ? this.ruinSellingTracer.generateStructuredLog() : "Tracer not available";
    }

    // Manual ruin selling command for on-demand cleanup
    async sellRuins(): Promise<{ processed: number, successful: number, failed: number }> {
        console.log("🗑️ Starting manual ruin selling operation...");
        console.log("🔍 Scanning for completed GoodyHut instances with sellable ruins...");

        // Get all current instances
        const entityInstances = this.getAllEntityInstances();
        if (!entityInstances || entityInstances.length === 0) {
            console.log("❌ No GoodyHut instances found");
            return { processed: 0, successful: 0, failed: 0 };
        }

        let processedCount = 0;
        let successfulCount = 0;
        let failedCount = 0;
        const completedInstances: ValidatedInstance[] = [];

        // Scan all instances for completed ones with GEMS rewards
        console.log(`🔍 Scanning ${entityInstances.length} total instances...`);

        for (let i = 0; i < entityInstances.length; i++) {
            try {
                const entity = entityInstances[i];
                const goodyHut = entity.field("m_goodyHut");

                if (goodyHut && goodyHut.value && goodyHut.value !== null && goodyHut.value.toString() !== "0x0") {
                    const goodyHutInstance = goodyHut.value as Il2Cpp.Object;
                    const validation = this.validateInstance(goodyHutInstance, i);

                    // Look for completed instances with GEMS rewards that might have sellable ruins
                    if (validation.isValid &&
                        validation.state === "COMPLETED_AWAITING" &&
                        validation.rewardType === "GEMS") {

                        completedInstances.push(validation);
                        console.log(`   Found completed GEMS instance: EntityController ${i} (${validation.rewardAmount} gems)`);
                    }
                }
            } catch (error) {
                // Silently skip invalid instances
                continue;
            }
        }

        if (completedInstances.length === 0) {
            console.log("ℹ️ No completed GEMS instances found that might have sellable ruins");
            return { processed: 0, successful: 0, failed: 0 };
        }

        console.log(`🎯 Found ${completedInstances.length} completed GEMS instances to process`);
        console.log("🗑️ Processing ruin selling operations...");

        // Process each completed instance for ruin selling
        for (const validation of completedInstances) {
            processedCount++;
            console.log(`   Processing ${processedCount}/${completedInstances.length}: EntityController ${validation.entityIndex}`);

            try {
                // Check if instance has sellable ruins
                const ruinInfo = this.checkForSellableRuins(validation);

                if (!ruinInfo.hasRuins) {
                    console.log(`     ℹ️ No sellable ruins found`);
                    continue;
                }

                console.log(`     💎 Found ${ruinInfo.ruinType} - attempting to sell/clear...`);

                // Execute the selling operation
                const sellSuccess = this.executeRuinSelling(validation, {
                    ruinType: ruinInfo.ruinType!,
                    sellMethod: ruinInfo.sellMethod!
                });

                if (sellSuccess) {
                    successfulCount++;
                    console.log(`     ✅ Successfully sold/cleared ${ruinInfo.ruinType}`);

                    // Wait a moment for the operation to complete
                    await new Promise(resolve => setTimeout(resolve, 500));

                    // Re-validate the instance to check if it's now available for collection
                    const updatedValidation = this.validateInstance(validation.instance, validation.entityIndex);
                    if (updatedValidation.isValid && updatedValidation.state === "IDLE_READY") {
                        console.log(`     🎉 Instance cleared and ready for future collection!`);
                    }
                } else {
                    failedCount++;
                    console.log(`     ❌ Failed to sell/clear ${ruinInfo.ruinType}`);
                }

            } catch (error: any) {
                failedCount++;
                console.log(`     ❌ Error processing instance: ${error.message || error}`);
            }

            // Small delay between instances to avoid overwhelming the system
            if (processedCount < completedInstances.length) {
                await new Promise(resolve => setTimeout(resolve, 200));
            }
        }

        // Update global statistics
        this.ruinSellSuccessCount += successfulCount;
        this.ruinSellFailureCount += failedCount;

        // Display summary results
        console.log("\n🗑️ Manual ruin selling operation completed!");
        console.log(`📊 Results: ${processedCount} processed, ${successfulCount} successful, ${failedCount} failed`);

        if (successfulCount > 0) {
            console.log(`✅ Successfully sold ruins for ${successfulCount} instances`);
            console.log(`💡 These instances may now be available for future collection cycles`);
        }

        if (failedCount > 0) {
            console.log(`⚠️ ${failedCount} instances could not be processed (no ruins or operation failed)`);
        }

        console.log(`📈 Total ruin selling stats: ${this.ruinSellSuccessCount} successful, ${this.ruinSellFailureCount} failed`);

        return {
            processed: processedCount,
            successful: successfulCount,
            failed: failedCount
        };
    }

    // Helper method to get all entity instances using the same approach as successful scan()
    private getAllEntityInstances(): any[] {
        try {
            console.log("🔍 Discovering EntityController instances using Il2Cpp.gc.choose...");

            const AssemblyCSharp = Il2Cpp.domain.assembly("Assembly-CSharp").image;
            const EntityControllerClass = AssemblyCSharp.class("EntityController");

            if (!EntityControllerClass) {
                console.log("❌ EntityController class not found");
                return [];
            }

            // Use the same method that works in scanAndValidateInstances()
            const entityInstances = Il2Cpp.gc.choose(EntityControllerClass);

            if (!entityInstances || entityInstances.length === 0) {
                console.log("❌ No EntityController instances found via Il2Cpp.gc.choose");
                return [];
            }

            console.log(`✅ Successfully discovered ${entityInstances.length} EntityController instances`);
            console.log("📊 Using same instance discovery method as successful batch processing");

            return entityInstances;

        } catch (error: any) {
            console.log(`❌ Critical error accessing EntityController instances: ${error.message || error}`);
            console.log("💡 Falling back to empty array - manual ruin selling will not be available");
            return [];
        }
    }
}

// Create global instance manager
const instanceManager = new GoodyHutInstanceManager();

// Make it available globally
(globalThis as any).goodyManager = {
    scan: () => instanceManager.scanAndValidateInstances(),
    showValid: () => instanceManager.showValidInstances(),
    getBest: () => instanceManager.getBestCollectibleInstance(),
    startCollection: () => {
        const best = instanceManager.getBestCollectibleInstance();
        if (best) {
            return instanceManager.executeStartCollect(best);
        } else {
            console.log("❌ No collectible instances available");
            return false;
        }
    },
    batchCollection: () => {
        console.log("🔄 Starting batch collection (will resume from saved progress if available)...");
        return instanceManager.processBatchCollection();
    },
    resume: () => {
        console.log("📂 Resuming collection from saved progress...");
        return instanceManager.resumeCollection();
    },
    reset: () => {
        console.log("🔄 Resetting progress and starting fresh...");
        return instanceManager.resetAndStartFresh();
    },
    enableInstantCompletion: () => instanceManager.setInstantCompletionEnabled(true),
    disableInstantCompletion: () => instanceManager.setInstantCompletionEnabled(false),
    getStats: () => instanceManager.getInstantCompletionStats(),
    enableRuinSelling: () => instanceManager.setRuinSellingEnabled(true),
    disableRuinSelling: () => instanceManager.setRuinSellingEnabled(false),
    getRuinStats: () => instanceManager.getRuinSellingStats(),
    sellRuins: () => {
        console.log("🗑️ Manually triggering ruin selling operation...");
        return instanceManager.sellRuins();
    },

    // Ruin selling tracer controls
    startTracing: () => {
        instanceManager.startRuinSellingTracer();
        console.log("🔍 Ruin selling tracer started");
    },
    stopTracing: () => {
        instanceManager.stopRuinSellingTracer();
        console.log("⏹️ Ruin selling tracer stopped");
    },
    getTraceHistory: () => {
        return instanceManager.getRuinSellingTraceHistory();
    },
    getTraceSummary: () => {
        const summary = instanceManager.getRuinSellingTraceSummary();
        if (summary) {
            console.log("📊 Ruin Selling Trace Summary:");
            console.log(`   Total Calls: ${summary.totalCalls}`);
            console.log(`   Successful: ${summary.successfulCalls}`);
            console.log(`   Failed: ${summary.failedCalls}`);
            console.log(`   Average Time: ${summary.averageExecutionTimeMs.toFixed(2)}ms`);
        } else {
            console.log("❌ Ruin selling tracer not available");
        }
        return summary;
    },
    showTraceLog: () => {
        const log = instanceManager.getRuinSellingTraceLog();
        console.log(log);
        return log;
    },
    getHookStatus: () => instanceManager.getHookStatus(),
    reinstallHook: () => instanceManager.reinstallHook(),
    getCleanupHookStatus: () => instanceManager.getCleanupHookStatus(),
    reinstallCleanupHook: () => instanceManager.reinstallCleanupHook(),
    getProgress: () => {
        const status = instanceManager.getProgressStatus();
        console.log(status.hasProgress ? `📊 ${status.summary}` : "📊 No saved progress found");
        return status;
    },
    showProgress: () => instanceManager.showProgressDetails(),
    resetProgress: () => instanceManager.resetProgress(),
    help: () => {
        console.log("=== GoodyHut Instance Manager Commands ===");
        console.log("goodyManager.scan() - Scan and validate all instances");
        console.log("goodyManager.showValid() - Show details of valid instances");
        console.log("goodyManager.getBest() - Get best collectible instance (GEMS only)");
        console.log("goodyManager.startCollection() - Start collection on best GEMS instance");
        console.log("goodyManager.batchCollection() - Process all GEMS instances in batches (auto-resume)");
        console.log("");
        console.log("=== Enhanced Progress Management ===");
        console.log("goodyManager.resume() - Resume from saved progress with detailed status");
        console.log("goodyManager.reset() - Clear ALL progress and start completely fresh");
        console.log("goodyManager.getProgress() - Check current progress status");
        console.log("goodyManager.showProgress() - Show detailed progress with recommendations");
        console.log("goodyManager.resetProgress() - Clear saved progress without starting collection");
        console.log("");
        console.log("=== Instant Completion Controls ===");
        console.log("goodyManager.enableInstantCompletion() - Enable DoJobBuyThrough attempts");
        console.log("goodyManager.disableInstantCompletion() - Disable DoJobBuyThrough attempts");
        console.log("goodyManager.getStats() - Show instant completion statistics");
        console.log("");
        console.log("=== Ruin Selling Controls ===");
        console.log("goodyManager.enableRuinSelling() - Enable automatic ruin selling for completed instances");
        console.log("goodyManager.disableRuinSelling() - Disable automatic ruin selling");
        console.log("goodyManager.getRuinStats() - Show ruin selling statistics");
        console.log("goodyManager.sellRuins() - Manually trigger ruin selling for completed instances");
        console.log("                              (Scans all instances, processes completed ones on-demand)");
        console.log("");
        console.log("=== Ruin Selling Tracer Controls ===");
        console.log("goodyManager.startTracing() - Start tracing ruin selling method calls");
        console.log("goodyManager.stopTracing() - Stop tracing operations");
        console.log("goodyManager.getTraceHistory() - Get detailed trace history");
        console.log("goodyManager.getTraceSummary() - Show performance summary");
        console.log("goodyManager.showTraceLog() - Display structured trace log");
        console.log("");
        console.log("=== Hook Management ===");
        console.log("goodyManager.getHookStatus() - Check GetCollectTime hook status");
        console.log("goodyManager.reinstallHook() - Reinstall GetCollectTime hook if needed");
        console.log("goodyManager.getCleanupHookStatus() - Check GoodyHutHelperConfig cleanup hook status");
        console.log("goodyManager.reinstallCleanupHook() - Reinstall cleanup hook if needed");
        console.log("");
        console.log("=== Key Features ===");
        console.log("💎 GEMS ONLY: Only instances with GEMS rewards will be collected");
        console.log("📦 BATCH MODE: Collections will start even if instant completion fails");
        console.log("⚡ HOOK MODE: GetCollectTime hook provides instant completion without premium currency");
        console.log("🧹 CLEANUP HOOK: Automatically enables cleanup flag for non-collectible instances");
        console.log("�️ RUIN SELLING: Automatically sells ruins from completed instances to clear them");
        console.log("�💾 PERSISTENT STATE: Progress saved to goodyHutState.json, survives script restarts");
        console.log("🔄 SMART RESUME: Automatically skips already processed instances");
        console.log("⏰ AUTO-EXPIRE: Saved progress expires after 24 hours");
        console.log("🛡️ ERROR RECOVERY: Handles corrupted state files gracefully");
        console.log("🎯 MANUAL CLEANUP: On-demand ruin selling with goodyManager.sellRuins() command");
        console.log("📱 CROSS-PLATFORM: Works on Windows, macOS, Linux, and Android/ARM64");
        console.log("🔧 ADAPTIVE STORAGE: Finds writable locations automatically across different platforms");
        console.log("");
        console.log("=== Quick Start ===");
        console.log("1. goodyManager.batchCollection() - Start/resume collection (includes automatic ruin selling)");
        console.log("2. goodyManager.showProgress() - Check current status and progress details");
        console.log("3. goodyManager.sellRuins() - Manually clean up completed instances (optional)");
        console.log("4. goodyManager.reset() - Force fresh start if needed");
        console.log("");
        console.log("=== Manual Ruin Selling Workflow ===");
        console.log("• goodyManager.sellRuins() - Scan and process all completed instances");
        console.log("• Returns: { processed: X, successful: Y, failed: Z }");
        console.log("• Works independently of batch collection");
        console.log("• Useful for on-demand cleanup without full collection cycle");
        console.log("");
        console.log("=== Troubleshooting ===");
        console.log("• If no instances found: Check if you're in the game world view");
        console.log("• If hook fails: Try goodyManager.reinstallHook() or goodyManager.reinstallCleanupHook()");
        console.log("• If progress lost: Check file permissions for goodyHutState.json");
        console.log("• If batch fails: Try goodyManager.reset() to start fresh");
        console.log("• If storage fails: System will try multiple locations and fallback to memory");
        console.log("• If ruin selling fails: Check goodyManager.getRuinStats() for details");
        console.log("• For help: goodyManager.help()");
        console.log("");
        console.log("=== Platform-Specific Notes ===");
        console.log("• Android/ARM64: System automatically detects and uses appropriate storage locations");
        console.log("• Limited permissions: System will find writable directories automatically");
        console.log("• Frida environment: Safe fallbacks when Node.js modules are unavailable");
        console.log("");
        console.log("💡 The system automatically saves progress and can resume after script restarts!");
        console.log("💡 Only GEMS instances will be processed - other rewards are ignored for efficiency!");
        console.log("💡 Use goodyManager.showProgress() to see detailed statistics and next steps!");
        console.log("💡 Manual ruin selling (sellRuins) works independently of batch collection!");
    }
};

    console.log("🔧 Robust GoodyHutHelper Instance Manager loaded!");
    console.log(`⚡ GetCollectTime hook status: ${getCollectTimeHookInstalled ? 'ACTIVE' : 'FAILED'}`);
    console.log(`🧹 Cleanup hook status: ${cleanupHookInstalled ? 'ACTIVE' : 'FAILED'}`);

    // Show startup progress status
    const startupProgress = instanceManager.getProgressStatus();
    if (startupProgress.hasProgress) {
        console.log(`📊 ${startupProgress.summary}`);
        console.log("💡 Use goodyManager.resume() to continue or goodyManager.reset() to start fresh");
    } else {
        console.log("📊 No saved progress found");
        console.log("💡 Use goodyManager.batchCollection() to start collecting GEMS");
    }

    console.log("Use goodyManager.help() for all available commands");
});
