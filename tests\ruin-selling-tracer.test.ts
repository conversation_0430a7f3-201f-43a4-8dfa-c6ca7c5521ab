import { RuinSellingTracer, TraceEntry, TracingConfig } from '../src/ruin-selling-tracer';

describe('RuinSellingTracer', () => {
    let tracer: RuinSellingTracer;
    let mockInstance: any;
    let mockGoodyHutHelper: any;

    beforeEach(() => {
        // Mock Il2Cpp instance
        mockInstance = {
            handle: '0x12345678',
            class: {
                name: 'EntityController'
            },
            field: jest.fn(),
            method: jest.fn()
        };

        // Mock GoodyHutHelper
        mockGoodyHutHelper = {
            handle: '0x87654321',
            method: jest.fn().mockReturnValue({
                handle: '0x209DE3C',
                invoke: jest.fn()
            })
        };

        tracer = new RuinSellingTracer();
    });

    afterEach(() => {
        tracer.stopTracing();
        jest.clearAllMocks();
    });

    describe('Configuration', () => {
        test('should initialize with default configuration', () => {
            expect(tracer.isTracing()).toBe(false);
            expect(tracer.getConfiguration()).toEqual({
                enableTimingMetrics: true,
                enableStateTracking: true,
                enableParameterLogging: true,
                enableReturnValueLogging: true,
                enableErrorTracking: true,
                maxTraceEntries: 1000,
                logLevel: 'INFO'
            });
        });

        test('should update configuration', () => {
            const newConfig: Partial<TracingConfig> = {
                enableTimingMetrics: false,
                maxTraceEntries: 500,
                logLevel: 'DEBUG'
            };

            tracer.updateConfiguration(newConfig);
            const config = tracer.getConfiguration();

            expect(config.enableTimingMetrics).toBe(false);
            expect(config.maxTraceEntries).toBe(500);
            expect(config.logLevel).toBe('DEBUG');
        });
    });

    describe('Tracing Lifecycle', () => {
        test('should start and stop tracing', () => {
            expect(tracer.isTracing()).toBe(false);

            tracer.startTracing();
            expect(tracer.isTracing()).toBe(true);

            tracer.stopTracing();
            expect(tracer.isTracing()).toBe(false);
        });

        test('should clear trace history', () => {
            tracer.startTracing();
            tracer.traceMethodCall('SellRuins', mockInstance, [], 'test-session');
            
            expect(tracer.getTraceHistory().length).toBe(1);
            
            tracer.clearTraceHistory();
            expect(tracer.getTraceHistory().length).toBe(0);
        });
    });

    describe('Method Call Tracing', () => {
        beforeEach(() => {
            tracer.startTracing();
        });

        test('should trace successful method call', () => {
            const parameters = [{ type: 'boolean', value: true }];
            const sessionId = 'test-session-1';

            const traceId = tracer.traceMethodCall('SellRuins', mockInstance, parameters, sessionId);

            expect(traceId).toBeDefined();
            expect(typeof traceId).toBe('string');

            const history = tracer.getTraceHistory();
            expect(history.length).toBe(1);

            const entry = history[0];
            expect(entry.traceId).toBe(traceId);
            expect(entry.methodName).toBe('SellRuins');
            expect(entry.instanceHandle).toBe('0x12345678');
            expect(entry.parameters).toEqual(parameters);
            expect(entry.sessionId).toBe(sessionId);
            expect(entry.status).toBe('STARTED');
        });

        test('should trace method completion with return value', () => {
            const traceId = tracer.traceMethodCall('SellRuins', mockInstance, [], 'test-session');
            const returnValue = { type: 'boolean', value: true };

            tracer.traceMethodCompletion(traceId, returnValue);

            const history = tracer.getTraceHistory();
            const entry = history.find(e => e.traceId === traceId);

            expect(entry?.status).toBe('COMPLETED');
            expect(entry?.returnValue).toEqual(returnValue);
            expect(entry?.endTimestamp).toBeDefined();
            expect(entry?.executionTimeMs).toBeGreaterThanOrEqual(0);
        });

        test('should trace method error', () => {
            const traceId = tracer.traceMethodCall('SellRuins', mockInstance, [], 'test-session');
            const error = new Error('Test error');

            tracer.traceMethodError(traceId, error);

            const history = tracer.getTraceHistory();
            const entry = history.find(e => e.traceId === traceId);

            expect(entry?.status).toBe('ERROR');
            expect(entry?.error).toEqual({
                message: 'Test error',
                stack: error.stack,
                type: 'Error'
            });
        });
    });

    describe('State Tracking', () => {
        beforeEach(() => {
            tracer.startTracing();
        });

        test('should capture instance state before method call', () => {
            const mockState = {
                goodyHutState: 'COMPLETED_AWAITING',
                rewardType: 'GEMS',
                rewardAmount: 25
            };

            mockInstance.field.mockImplementation((fieldName: string) => ({
                value: mockState[fieldName as keyof typeof mockState]
            }));

            const traceId = tracer.traceMethodCall('SellRuins', mockInstance, [], 'test-session');

            const history = tracer.getTraceHistory();
            const entry = history.find(e => e.traceId === traceId);

            expect(entry?.preCallState).toBeDefined();
            expect(mockInstance.field).toHaveBeenCalled();
        });

        test('should capture instance state after method completion', () => {
            const traceId = tracer.traceMethodCall('SellRuins', mockInstance, [], 'test-session');
            
            const mockPostState = {
                goodyHutState: 'IDLE_READY',
                rewardType: 'GEMS',
                rewardAmount: 25
            };

            mockInstance.field.mockImplementation((fieldName: string) => ({
                value: mockPostState[fieldName as keyof typeof mockPostState]
            }));

            tracer.traceMethodCompletion(traceId, { type: 'boolean', value: true });

            const history = tracer.getTraceHistory();
            const entry = history.find(e => e.traceId === traceId);

            expect(entry?.postCallState).toBeDefined();
        });
    });

    describe('Performance Metrics', () => {
        beforeEach(() => {
            tracer.startTracing();
        });

        test('should calculate execution time', async () => {
            const traceId = tracer.traceMethodCall('SellRuins', mockInstance, [], 'test-session');
            
            // Simulate some execution time
            await new Promise(resolve => setTimeout(resolve, 10));
            
            tracer.traceMethodCompletion(traceId, { type: 'boolean', value: true });

            const history = tracer.getTraceHistory();
            const entry = history.find(e => e.traceId === traceId);

            expect(entry?.executionTimeMs).toBeGreaterThan(0);
            expect(entry?.executionTimeMs).toBeLessThan(100); // Should be reasonable
        });

        test('should generate performance summary', () => {
            // Create multiple trace entries
            for (let i = 0; i < 5; i++) {
                const traceId = tracer.traceMethodCall('SellRuins', mockInstance, [], `session-${i}`);
                tracer.traceMethodCompletion(traceId, { type: 'boolean', value: true });
            }

            const summary = tracer.getPerformanceSummary();

            expect(summary.totalCalls).toBe(5);
            expect(summary.successfulCalls).toBe(5);
            expect(summary.failedCalls).toBe(0);
            expect(summary.averageExecutionTimeMs).toBeGreaterThanOrEqual(0);
            expect(summary.minExecutionTimeMs).toBeGreaterThanOrEqual(0);
            expect(summary.maxExecutionTimeMs).toBeGreaterThanOrEqual(0);
        });
    });

    describe('Error Handling', () => {
        beforeEach(() => {
            tracer.startTracing();
        });

        test('should handle invalid trace ID gracefully', () => {
            expect(() => {
                tracer.traceMethodCompletion('invalid-id', { type: 'boolean', value: true });
            }).not.toThrow();

            expect(() => {
                tracer.traceMethodError('invalid-id', new Error('Test'));
            }).not.toThrow();
        });

        test('should handle tracing when not started', () => {
            tracer.stopTracing();

            expect(() => {
                tracer.traceMethodCall('SellRuins', mockInstance, [], 'test-session');
            }).not.toThrow();

            expect(tracer.getTraceHistory().length).toBe(0);
        });
    });

    describe('Trace History Management', () => {
        beforeEach(() => {
            tracer.startTracing();
        });

        test('should limit trace history to maxTraceEntries', () => {
            tracer.updateConfiguration({ maxTraceEntries: 3 });

            // Create more entries than the limit
            for (let i = 0; i < 5; i++) {
                tracer.traceMethodCall('SellRuins', mockInstance, [], `session-${i}`);
            }

            const history = tracer.getTraceHistory();
            expect(history.length).toBe(3);
            
            // Should keep the most recent entries
            expect(history[0].sessionId).toBe('session-2');
            expect(history[2].sessionId).toBe('session-4');
        });
    });

    describe('Structured Logging', () => {
        beforeEach(() => {
            tracer.startTracing();
        });

        test('should generate structured log output', () => {
            const traceId = tracer.traceMethodCall('SellRuins', mockInstance, [], 'test-session');
            tracer.traceMethodCompletion(traceId, { type: 'boolean', value: true });

            const logOutput = tracer.generateStructuredLog();

            expect(logOutput).toContain('RUIN_SELLING_TRACE');
            expect(logOutput).toContain('SellRuins');
            expect(logOutput).toContain('test-session');
            expect(logOutput).toContain('COMPLETED');
        });
    });
});
