// EntityController Auto-Upgrade Script using RVA addresses
// Usage: frida -U -l entitycontroller_rva_upgrade.js -f com.nexonm.dominations.adk

console.log("[+] EntityController RVA Auto-Upgrade Script Started");

// RVA addresses from the IL2CPP metadata
var RVA_ADDRESSES = {
    // From entitycontroller.cs IL2CPP dump
    GET_UNIQUE_ID: 0x1E33480,    // get_uniqueId()
    SET_UNIQUE_ID: 0x1E33488,    // set_uniqueId(int value)
    IS_SELECTED: 0x1E53050,      // IsSelected()

    // Found upgrade methods with their RVA addresses
    CAN_UPGRADE: 0x1E4027C,      // CanUpgrade(bool useAlternateResource = False)
    GET_LEVEL: 0x1E365A4,        // GetLevel()
    GET_MAX_LEVEL: 0x1E37428,    // GetMaxLevel()
    INSTANT_UPGRADE: 0x1E40540   // InstantUpgrade()
};

// Function to find the correct IL2CPP module and calculate proper base address
function findIL2CPPModule() {
    console.log("[+] Searching for IL2CPP module...");

    var modules = Process.enumerateModules();
    var targetModule = null;

    // Priority order for module search
    var moduleNames = [
        "GameAssembly.dll",
        "libil2cpp.so",
        "UnityPlayer.dll",
        "libunity.so"
    ];

    // First, try to find by exact name
    for (var i = 0; i < moduleNames.length; i++) {
        targetModule = Process.findModuleByName(moduleNames[i]);
        if (targetModule) {
            console.log("[+] Found " + moduleNames[i] + " at: " + targetModule.base);
            console.log("[+] Module size: 0x" + targetModule.size.toString(16));
            return targetModule;
        }
    }

    // If not found, search for modules containing these keywords
    var keywords = ["gameassembly", "il2cpp", "unity"];
    for (var i = 0; i < modules.length; i++) {
        var module = modules[i];
        var name = module.name.toLowerCase();

        for (var j = 0; j < keywords.length; j++) {
            if (name.includes(keywords[j])) {
                console.log("[+] Found potential module: " + module.name + " at: " + module.base);
                console.log("[+] Module size: 0x" + module.size.toString(16));
                return module;
            }
        }
    }

    console.log("[-] Could not find IL2CPP module");
    console.log("[+] Available modules:");
    modules.forEach(function(module) {
        console.log("  - " + module.name + " at " + module.base);
    });

    return null;
}

// Function to find IL2CPP metadata and calculate proper base address
function findIL2CPPImageBase(module) {
    console.log("[+] Searching for IL2CPP image base...");

    // Common IL2CPP image bases (these are typical compilation bases)
    var commonImageBases = [
        0x10000000,  // Common Windows IL2CPP base
        0x08000000,  // Alternative Windows base
        0x00400000,  // Standard PE base
        0x01000000,  // Another common base
        0x00000000   // No offset (direct RVA)
    ];

    var moduleBase = module.base;
    var moduleSize = module.size;

    // Try each potential image base
    for (var i = 0; i < commonImageBases.length; i++) {
        var imageBase = ptr(commonImageBases[i]);
        console.log("[+] Testing image base: 0x" + imageBase.toString(16));

        // Test with a known method (IsSelected)
        var testRVA = RVA_ADDRESSES.IS_SELECTED;
        if (testRVA) {
            var calculatedOffset = testRVA - commonImageBases[i];
            if (calculatedOffset >= 0 && calculatedOffset < moduleSize) {
                var testAddress = moduleBase.add(calculatedOffset);

                try {
                    // Try to read instruction bytes
                    var bytes = Memory.readByteArray(testAddress, 16);
                    if (bytes) {
                        // Check if it looks like ARM64/x86 code
                        var firstBytes = new Uint8Array(bytes.slice(0, 4));
                        if (isValidCodeBytes(firstBytes)) {
                            console.log("[+] Found valid image base: 0x" + imageBase.toString(16));
                            console.log("[+] Calculated offset: 0x" + calculatedOffset.toString(16));
                            return imageBase;
                        }
                    }
                } catch (e) {
                    // Continue to next base
                }
            }
        }
    }

    console.log("[-] Could not determine IL2CPP image base");
    return null;
}

// Helper function to check if bytes look like valid code
function isValidCodeBytes(bytes) {
    // ARM64 common instruction patterns
    if (bytes.length >= 4) {
        // Check for common ARM64 patterns
        var word = (bytes[3] << 24) | (bytes[2] << 16) | (bytes[1] << 8) | bytes[0];

        // Common ARM64 instruction patterns
        if ((word & 0x1F000000) === 0x14000000) return true; // B instruction
        if ((word & 0x9F000000) === 0x90000000) return true; // ADRP instruction
        if ((word & 0xFFE0FC00) === 0xAA0003E0) return true; // MOV instruction
        if ((word & 0xFF000000) === 0x52000000) return true; // MOVZ instruction
    }

    // x86/x64 common patterns
    if (bytes[0] === 0x48 || bytes[0] === 0x55 || bytes[0] === 0x53) return true; // Common x64 prefixes
    if (bytes[0] === 0xE9 || bytes[0] === 0xE8) return true; // JMP/CALL

    return false;
}

// Function to find methods using multiple approaches
function findMethodAddresses(module) {
    console.log("[+] Finding method addresses using multiple approaches...");

    var foundMethods = {};
    var moduleBase = module.base;

    // First, try to find the correct IL2CPP image base
    var imageBase = findIL2CPPImageBase(module);

    // Approach 1: Try RVA addresses with proper image base calculation
    console.log("[+] Approach 1: Testing RVA addresses with image base correction...");
    var methods = ['GET_UNIQUE_ID', 'IS_SELECTED', 'CAN_UPGRADE', 'GET_LEVEL', 'GET_MAX_LEVEL', 'INSTANT_UPGRADE'];

    methods.forEach(function(methodName) {
        var rva = RVA_ADDRESSES[methodName];
        if (rva && imageBase) {
            var offset = rva - imageBase.toInt32();
            if (offset >= 0 && offset < module.size) {
                var address = moduleBase.add(offset);
                try {
                    // Try to read instruction bytes
                    var bytes = Memory.readByteArray(address, 16);
                    if (bytes && isValidCodeBytes(new Uint8Array(bytes.slice(0, 4)))) {
                        foundMethods[methodName] = address;
                        console.log("[+] " + methodName + " found at offset 0x" + offset.toString(16) + " (VA: " + address + ")");
                    }
                } catch (e) {
                    console.log("[-] " + methodName + " at offset 0x" + offset.toString(16) + " failed: " + e);
                }
            } else {
                console.log("[-] " + methodName + " calculated offset 0x" + offset.toString(16) + " is outside module bounds");
            }
        }
    });

    // Approach 2: Search by symbol names if RVA failed
    if (Object.keys(foundMethods).length < methods.length) {
        console.log("[+] Approach 2: Searching by symbol names...");

        try {
            var symbols = module.enumerateSymbols();
            var symbolCount = 0;

            symbols.forEach(function(symbol) {
                var name = symbol.name;
                if (name && name.includes("EntityController")) {
                    symbolCount++;
                    console.log("[+] Found EntityController symbol: " + name);

                    if (name.includes("IsSelected") && !foundMethods.IS_SELECTED) {
                        foundMethods.IS_SELECTED = symbol.address;
                        console.log("[+] IS_SELECTED found by symbol: " + symbol.address);
                    } else if (name.includes("CanUpgrade") && !foundMethods.CAN_UPGRADE) {
                        foundMethods.CAN_UPGRADE = symbol.address;
                        console.log("[+] CAN_UPGRADE found by symbol: " + symbol.address);
                    } else if (name.includes("GetLevel") && !name.includes("GetMaxLevel") && !foundMethods.GET_LEVEL) {
                        foundMethods.GET_LEVEL = symbol.address;
                        console.log("[+] GET_LEVEL found by symbol: " + symbol.address);
                    } else if (name.includes("GetMaxLevel") && !foundMethods.GET_MAX_LEVEL) {
                        foundMethods.GET_MAX_LEVEL = symbol.address;
                        console.log("[+] GET_MAX_LEVEL found by symbol: " + symbol.address);
                    } else if (name.includes("InstantUpgrade") && !foundMethods.INSTANT_UPGRADE) {
                        foundMethods.INSTANT_UPGRADE = symbol.address;
                        console.log("[+] INSTANT_UPGRADE found by symbol: " + symbol.address);
                    } else if (name.includes("get_uniqueId") && !foundMethods.GET_UNIQUE_ID) {
                        foundMethods.GET_UNIQUE_ID = symbol.address;
                        console.log("[+] GET_UNIQUE_ID found by symbol: " + symbol.address);
                    }
                }
            });

            console.log("[+] Found " + symbolCount + " EntityController symbols total");
        } catch (e) {
            console.log("[-] Symbol enumeration failed: " + e);
        }
    }

    // Approach 3: Memory pattern scanning for method signatures
    if (Object.keys(foundMethods).length < methods.length) {
        console.log("[+] Approach 3: Memory pattern scanning...");
        foundMethods = scanForMethodPatterns(module, foundMethods);
    }

    // Approach 4: Try alternative RVA calculations
    if (Object.keys(foundMethods).length < methods.length) {
        console.log("[+] Approach 4: Alternative RVA calculations...");
        foundMethods = tryAlternativeRVACalculations(module, foundMethods);
    }

    // Approach 5: Try Frida's IL2CPP API
    if (Object.keys(foundMethods).length < methods.length) {
        console.log("[+] Approach 5: Frida IL2CPP API...");
        foundMethods = tryFridaIL2CPPApi(foundMethods);
    }

    console.log("[+] Found " + Object.keys(foundMethods).length + " out of " + methods.length + " methods");
    return foundMethods;
}

// Function to scan memory for method patterns
function scanForMethodPatterns(module, foundMethods) {
    console.log("[+] Scanning memory for method patterns...");

    try {
        var moduleBase = module.base;
        var moduleSize = module.size;
        var scanSize = Math.min(moduleSize, 0x1000000); // Limit scan to 16MB

        // Scan in chunks to avoid memory issues
        var chunkSize = 0x10000; // 64KB chunks

        for (var offset = 0; offset < scanSize; offset += chunkSize) {
            try {
                var currentSize = Math.min(chunkSize, scanSize - offset);
                var data = Memory.readByteArray(moduleBase.add(offset), currentSize);

                if (data) {
                    // Look for ARM64 function prologue patterns
                    var bytes = new Uint8Array(data);

                    for (var i = 0; i < bytes.length - 8; i += 4) {
                        // ARM64 function prologue: stp x29, x30, [sp, #-0x10]!
                        if (bytes[i] === 0xFD && bytes[i+1] === 0x7B &&
                            bytes[i+2] === 0xBF && bytes[i+3] === 0xA9) {

                            var potentialFunc = moduleBase.add(offset + i);
                            // This could be a function start - would need more analysis
                            // For now, just log it
                            if (i % 1000 === 0) { // Reduce spam
                                console.log("[+] Found potential function at: " + potentialFunc);
                            }
                        }
                    }
                }
            } catch (e) {
                // Continue scanning
            }
        }
    } catch (e) {
        console.log("[-] Pattern scanning failed: " + e);
    }

    return foundMethods;
}

// Function to try alternative RVA calculation methods
function tryAlternativeRVACalculations(module, foundMethods) {
    console.log("[+] Trying alternative RVA calculation methods...");

    var moduleBase = module.base;
    var methods = ['GET_UNIQUE_ID', 'IS_SELECTED', 'CAN_UPGRADE', 'GET_LEVEL', 'GET_MAX_LEVEL', 'INSTANT_UPGRADE'];

    // Try direct RVA as offset (no image base subtraction)
    methods.forEach(function(methodName) {
        if (!foundMethods[methodName]) {
            var rva = RVA_ADDRESSES[methodName];
            if (rva && rva < module.size) {
                var address = moduleBase.add(rva);
                try {
                    var bytes = Memory.readByteArray(address, 16);
                    if (bytes && isValidCodeBytes(new Uint8Array(bytes.slice(0, 4)))) {
                        foundMethods[methodName] = address;
                        console.log("[+] " + methodName + " found using direct RVA at: " + address);
                    }
                } catch (e) {
                    // Continue
                }
            }
        }
    });

    return foundMethods;
}

// Function to try Frida's IL2CPP API if available
function tryFridaIL2CPPApi(foundMethods) {
    console.log("[+] Trying Frida's IL2CPP API...");

    try {
        // Check if IL2CPP API is available
        if (typeof Il2Cpp !== 'undefined') {
            console.log("[+] IL2CPP API is available");

            // Try to perform domain enumeration
            Il2Cpp.perform(function() {
                console.log("[+] IL2CPP domain attached");

                // Try to find EntityController class
                var domain = Il2Cpp.domain;
                var assemblies = domain.assemblies;

                assemblies.forEach(function(assembly) {
                    console.log("[+] Checking assembly: " + assembly.name);

                    var image = assembly.image;
                    var classes = image.classes;

                    classes.forEach(function(klass) {
                        if (klass.name === "EntityController") {
                            console.log("[+] Found EntityController class!");

                            var methods = klass.methods;
                            methods.forEach(function(method) {
                                var methodName = method.name;
                                console.log("[+] Found method: " + methodName);

                                if (methodName === "IsSelected" && !foundMethods.IS_SELECTED) {
                                    foundMethods.IS_SELECTED = method.nativeFunction;
                                    console.log("[+] IS_SELECTED found via IL2CPP API");
                                } else if (methodName === "CanUpgrade" && !foundMethods.CAN_UPGRADE) {
                                    foundMethods.CAN_UPGRADE = method.nativeFunction;
                                    console.log("[+] CAN_UPGRADE found via IL2CPP API");
                                } else if (methodName === "GetLevel" && !foundMethods.GET_LEVEL) {
                                    foundMethods.GET_LEVEL = method.nativeFunction;
                                    console.log("[+] GET_LEVEL found via IL2CPP API");
                                } else if (methodName === "GetMaxLevel" && !foundMethods.GET_MAX_LEVEL) {
                                    foundMethods.GET_MAX_LEVEL = method.nativeFunction;
                                    console.log("[+] GET_MAX_LEVEL found via IL2CPP API");
                                } else if (methodName === "InstantUpgrade" && !foundMethods.INSTANT_UPGRADE) {
                                    foundMethods.INSTANT_UPGRADE = method.nativeFunction;
                                    console.log("[+] INSTANT_UPGRADE found via IL2CPP API");
                                } else if (methodName === "get_uniqueId" && !foundMethods.GET_UNIQUE_ID) {
                                    foundMethods.GET_UNIQUE_ID = method.nativeFunction;
                                    console.log("[+] GET_UNIQUE_ID found via IL2CPP API");
                                }
                            });
                        }
                    });
                });
            });
        } else {
            console.log("[-] IL2CPP API not available");
        }
    } catch (e) {
        console.log("[-] IL2CPP API failed: " + e);
    }

    return foundMethods;
}

// Function to create method hooks using found addresses
function createMethodHooks(foundMethods) {
    var methods = {};

    console.log("[+] Creating method hooks from found addresses...");

    try {
        // IsSelected() - bool return, takes EntityController* this
        if (foundMethods.IS_SELECTED) {
            methods.IsSelected = new NativeFunction(foundMethods.IS_SELECTED, 'bool', ['pointer']);
            console.log("[+] Hooked IsSelected at: " + foundMethods.IS_SELECTED);
        }

        // get_uniqueId() - int return, takes EntityController* this
        if (foundMethods.GET_UNIQUE_ID) {
            methods.getUniqueId = new NativeFunction(foundMethods.GET_UNIQUE_ID, 'int', ['pointer']);
            console.log("[+] Hooked get_uniqueId at: " + foundMethods.GET_UNIQUE_ID);
        }

        // CanUpgrade() - bool return, takes EntityController* this, bool useAlternateResource
        if (foundMethods.CAN_UPGRADE) {
            methods.CanUpgrade = new NativeFunction(foundMethods.CAN_UPGRADE, 'bool', ['pointer', 'bool']);
            console.log("[+] Hooked CanUpgrade at: " + foundMethods.CAN_UPGRADE);
        }

        // GetLevel() - int return, takes EntityController* this
        if (foundMethods.GET_LEVEL) {
            methods.GetLevel = new NativeFunction(foundMethods.GET_LEVEL, 'int', ['pointer']);
            console.log("[+] Hooked GetLevel at: " + foundMethods.GET_LEVEL);
        }

        // GetMaxLevel() - int return, takes EntityController* this
        if (foundMethods.GET_MAX_LEVEL) {
            methods.GetMaxLevel = new NativeFunction(foundMethods.GET_MAX_LEVEL, 'int', ['pointer']);
            console.log("[+] Hooked GetMaxLevel at: " + foundMethods.GET_MAX_LEVEL);
        }

        // InstantUpgrade() - void return, takes EntityController* this
        if (foundMethods.INSTANT_UPGRADE) {
            methods.InstantUpgrade = new NativeFunction(foundMethods.INSTANT_UPGRADE, 'void', ['pointer']);
            console.log("[+] Hooked InstantUpgrade at: " + foundMethods.INSTANT_UPGRADE);
        }

    } catch (error) {
        console.log("[-] Error creating method hooks: " + error);
    }

    return methods;
}

// Track EntityController instances by hooking constructor or other methods
var trackedInstances = [];

function hookInstanceTracking(foundMethods) {
    console.log("[+] Setting up instance tracking...");

    // Hook IsSelected to track when entities are selected
    if (foundMethods.IS_SELECTED) {
        Interceptor.attach(foundMethods.IS_SELECTED, {
            onEnter: function(args) {
                var entityPtr = args[0];

                // Add to tracked instances if not already present
                var found = false;
                for (var i = 0; i < trackedInstances.length; i++) {
                    if (trackedInstances[i].equals(entityPtr)) {
                        found = true;
                        break;
                    }
                }

                if (!found) {
                    trackedInstances.push(entityPtr);
                    console.log("[+] Tracking new EntityController instance: " + entityPtr);
                }
            },
            onLeave: function(retval) {
                if (retval) {
                    console.log("[+] Entity is selected!");
                }
            }
        });

        console.log("[+] Instance tracking hooked to IsSelected at: " + foundMethods.IS_SELECTED);
    } else {
        console.log("[-] Cannot set up instance tracking - IsSelected method not found");
    }
}

// Main upgrade function
function upgradeSelectedEntities() {
    console.log("[+] Checking for selected entities to upgrade...");

    var module = findIL2CPPModule();
    if (!module) {
        console.log("[-] Cannot find IL2CPP module");
        return 0;
    }

    // Find method addresses using multiple approaches
    var foundMethods = findMethodAddresses(module);
    if (Object.keys(foundMethods).length === 0) {
        console.log("[-] No methods found");
        return 0;
    }

    // Create method hooks
    var methods = createMethodHooks(foundMethods);
    
    // Check if we have all required methods
    var requiredMethods = ['IsSelected', 'CanUpgrade', 'GetLevel', 'GetMaxLevel', 'InstantUpgrade'];
    var missingMethods = [];
    
    requiredMethods.forEach(function(methodName) {
        if (!methods[methodName]) {
            missingMethods.push(methodName);
        }
    });
    
    if (missingMethods.length > 0) {
        console.log("[-] Missing required methods: " + missingMethods.join(', '));
        console.log("[-] Cannot proceed with upgrade");
        return 0;
    }
    
    var upgradedCount = 0;
    
    // Process tracked instances
    for (var i = 0; i < trackedInstances.length; i++) {
        try {
            var entityPtr = trackedInstances[i];
            
            // Check if entity is selected
            var isSelected = methods.IsSelected(entityPtr);
            if (!isSelected) {
                continue;
            }
            
            var uniqueId = methods.getUniqueId ? methods.getUniqueId(entityPtr) : i;
            console.log("[+] Found selected entity with ID: " + uniqueId);
            
            // Check if entity can upgrade
            var canUpgrade = methods.CanUpgrade(entityPtr, false);
            if (!canUpgrade) {
                console.log("[-] Entity " + uniqueId + " cannot upgrade");
                continue;
            }
            
            console.log("[+] Entity " + uniqueId + " can upgrade");
            
            // Get current and max levels
            var currentLevel = methods.GetLevel(entityPtr);
            var maxLevel = methods.GetMaxLevel(entityPtr);
            
            console.log("[+] Entity " + uniqueId + " - Current Level: " + currentLevel + ", Max Level: " + maxLevel);
            
            // Upgrade until max level is reached
            var upgradeAttempts = 0;
            var maxAttempts = 50;
            
            while (currentLevel < maxLevel && upgradeAttempts < maxAttempts) {
                try {
                    console.log("[+] Upgrading entity " + uniqueId + " from level " + currentLevel);
                    methods.InstantUpgrade(entityPtr);
                    
                    // Get new level after upgrade
                    var newLevel = methods.GetLevel(entityPtr);
                    if (newLevel > currentLevel) {
                        currentLevel = newLevel;
                        console.log("[+] Successfully upgraded to level " + currentLevel);
                        upgradedCount++;
                    } else {
                        console.log("[-] Upgrade failed or level didn't increase");
                        break;
                    }
                    
                    upgradeAttempts++;
                    Thread.sleep(0.1); // Small delay
                    
                } catch (upgradeError) {
                    console.log("[-] Error during upgrade: " + upgradeError);
                    break;
                }
            }
            
            if (upgradeAttempts >= maxAttempts) {
                console.log("[-] Reached maximum upgrade attempts for entity " + uniqueId);
            } else if (currentLevel >= maxLevel) {
                console.log("[+] Entity " + uniqueId + " reached maximum level: " + currentLevel);
            }
            
        } catch (entityError) {
            console.log("[-] Error processing entity: " + entityError);
            // Remove invalid instances
            trackedInstances.splice(i, 1);
            i--;
        }
    }
    
    console.log("[+] Upgrade check complete. Total upgrades performed: " + upgradedCount);
    return upgradedCount;
}

// Initialize the script
function initialize() {
    var module = findIL2CPPModule();
    if (module) {
        var foundMethods = findMethodAddresses(module);
        if (Object.keys(foundMethods).length > 0) {
            hookInstanceTracking(foundMethods);
            console.log("[+] Script initialized successfully");
            console.log("[+] Found " + Object.keys(foundMethods).length + " methods");
            console.log("[+] Select an entity in the game, then call rpc.exports.upgradeSelected()");
        } else {
            console.log("[-] Failed to find any methods");
        }
    } else {
        console.log("[-] Failed to initialize - no IL2CPP module found");
    }
}

// Expose functions via RPC
rpc.exports = {
    upgradeSelected: upgradeSelectedEntities,
    initialize: initialize,
    getTrackedCount: function() {
        return trackedInstances.length;
    },
    clearTracked: function() {
        trackedInstances = [];
        return "Cleared tracked instances";
    }
};

// Auto-initialize after a delay
setTimeout(initialize, 2000);

console.log("[+] Script loaded. Available commands:");
console.log("  rpc.exports.upgradeSelected() - Check and upgrade selected entities");
console.log("  rpc.exports.getTrackedCount() - Get number of tracked instances");
console.log("  rpc.exports.clearTracked() - Clear tracked instances");
console.log("  rpc.exports.initialize() - Re-initialize the script");
