"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
require("frida-il2cpp-bridge");
Il2Cpp.perform(function () {
    console.log("🔧 Loading Robust GoodyHutHelper Instance Manager...");
    // Install GetCollectTime hook for instant collection completion
    let getCollectTimeHookInstalled = false;
    let getCollectTimeHookCallCount = 0;
    // GoodyHutHelperConfig cleanup hook tracking
    let cleanupHookInstalled = false;
    let cleanupHookCallCount = 0;
    let cleanupAutoEnabledCount = 0;
    function installGetCollectTimeHook() {
        try {
            console.log("🎯 Installing GetCollectTime hook for instant collection completion...");
            const AssemblyCSharp = Il2Cpp.domain.assembly("Assembly-CSharp").image;
            const GoodyHutHelper = AssemblyCSharp.class("GoodyHutHelper");
            if (!GoodyHutHelper) {
                console.log("❌ GoodyHutHelper class not found");
                return;
            }
            // Find the GetCollectTime method
            const getCollectTimeMethod = GoodyHutHelper.method("GetCollectTime");
            if (!getCollectTimeMethod) {
                console.log("❌ GetCollectTime method not found");
                return;
            }
            console.log(`🎯 Found GetCollectTime method at: ${getCollectTimeMethod.handle}`);
            // Install the hook
            getCollectTimeMethod.implementation = function () {
                getCollectTimeHookCallCount++;
                // Log occasionally to avoid spam (every 10th call)
                if (getCollectTimeHookCallCount % 10 === 1) {
                    console.log(`⚡ GetCollectTime hook triggered (call #${getCollectTimeHookCallCount}) - returning 0 for instant completion`);
                }
                // Return 0 to force instant completion
                return 0.0;
            };
            getCollectTimeHookInstalled = true;
            console.log("✅ GetCollectTime hook installed successfully!");
            console.log("💡 All GoodyHut collections will now complete instantly");
        }
        catch (error) {
            console.log(`❌ Failed to install GetCollectTime hook: ${error}`);
            getCollectTimeHookInstalled = false;
        }
    }
    // Install GoodyHutHelperConfig cleanup hook
    function installCleanupHook() {
        try {
            console.log("🧹 Installing GoodyHutHelperConfig cleanup hook...");
            const AssemblyCSharp = Il2Cpp.domain.assembly("Assembly-CSharp").image;
            const GoodyHutHelper = AssemblyCSharp.class("GoodyHutHelper");
            if (!GoodyHutHelper) {
                console.log("❌ GoodyHutHelper class not found for cleanup hook");
                return;
            }
            // Find the CanCollect method to hook
            const canCollectMethod = GoodyHutHelper.method("CanCollect");
            if (!canCollectMethod) {
                console.log("❌ CanCollect method not found for cleanup hook");
                return;
            }
            console.log(`🧹 Found CanCollect method at: ${canCollectMethod.handle}`);
            // Store reference to original method implementation before replacing it
            const originalCanCollectImpl = canCollectMethod.implementation;
            // Verify we have a valid original implementation
            if (!originalCanCollectImpl) {
                console.log("❌ Could not capture original CanCollect implementation");
                return;
            }
            // Install the hook to automatically enable cleanup when CanCollect returns false
            canCollectMethod.implementation = function () {
                cleanupHookCallCount++;
                let originalResult;
                try {
                    // Try to call the original method implementation with proper context
                    if (originalCanCollectImpl && typeof originalCanCollectImpl.call === 'function') {
                        originalResult = originalCanCollectImpl.call(this);
                    }
                    else {
                        // Fallback: call the method directly on the instance
                        originalResult = this.method("CanCollect").invoke();
                    }
                }
                catch (invocationError) {
                    // Final fallback: try alternative invocation method
                    try {
                        originalResult = this.method("CanCollect").invoke();
                    }
                    catch (fallbackError) {
                        console.log(`❌ Failed to invoke CanCollect: ${fallbackError}`);
                        return false; // Safe default
                    }
                }
                // If CanCollect returns false, try to enable cleanup
                if (originalResult === false) {
                    try {
                        // Try to access the GoodyHutHelperConfig
                        const configField = this.field("m_config");
                        if (configField && configField.value && configField.value !== null) {
                            const config = configField.value;
                            // Check current cleanup state
                            const cleanupField = config.field("cleanUp");
                            if (cleanupField && cleanupField.value === false) {
                                // Set cleanup to true (offset 0x30 as per C# decompiled code)
                                cleanupField.value = true;
                                cleanupAutoEnabledCount++;
                                // Log occasionally to avoid spam (every 10th auto-enable)
                                if (cleanupAutoEnabledCount % 10 === 1) {
                                    console.log(`🧹 Auto-enabled cleanup for non-collectible instance (count: ${cleanupAutoEnabledCount})`);
                                }
                            }
                        }
                    }
                    catch (configError) {
                        // Silently handle config access errors to avoid spam
                        // Only log on first few errors
                        if (cleanupHookCallCount <= 5) {
                            console.log(`⚠️ Could not access GoodyHutHelperConfig: ${configError}`);
                        }
                    }
                }
                return originalResult;
            };
            cleanupHookInstalled = true;
            console.log("✅ GoodyHutHelperConfig cleanup hook installed successfully!");
            console.log("💡 Cleanup will be automatically enabled for non-collectible instances");
        }
        catch (error) {
            console.log(`❌ Failed to install cleanup hook: ${error}`);
            cleanupHookInstalled = false;
        }
    }
    // Install both hooks immediately
    installGetCollectTimeHook();
    installCleanupHook();
    class GoodyHutInstanceManager {
        constructor() {
            this.validInstances = [];
            this.collectibleInstances = [];
            this.batchSize = 10;
            this.batchDelay = 2000; // 2 seconds between batches (increased for stability)
            // DoJobBuyThrough configuration and tracking
            this.attemptInstantCompletion = true;
            this.buyThroughFailureCount = 0;
            this.buyThroughSuccessCount = 0;
            this.buyThroughFailureThreshold = 20; // Stop attempting after 20 consecutive failures
            // Ruin selling configuration and tracking
            this.attemptRuinSelling = true;
            this.ruinSellSuccessCount = 0;
            this.ruinSellFailureCount = 0;
            // State persistence
            this.stateFilePath = "goodyHutState.json";
            this.alternativeStatePaths = [];
            this.currentWorkingPath = "";
            this.stateExpirationHours = 24; // Reset state after 24 hours
            this.stateVersion = 2; // Increment when state structure changes
            // Initialize storage paths on creation
            this.initializeStoragePaths();
        }
        // Initialize alternative storage paths for cross-platform compatibility
        initializeStoragePaths() {
            try {
                const os = require('os');
                const path = require('path');
                // Initialize alternative paths in order of preference
                this.alternativeStatePaths = [
                    // 1. Current workspace directory (original)
                    "goodyHutState.json",
                    // 2. User's temp directory
                    path.join(os.tmpdir(), "goodyHutState.json"),
                    // 3. User's home directory
                    path.join(os.homedir(), "goodyHutState.json"),
                    // 4. User's documents folder (Windows)
                    path.join(os.homedir(), "Documents", "goodyHutState.json"),
                    // 5. System temp with unique name
                    path.join(os.tmpdir(), "dominations_goodyHutState.json")
                ];
                console.log(`📁 Initialized ${this.alternativeStatePaths.length} potential storage locations`);
            }
            catch (error) {
                console.log(`⚠️ Could not initialize alternative storage paths: ${error}`);
                // Fallback to just the original path
                this.alternativeStatePaths = ["goodyHutState.json"];
            }
        }
        // Find the first writable storage location
        findWritableStoragePath() {
            if (this.currentWorkingPath) {
                return this.currentWorkingPath; // Use cached working path
            }
            const fs = require('fs');
            for (const testPath of this.alternativeStatePaths) {
                try {
                    // Test if we can write to this location
                    const testData = JSON.stringify({ test: true, timestamp: Date.now() });
                    fs.writeFileSync(testPath, testData, 'utf8');
                    // If write succeeded, try to read it back
                    const readData = fs.readFileSync(testPath, 'utf8');
                    const parsed = JSON.parse(readData);
                    if (parsed.test === true) {
                        // Clean up test file
                        try {
                            fs.unlinkSync(testPath);
                        }
                        catch (cleanupError) {
                            // Ignore cleanup errors
                        }
                        this.currentWorkingPath = testPath;
                        console.log(`✅ Found writable storage location: ${testPath}`);
                        return testPath;
                    }
                }
                catch (error) {
                    // This path is not writable, try the next one
                    console.log(`❌ Cannot write to ${testPath}: ${error.message || error}`);
                    continue;
                }
            }
            // If no writable path found, return the original and let it fail with clear error
            console.log(`❌ No writable storage locations found! Using fallback: ${this.stateFilePath}`);
            return this.stateFilePath;
        }
        // Safe method invocation with null pointer protection
        safeInvoke(instance, methodName) {
            try {
                // First check if instance is null or invalid
                if (!instance || instance.isNull()) {
                    return { error: "Null instance", value: null };
                }
                // Try to get the method
                const method = instance.method(methodName);
                if (!method) {
                    return { error: `Method ${methodName} not found`, value: null };
                }
                // Try to invoke the method
                const result = method.invoke();
                return { error: null, value: result };
            }
            catch (error) {
                // Catch access violations and other errors
                const errorMsg = String(error);
                if (errorMsg.includes("access violation") || errorMsg.includes("0x0")) {
                    return { error: "Access violation - invalid instance", value: null };
                }
                return { error: `Method error: ${errorMsg}`, value: null };
            }
        }
        // Validate a single GoodyHutHelper instance
        validateInstance(instance, entityIndex) {
            console.log(`Validating instance from EntityController ${entityIndex}...`);
            const result = {
                instance,
                entityIndex,
                isValid: false,
                canCollect: false,
                canBuyThrough: false,
                state: "UNKNOWN",
                rewardType: "UNKNOWN",
                rewardAmount: null
            };
            // Test basic method calls
            const isJobCompleteResult = this.safeInvoke(instance, "IsJobComplete");
            if (isJobCompleteResult.error) {
                result.error = isJobCompleteResult.error;
                return result;
            }
            const canCollectResult = this.safeInvoke(instance, "CanCollect");
            if (canCollectResult.error) {
                result.error = canCollectResult.error;
                return result;
            }
            const canBuyThroughResult = this.safeInvoke(instance, "CanBuyThrough");
            if (canBuyThroughResult.error) {
                result.error = canBuyThroughResult.error;
                return result;
            }
            // Get reward information
            const rewardTypeResult = this.safeInvoke(instance, "GetRewardType");
            const rewardAmountResult = this.safeInvoke(instance, "GetRewardAmount");
            // If we get here, instance is valid
            result.isValid = true;
            result.canCollect = canCollectResult.value;
            result.canBuyThrough = canBuyThroughResult.value;
            result.rewardType = rewardTypeResult.error ? "UNKNOWN" : String(rewardTypeResult.value);
            result.rewardAmount = rewardAmountResult.error ? null : rewardAmountResult.value;
            // Determine state
            const isJobComplete = isJobCompleteResult.value;
            if (isJobComplete === true && result.canCollect === true) {
                result.state = "IDLE_READY";
            }
            else if (isJobComplete === false && result.canCollect === false) {
                result.state = "COLLECTING";
            }
            else if (isJobComplete === true && result.canCollect === false) {
                result.state = "COMPLETED_AWAITING";
            }
            else {
                result.state = "INCONSISTENT";
            }
            console.log(`✅ EntityController ${entityIndex}: Valid - ${result.state} (CanCollect: ${result.canCollect}, Reward: ${result.rewardAmount} ${result.rewardType})`);
            return result;
        }
        // Scan all EntityController instances and validate GoodyHutHelper components
        scanAndValidateInstances() {
            console.log("🔍 Starting comprehensive instance validation...");
            this.validInstances = [];
            this.collectibleInstances = [];
            try {
                const EntityController = Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController");
                const entityInstances = Il2Cpp.gc.choose(EntityController);
                console.log(`Found ${entityInstances.length} EntityController instances`);
                console.log("Validating GoodyHutHelper components...\n");
                let validCount = 0;
                let invalidCount = 0;
                let collectibleCount = 0;
                let gemsCount = 0;
                entityInstances.forEach((entity, index) => {
                    try {
                        const goodyHut = entity.field("m_goodyHut");
                        if (goodyHut && goodyHut.value && goodyHut.value !== null && goodyHut.value.toString() !== "0x0") {
                            const goodyHutInstance = goodyHut.value;
                            const validation = this.validateInstance(goodyHutInstance, index);
                            if (validation.isValid) {
                                this.validInstances.push(validation);
                                validCount++;
                                if (validation.canCollect) {
                                    this.collectibleInstances.push(validation);
                                    collectibleCount++;
                                    if (validation.rewardType === "GEMS") {
                                        gemsCount++;
                                    }
                                }
                            }
                            else {
                                invalidCount++;
                            }
                        }
                    }
                    catch (error) {
                        invalidCount++;
                    }
                });
                console.log("\n=== VALIDATION SUMMARY ===");
                console.log(`✅ Valid instances: ${validCount}`);
                console.log(`❌ Invalid instances: ${invalidCount}`);
                console.log(`🎯 Collectible instances: ${collectibleCount}`);
                console.log(`💎 GEMS collectible instances: ${gemsCount}`);
                console.log(`📊 Success rate: ${Math.round((validCount / (validCount + invalidCount)) * 100)}%`);
            }
            catch (error) {
                console.log(`❌ Error during validation: ${error}`);
            }
        }
        // Get detailed information about valid instances
        showValidInstances() {
            console.log("\n=== VALID INSTANCES DETAILS ===");
            this.validInstances.forEach((validation, index) => {
                console.log(`\n[${index}] EntityController ${validation.entityIndex}:`);
                console.log(`  State: ${validation.state}`);
                console.log(`  CanCollect: ${validation.canCollect}`);
                console.log(`  CanBuyThrough: ${validation.canBuyThrough}`);
                console.log(`  Reward: ${validation.rewardAmount} ${validation.rewardType}`);
                // Get additional details safely
                const timeLeftResult = this.safeInvoke(validation.instance, "GetJobTimeLeft");
                const healthResult = this.safeInvoke(validation.instance, "GetHealth");
                const explorationsResult = this.safeInvoke(validation.instance, "GetExplorations");
                if (!timeLeftResult.error)
                    console.log(`  TimeLeft: ${timeLeftResult.value}`);
                if (!healthResult.error)
                    console.log(`  Health: ${healthResult.value}`);
                if (!explorationsResult.error)
                    console.log(`  Explorations: ${explorationsResult.value}`);
            });
        }
        // Get the best instance for collection (GEMS rewards only)
        getBestCollectibleInstance() {
            if (this.collectibleInstances.length === 0) {
                console.log("❌ No collectible instances found");
                return null;
            }
            // Filter for GEMS rewards only
            const gemsInstances = this.collectibleInstances.filter(v => v.rewardType === "GEMS");
            if (gemsInstances.length === 0) {
                console.log("❌ No collectible instances with GEMS rewards found");
                console.log(`Available rewards: ${this.collectibleInstances.map(v => `${v.rewardAmount} ${v.rewardType}`).join(", ")}`);
                return null;
            }
            console.log(`💎 Found ${gemsInstances.length} collectible instances with GEMS rewards`);
            // Prioritize IDLE_READY instances with GEMS
            const idleReadyGems = gemsInstances.filter(v => v.state === "IDLE_READY");
            if (idleReadyGems.length > 0) {
                console.log(`✅ Using IDLE_READY GEMS instance: ${idleReadyGems[0].rewardAmount} GEMS`);
                return idleReadyGems[0];
            }
            // Fallback to any GEMS collectible instance
            console.log(`✅ Using first GEMS collectible instance (${gemsInstances[0].state}): ${gemsInstances[0].rewardAmount} GEMS`);
            return gemsInstances[0];
        }
        // Check if DoJobBuyThrough is available and should be attempted
        shouldAttemptInstantCompletion(validation) {
            // If we've disabled instant completion due to failures, don't attempt
            if (!this.attemptInstantCompletion) {
                return false;
            }
            // If we've hit too many failures, disable instant completion
            if (this.buyThroughFailureCount >= this.buyThroughFailureThreshold) {
                console.log(`⚠️ Disabling instant completion after ${this.buyThroughFailureCount} consecutive failures`);
                this.attemptInstantCompletion = false;
                return false;
            }
            // Check if the instance supports DoJobBuyThrough
            if (!validation.canBuyThrough) {
                return false;
            }
            return true;
        }
        // Execute DoJobBuyThrough with proper error handling and tracking
        executeInstantCompletion(validation) {
            const buyThroughResult = this.safeInvoke(validation.instance, "DoJobBuyThrough");
            if (buyThroughResult.error) {
                this.buyThroughFailureCount++;
                // Check for specific error types
                if (buyThroughResult.error.includes("abort was called")) {
                    console.log(`    ⚠️ Instant completion blocked (insufficient currency or game restriction)`);
                }
                else {
                    console.log(`    ⚠️ Instant completion failed: ${buyThroughResult.error}`);
                }
                return false;
            }
            else {
                this.buyThroughSuccessCount++;
                this.buyThroughFailureCount = 0; // Reset failure count on success
                console.log(`    💎 Instant completion successful!`);
                return true;
            }
        }
        // Simplified DoJobBuyThrough execution without monitoring
        testDoJobBuyThrough(validation) {
            console.log(`\n💎 Executing DoJobBuyThrough on EntityController ${validation.entityIndex}...`);
            const buyThroughResult = this.safeInvoke(validation.instance, "DoJobBuyThrough");
            if (buyThroughResult.error) {
                console.log(`⚠️ DoJobBuyThrough not available: ${buyThroughResult.error}`);
                return false;
            }
            else {
                console.log(`✅ DoJobBuyThrough executed successfully - instant completion!`);
                return true;
            }
        }
        // Simplified StartCollect execution without aggressive monitoring
        executeStartCollect(validation) {
            console.log(`\n🚀 Executing StartCollect on EntityController ${validation.entityIndex}...`);
            // Double-check the instance is still valid and collectible
            const canCollectResult = this.safeInvoke(validation.instance, "CanCollect");
            if (canCollectResult.error) {
                console.log(`❌ Instance became invalid: ${canCollectResult.error}`);
                return false;
            }
            if (!canCollectResult.value) {
                console.log(`❌ Instance no longer collectible`);
                return false;
            }
            // Execute StartCollect
            const startCollectResult = this.safeInvoke(validation.instance, "StartCollect");
            if (startCollectResult.error) {
                console.log(`❌ StartCollect failed: ${startCollectResult.error}`);
                return false;
            }
            console.log(`✅ StartCollect executed successfully!`);
            // Try DoJobBuyThrough for instant completion
            setTimeout(() => {
                const buyThroughResult = this.safeInvoke(validation.instance, "DoJobBuyThrough");
                if (buyThroughResult.error) {
                    console.log(`⚠️ DoJobBuyThrough not available (this is normal if collection is already complete)`);
                }
                else {
                    console.log(`💎 DoJobBuyThrough executed successfully - instant completion!`);
                }
                console.log(`🎉 Collection process completed for ${validation.rewardAmount} GEMS`);
            }, 500);
            return true;
        }
        // Ruin selling methods for clearing completed instances
        // Check if an instance has sellable ruins or debris
        checkForSellableRuins(validation) {
            try {
                // Try to get ruin-related methods from the GoodyHutHelper instance
                const hasRuinsResult = this.safeInvoke(validation.instance, "HasRuins");
                const hasDebrisResult = this.safeInvoke(validation.instance, "HasDebris");
                const canSellResult = this.safeInvoke(validation.instance, "CanSell");
                const canClearResult = this.safeInvoke(validation.instance, "CanClear");
                // Check for various ruin/debris indicators
                if (!hasRuinsResult.error && hasRuinsResult.value === true) {
                    return { hasRuins: true, ruinType: "ruins", sellMethod: "SellRuins" };
                }
                if (!hasDebrisResult.error && hasDebrisResult.value === true) {
                    return { hasRuins: true, ruinType: "debris", sellMethod: "ClearDebris" };
                }
                if (!canSellResult.error && canSellResult.value === true) {
                    return { hasRuins: true, ruinType: "sellable", sellMethod: "DoSell" };
                }
                if (!canClearResult.error && canClearResult.value === true) {
                    return { hasRuins: true, ruinType: "clearable", sellMethod: "DoClear" };
                }
                return { hasRuins: false };
            }
            catch (error) {
                console.log(`    ⚠️ Error checking for ruins: ${error}`);
                return { hasRuins: false };
            }
        }
        // Execute ruin selling/clearing operation
        executeRuinSelling(validation, ruinInfo) {
            try {
                console.log(`    🗑️ Attempting to sell/clear ${ruinInfo.ruinType} using ${ruinInfo.sellMethod}...`);
                const sellResult = this.safeInvoke(validation.instance, ruinInfo.sellMethod);
                if (sellResult.error) {
                    console.log(`    ❌ Ruin selling failed: ${sellResult.error}`);
                    this.ruinSellFailureCount++;
                    return false;
                }
                console.log(`    ✅ Successfully sold/cleared ${ruinInfo.ruinType}!`);
                this.ruinSellSuccessCount++;
                return true;
            }
            catch (error) {
                console.log(`    ❌ Ruin selling error: ${error}`);
                this.ruinSellFailureCount++;
                return false;
            }
        }
        // Process completed instances to sell ruins and clear them for future collection
        async processCompletedInstances(validation) {
            if (!this.attemptRuinSelling) {
                return false;
            }
            // Only process instances in COMPLETED_AWAITING state
            if (validation.state !== "COMPLETED_AWAITING") {
                return false;
            }
            console.log(`    🔍 Processing completed instance (EntityController ${validation.entityIndex}) for ruin selling...`);
            // Check if instance has sellable ruins
            const ruinInfo = this.checkForSellableRuins(validation);
            if (!ruinInfo.hasRuins) {
                console.log(`    ℹ️ No sellable ruins found for EntityController ${validation.entityIndex}`);
                return false;
            }
            console.log(`    💎 Found ${ruinInfo.ruinType} that can be sold/cleared`);
            // Execute the selling operation
            const sellSuccess = this.executeRuinSelling(validation, { ruinType: ruinInfo.ruinType, sellMethod: ruinInfo.sellMethod });
            if (sellSuccess) {
                // Wait a moment for the operation to complete
                await new Promise(resolve => setTimeout(resolve, 500));
                // Re-validate the instance to check if it's now available for collection
                const updatedValidation = this.validateInstance(validation.instance, validation.entityIndex);
                if (updatedValidation.isValid && updatedValidation.state === "IDLE_READY") {
                    console.log(`    🎉 Instance cleared and ready for future collection!`);
                    return true;
                }
                else {
                    console.log(`    ⚠️ Instance state after selling: ${updatedValidation.state}`);
                    return false;
                }
            }
            return false;
        }
        // Batch processing methods
        // Validate a batch of instances and return only GEMS collectible ones
        async validateBatch(entityInstances, startIndex, batchSize) {
            const batchEnd = Math.min(startIndex + batchSize, entityInstances.length);
            const validGemsInstances = [];
            console.log(`🔍 Validating batch instances ${startIndex + 1}-${batchEnd}...`);
            for (let i = startIndex; i < batchEnd; i++) {
                try {
                    const entity = entityInstances[i];
                    const goodyHut = entity.field("m_goodyHut");
                    if (goodyHut && goodyHut.value && goodyHut.value !== null && goodyHut.value.toString() !== "0x0") {
                        const goodyHutInstance = goodyHut.value;
                        const validation = this.validateInstance(goodyHutInstance, i);
                        // Only include valid, collectible instances with GEMS rewards
                        if (validation.isValid && validation.canCollect && validation.rewardType === "GEMS") {
                            validGemsInstances.push(validation);
                        }
                    }
                }
                catch (error) {
                    // Silently skip invalid instances
                }
            }
            return validGemsInstances;
        }
        // Validate a batch with state persistence (skip already processed instances) and process completed instances for ruin selling
        async validateBatchWithState(entityInstances, startIndex, batchSize, state) {
            const batchEnd = Math.min(startIndex + batchSize, entityInstances.length);
            const validGemsInstances = [];
            let skippedCount = 0;
            let ruinProcessedCount = 0;
            console.log(`🔍 Validating batch instances ${startIndex + 1}-${batchEnd}...`);
            for (let i = startIndex; i < batchEnd; i++) {
                // Skip if already processed
                if (state.processedEntityIndices.has(i)) {
                    skippedCount++;
                    continue;
                }
                try {
                    const entity = entityInstances[i];
                    const goodyHut = entity.field("m_goodyHut");
                    if (goodyHut && goodyHut.value && goodyHut.value !== null && goodyHut.value.toString() !== "0x0") {
                        const goodyHutInstance = goodyHut.value;
                        const validation = this.validateInstance(goodyHutInstance, i);
                        if (validation.isValid) {
                            // Check if this is a collectible GEMS instance
                            if (validation.canCollect && validation.rewardType === "GEMS") {
                                validGemsInstances.push(validation);
                            }
                            // Check if this is a completed instance that might have sellable ruins
                            else if (validation.state === "COMPLETED_AWAITING" && validation.rewardType === "GEMS") {
                                console.log(`  🗑️ Found completed GEMS instance (EntityController ${i}) - checking for ruins...`);
                                const ruinProcessed = await this.processCompletedInstances(validation);
                                if (ruinProcessed) {
                                    ruinProcessedCount++;
                                    console.log(`  ✅ Ruins cleared for EntityController ${i} - may be available for future collection`);
                                }
                            }
                        }
                    }
                }
                catch (error) {
                    // Silently skip invalid instances
                }
            }
            if (skippedCount > 0) {
                console.log(`⏭️ Skipped ${skippedCount} already processed instances`);
            }
            if (ruinProcessedCount > 0) {
                console.log(`🗑️ Processed ruins for ${ruinProcessedCount} completed instances`);
            }
            return validGemsInstances;
        }
        // Collect all instances in a batch with improved success detection and instant completion handling
        async collectBatch(batchInstances, batchNumber) {
            if (batchInstances.length === 0) {
                console.log(`📦 Batch ${batchNumber}: No GEMS instances to collect`);
                return { collectionsStarted: 0, instantCompletions: 0 };
            }
            console.log(`🚀 Batch ${batchNumber}: Starting collection of ${batchInstances.length} GEMS instances...`);
            let collectionsStarted = 0;
            let instantCompletions = 0;
            // Start collection on all instances in the batch
            const collectionPromises = batchInstances.map(async (validation, index) => {
                try {
                    console.log(`  💎 [${index + 1}/${batchInstances.length}] Collecting ${validation.rewardAmount} GEMS from EntityController ${validation.entityIndex}`);
                    // Execute StartCollect - this is the primary success metric
                    const startCollectResult = this.safeInvoke(validation.instance, "StartCollect");
                    if (startCollectResult.error) {
                        console.log(`    ❌ StartCollect failed: ${startCollectResult.error}`);
                        return { started: false, instantCompleted: false };
                    }
                    console.log(`    ✅ Collection started successfully`);
                    collectionsStarted++;
                    let instantCompleted = false;
                    // With GetCollectTime hook, collections should complete instantly
                    if (getCollectTimeHookInstalled) {
                        console.log(`    ⚡ Collection completing instantly via GetCollectTime hook`);
                        instantCompleted = true;
                        instantCompletions++; // Count hook-based instant completion
                    }
                    else {
                        // Fallback to DoJobBuyThrough if hook is not available
                        await new Promise(resolve => setTimeout(resolve, 100));
                        if (this.shouldAttemptInstantCompletion(validation)) {
                            instantCompleted = this.executeInstantCompletion(validation);
                            if (instantCompleted) {
                                instantCompletions++;
                            }
                        }
                        else {
                            console.log(`    ⏳ Instant completion skipped (will complete naturally)`);
                        }
                    }
                    console.log(`    🎉 Collection initiated for ${validation.rewardAmount} GEMS`);
                    return { started: true, instantCompleted };
                }
                catch (error) {
                    console.log(`    ❌ Collection error: ${error}`);
                    return { started: false, instantCompleted: false };
                }
            });
            // Wait for all collections in this batch to complete
            const results = await Promise.all(collectionPromises);
            const finalCollectionsStarted = results.filter(r => r.started).length;
            const finalInstantCompletions = results.filter(r => r.instantCompleted).length;
            console.log(`📦 Batch ${batchNumber} completed: ${finalCollectionsStarted}/${batchInstances.length} collections started, ${finalInstantCompletions}/${batchInstances.length} instant completions`);
            return { collectionsStarted: finalCollectionsStarted, instantCompletions: finalInstantCompletions };
        }
        // Main batch processing method with state persistence
        async processBatchCollection(forceReset = false) {
            console.log("🔄 Starting batch collection process...");
            // Show startup progress status
            if (!forceReset) {
                const progressStatus = this.getProgressStatus();
                if (progressStatus.hasProgress) {
                    console.log(`📊 ${progressStatus.summary}`);
                }
            }
            // Load or create state
            let state = forceReset ? null : this.loadState();
            const isResuming = state !== null;
            if (!state) {
                state = this.createFreshState();
                if (forceReset) {
                    console.log("🔄 Force reset: Starting fresh collection session");
                }
                else {
                    console.log("🆕 Starting fresh collection session");
                }
            }
            else {
                const sessionAge = Math.round((Date.now() - state.sessionStats.startTime) / 1000 / 60); // minutes
                console.log(`📂 Resuming from saved progress: batch ${state.currentBatchIndex + 1}/${state.totalBatches} (saved ${sessionAge}m ago)`);
                console.log(`⏭️ Already processed: ${state.processedEntityIndices.size} instances in ${state.completedBatches.length} completed batches`);
                // Restore counters from saved state
                this.buyThroughFailureCount = state.buyThroughStats.failureCount;
                this.buyThroughSuccessCount = state.buyThroughStats.successCount;
                this.attemptInstantCompletion = state.buyThroughStats.enabled;
                // Restore ruin selling stats if available
                if (state.ruinSellStats) {
                    this.ruinSellFailureCount = state.ruinSellStats.failureCount;
                    this.ruinSellSuccessCount = state.ruinSellStats.successCount;
                    this.attemptRuinSelling = state.ruinSellStats.enabled;
                }
            }
            // Show hook status
            if (getCollectTimeHookInstalled) {
                console.log("⚡ GetCollectTime hook is active - collections will complete instantly");
            }
            else {
                console.log("⚠️ GetCollectTime hook not available - falling back to DoJobBuyThrough method");
            }
            try {
                const EntityController = Il2Cpp.domain.assembly("Assembly-CSharp").image.class("EntityController");
                const entityInstances = Il2Cpp.gc.choose(EntityController);
                console.log(`📊 Found ${entityInstances.length} EntityController instances`);
                const totalBatches = Math.ceil(entityInstances.length / this.batchSize);
                state.totalBatches = totalBatches;
                if (isResuming) {
                    const remaining = totalBatches - state.completedBatches.length;
                    console.log(`📦 Resuming: ${remaining} batches remaining of ${totalBatches} total`);
                    console.log(`⏭️ Skipping ${state.processedEntityIndices.size} already processed instances`);
                }
                else {
                    console.log(`📦 Processing in ${totalBatches} batches of ${this.batchSize} instances each\n`);
                }
                let totalCollectionsStarted = state.sessionStats.totalCollectionsStarted;
                let totalInstantCompletions = state.sessionStats.totalInstantCompletions;
                for (let batchIndex = state.currentBatchIndex; batchIndex < totalBatches; batchIndex++) {
                    const startIndex = batchIndex * this.batchSize;
                    const batchNumber = batchIndex + 1;
                    // Skip if batch already completed
                    if (state.completedBatches.includes(batchNumber)) {
                        console.log(`⏭️ Skipping already completed batch ${batchNumber}/${totalBatches}`);
                        continue;
                    }
                    console.log(`\n=== BATCH ${batchNumber}/${totalBatches} ===`);
                    // Phase 1: Validate batch (skip already processed instances)
                    const validGemsInstances = await this.validateBatchWithState(entityInstances, startIndex, this.batchSize, state);
                    console.log(`✅ Found ${validGemsInstances.length} valid GEMS instances in batch ${batchNumber}`);
                    // Phase 2: Collect batch
                    if (validGemsInstances.length > 0) {
                        const batchResults = await this.collectBatch(validGemsInstances, batchNumber);
                        totalCollectionsStarted += batchResults.collectionsStarted;
                        totalInstantCompletions += batchResults.instantCompletions;
                        // Update state with processed instances
                        validGemsInstances.forEach(instance => {
                            state.processedEntityIndices.add(instance.entityIndex);
                        });
                    }
                    // Mark batch as completed and update state
                    state.completedBatches.push(batchNumber);
                    state.currentBatchIndex = batchIndex;
                    state.sessionStats.totalCollectionsStarted = totalCollectionsStarted;
                    state.sessionStats.totalInstantCompletions = totalInstantCompletions;
                    state.sessionStats.totalRuinsProcessed = this.ruinSellSuccessCount;
                    state.buyThroughStats.successCount = this.buyThroughSuccessCount;
                    state.buyThroughStats.failureCount = this.buyThroughFailureCount;
                    state.buyThroughStats.enabled = this.attemptInstantCompletion;
                    state.ruinSellStats.successCount = this.ruinSellSuccessCount;
                    state.ruinSellStats.failureCount = this.ruinSellFailureCount;
                    state.ruinSellStats.enabled = this.attemptRuinSelling;
                    state.hookStats.callCount = getCollectTimeHookCallCount;
                    state.cleanupHookStats.callCount = cleanupHookCallCount;
                    state.cleanupHookStats.autoEnabledCount = cleanupAutoEnabledCount;
                    // Save progress
                    this.saveState(state);
                    // Show current instant completion status
                    if (this.buyThroughFailureCount > 0) {
                        console.log(`⚠️ Instant completion status: ${this.buyThroughSuccessCount} successes, ${this.buyThroughFailureCount} failures`);
                    }
                    // Wait before processing next batch (except for the last batch)
                    if (batchIndex < totalBatches - 1) {
                        console.log(`⏳ Waiting ${this.batchDelay / 1000}s before next batch...`);
                        await new Promise(resolve => setTimeout(resolve, this.batchDelay));
                    }
                }
                console.log(`\n🎉 Batch collection completed!`);
                console.log(`📊 Collections started: ${totalCollectionsStarted}`);
                console.log(`💎 Instant completions: ${totalInstantCompletions}`);
                console.log(`🗑️ Ruins processed: ${this.ruinSellSuccessCount}`);
                // Show completion method used
                if (getCollectTimeHookInstalled) {
                    console.log(`⚡ Completion method: GetCollectTime hook (${getCollectTimeHookCallCount} hook calls)`);
                }
                else if (totalInstantCompletions > 0) {
                    console.log(`💎 Completion method: DoJobBuyThrough`);
                }
                else if (totalCollectionsStarted > 0) {
                    console.log(`⏳ Completion method: Natural completion over time`);
                }
                if (totalInstantCompletions === 0 && totalCollectionsStarted > 0 && !getCollectTimeHookInstalled) {
                    console.log(`ℹ️ Note: Collections were started but instant completion was not available`);
                    console.log(`ℹ️ Collections will complete naturally over time`);
                }
                // Final instant completion statistics (only for DoJobBuyThrough)
                if (!getCollectTimeHookInstalled && (this.buyThroughSuccessCount > 0 || this.buyThroughFailureCount > 0)) {
                    const successRate = Math.round((this.buyThroughSuccessCount / (this.buyThroughSuccessCount + this.buyThroughFailureCount)) * 100);
                    console.log(`📈 DoJobBuyThrough success rate: ${successRate}% (${this.buyThroughSuccessCount}/${this.buyThroughSuccessCount + this.buyThroughFailureCount})`);
                }
                // Clear completed state
                this.clearState();
                console.log("✨ Session completed - progress cleared");
            }
            catch (error) {
                console.log(`❌ Batch collection error: ${error}`);
            }
        }
        // Configuration methods
        setInstantCompletionEnabled(enabled) {
            this.attemptInstantCompletion = enabled;
            console.log(`💎 Instant completion ${enabled ? 'enabled' : 'disabled'}`);
        }
        getInstantCompletionStats() {
            return {
                enabled: this.attemptInstantCompletion,
                successes: this.buyThroughSuccessCount,
                failures: this.buyThroughFailureCount
            };
        }
        // Ruin selling configuration methods
        setRuinSellingEnabled(enabled) {
            this.attemptRuinSelling = enabled;
            console.log(`🗑️ Ruin selling ${enabled ? 'enabled' : 'disabled'}`);
        }
        getRuinSellingStats() {
            return {
                enabled: this.attemptRuinSelling,
                successes: this.ruinSellSuccessCount,
                failures: this.ruinSellFailureCount
            };
        }
        // Hook status methods
        getHookStatus() {
            return {
                installed: getCollectTimeHookInstalled,
                callCount: getCollectTimeHookCallCount
            };
        }
        getCleanupHookStatus() {
            return {
                installed: cleanupHookInstalled,
                callCount: cleanupHookCallCount,
                autoEnabledCount: cleanupAutoEnabledCount
            };
        }
        reinstallHook() {
            console.log("🔄 Attempting to reinstall GetCollectTime hook...");
            installGetCollectTimeHook();
        }
        reinstallCleanupHook() {
            console.log("🔄 Attempting to reinstall GoodyHutHelperConfig cleanup hook...");
            installCleanupHook();
        }
        // State persistence methods with enhanced file-based storage
        saveState(state) {
            // Initialize storage paths if not done yet
            if (this.alternativeStatePaths.length === 0) {
                this.initializeStoragePaths();
            }
            const stateData = {
                ...state,
                version: this.stateVersion,
                processedEntityIndices: Array.from(state.processedEntityIndices)
            };
            const stateJson = JSON.stringify(stateData, null, 2);
            // Try to find a writable location and save the state
            let saveSuccess = false;
            let lastError = null;
            try {
                const workingPath = this.findWritableStoragePath();
                const fs = require('fs');
                // Attempt to save to the working path
                fs.writeFileSync(workingPath, stateJson, 'utf8');
                // Verify the file was written correctly
                const verifyData = fs.readFileSync(workingPath, 'utf8');
                const parsed = JSON.parse(verifyData);
                if (parsed.version === this.stateVersion) {
                    console.log(`💾 State saved to ${workingPath}: ${state.processedEntityIndices.size} processed instances, batch ${state.currentBatchIndex + 1}/${state.totalBatches}`);
                    saveSuccess = true;
                }
                else {
                    throw new Error("State verification failed - data corruption detected");
                }
            }
            catch (error) {
                lastError = error;
                console.log(`❌ Failed to save state to file: ${error.message || error}`);
                // Provide specific guidance based on error type
                if (error.message && error.message.includes('EACCES')) {
                    console.log(`🔧 Permission Error: The file system is read-only or lacks write permissions`);
                    console.log(`💡 Try running the script with elevated permissions or check folder permissions`);
                }
                else if (error.message && error.message.includes('ENOSPC')) {
                    console.log(`💽 Disk Space Error: Insufficient disk space to save state file`);
                }
                else if (error.message && error.message.includes('ENOENT')) {
                    console.log(`📁 Path Error: Directory does not exist or is inaccessible`);
                }
            }
            // Fallback to memory storage if file save failed
            if (!saveSuccess) {
                try {
                    globalThis.persistentState = stateJson;
                    console.log(`💾 Fallback: State saved to memory (will be lost on restart)`);
                    console.log(`⚠️ File persistence is not working - progress will not survive script restarts`);
                    console.log(`🔧 To fix: Ensure write permissions to workspace directory or run with elevated privileges`);
                }
                catch (fallbackError) {
                    console.log(`❌ Critical: Both file and memory storage failed!`);
                    console.log(`❌ File error: ${lastError}`);
                    console.log(`❌ Memory error: ${fallbackError}`);
                }
            }
        }
        loadState() {
            // Initialize storage paths if not done yet
            if (this.alternativeStatePaths.length === 0) {
                this.initializeStoragePaths();
            }
            try {
                let stateJson = null;
                // Try to load from file locations in order of preference
                const fs = require('fs');
                for (const testPath of this.alternativeStatePaths) {
                    try {
                        if (fs.existsSync(testPath)) {
                            stateJson = fs.readFileSync(testPath, 'utf8');
                            console.log(`📂 State loaded from: ${testPath}`);
                            break;
                        }
                    }
                    catch (fileError) {
                        // Try next path
                        continue;
                    }
                }
                // If no file found, try memory fallback
                if (!stateJson) {
                    stateJson = globalThis.persistentState;
                    if (stateJson) {
                        console.log(`📂 Loading state from memory fallback (no file found)`);
                    }
                }
                if (!stateJson) {
                    return null;
                }
                const rawState = JSON.parse(stateJson);
                // Validate state version
                if (rawState.version && rawState.version !== this.stateVersion) {
                    console.log(`⚠️ State version mismatch (saved: ${rawState.version}, current: ${this.stateVersion}), starting fresh`);
                    this.clearState();
                    return null;
                }
                // Check if state has expired
                const now = Date.now();
                const stateAge = (now - rawState.timestamp) / (1000 * 60 * 60); // hours
                if (stateAge > this.stateExpirationHours) {
                    console.log(`⏰ Saved state expired (${Math.round(stateAge)}h old), starting fresh`);
                    this.clearState();
                    return null;
                }
                // Validate required fields
                if (!rawState.processedEntityIndices || !Array.isArray(rawState.processedEntityIndices)) {
                    console.log(`❌ Invalid state format, starting fresh`);
                    this.clearState();
                    return null;
                }
                // Reconstruct Set from array
                const state = {
                    ...rawState,
                    processedEntityIndices: new Set(rawState.processedEntityIndices)
                };
                // Validate state consistency
                if (!this.validateStateConsistency(state)) {
                    console.log(`❌ State consistency check failed, starting fresh`);
                    this.clearState();
                    return null;
                }
                console.log(`📂 State loaded: ${state.processedEntityIndices.size} processed instances, batch ${state.currentBatchIndex + 1}/${state.totalBatches}`);
                return state;
            }
            catch (error) {
                console.log(`❌ Failed to load state: ${error}`);
                console.log(`🔄 Starting with fresh state`);
                this.clearState();
                return null;
            }
        }
        clearState() {
            try {
                let clearedFiles = 0;
                // Clear file-based state from all possible locations
                const fs = require('fs');
                for (const testPath of this.alternativeStatePaths) {
                    try {
                        if (fs.existsSync(testPath)) {
                            fs.unlinkSync(testPath);
                            clearedFiles++;
                            console.log(`🗑️ Cleared state file: ${testPath}`);
                        }
                    }
                    catch (fileError) {
                        // File might not exist or be inaccessible, continue with others
                        console.log(`⚠️ Could not clear ${testPath}: ${fileError.message || fileError}`);
                    }
                }
                // Clear memory fallback
                delete globalThis.persistentState;
                // Reset working path cache
                this.currentWorkingPath = "";
                if (clearedFiles > 0) {
                    console.log(`🗑️ Saved state cleared (${clearedFiles} files removed)`);
                }
                else {
                    console.log("🗑️ Saved state cleared (no files found)");
                }
            }
            catch (error) {
                console.log(`❌ Failed to clear state: ${error}`);
            }
        }
        createFreshState() {
            return {
                version: this.stateVersion,
                timestamp: Date.now(),
                processedEntityIndices: new Set(),
                completedBatches: [],
                totalBatches: 0,
                currentBatchIndex: 0,
                hookStats: {
                    installed: getCollectTimeHookInstalled,
                    callCount: getCollectTimeHookCallCount
                },
                cleanupHookStats: {
                    installed: cleanupHookInstalled,
                    callCount: cleanupHookCallCount,
                    autoEnabledCount: cleanupAutoEnabledCount
                },
                buyThroughStats: {
                    successCount: this.buyThroughSuccessCount,
                    failureCount: this.buyThroughFailureCount,
                    enabled: this.attemptInstantCompletion
                },
                ruinSellStats: {
                    successCount: this.ruinSellSuccessCount,
                    failureCount: this.ruinSellFailureCount,
                    enabled: this.attemptRuinSelling
                },
                sessionStats: {
                    totalCollectionsStarted: 0,
                    totalInstantCompletions: 0,
                    totalRuinsProcessed: 0,
                    startTime: Date.now()
                }
            };
        }
        // Validate state consistency and handle edge cases
        validateStateConsistency(state) {
            try {
                // Check basic structure
                if (!state.processedEntityIndices || !state.completedBatches || !state.sessionStats) {
                    console.log("❌ State validation failed: Missing required fields");
                    return false;
                }
                // Check for reasonable values
                if (state.currentBatchIndex < 0 || state.totalBatches < 0) {
                    console.log("❌ State validation failed: Invalid batch indices");
                    return false;
                }
                if (state.completedBatches.length > state.totalBatches) {
                    console.log("❌ State validation failed: More completed batches than total");
                    return false;
                }
                // Check timestamp is reasonable (not in future, not too old)
                const now = Date.now();
                if (state.timestamp > now || state.sessionStats.startTime > now) {
                    console.log("❌ State validation failed: Future timestamps detected");
                    return false;
                }
                // Validate completed batches are within reasonable range
                for (const batchNum of state.completedBatches) {
                    if (batchNum < 1 || batchNum > state.totalBatches) {
                        console.log(`❌ State validation failed: Invalid batch number ${batchNum}`);
                        return false;
                    }
                }
                return true;
            }
            catch (error) {
                console.log(`❌ State validation error: ${error}`);
                return false;
            }
        }
        // Public state management methods
        getProgressStatus() {
            const state = this.loadState();
            if (!state) {
                return { hasProgress: false, summary: "No saved progress found" };
            }
            const processed = state.processedEntityIndices.size;
            const completedBatches = state.completedBatches.length;
            const totalBatches = state.totalBatches;
            const sessionTime = Math.round((Date.now() - state.sessionStats.startTime) / 1000 / 60); // minutes
            const remaining = totalBatches - completedBatches;
            const summary = `Progress: ${processed} instances processed, ${completedBatches}/${totalBatches} batches completed (${remaining} remaining, saved ${sessionTime}m ago)`;
            return {
                hasProgress: true,
                summary,
                details: {
                    processedInstances: processed,
                    completedBatches,
                    totalBatches,
                    remainingBatches: remaining,
                    sessionAgeMinutes: sessionTime,
                    collectionsStarted: state.sessionStats.totalCollectionsStarted,
                    instantCompletions: state.sessionStats.totalInstantCompletions
                }
            };
        }
        resetProgress() {
            this.clearState();
            console.log("🔄 All progress reset - next collection will start fresh");
        }
        showProgressDetails() {
            const state = this.loadState();
            if (!state) {
                console.log("📊 No saved progress found");
                console.log("💡 Use goodyManager.batchCollection() to start a new collection session");
                return;
            }
            const sessionAge = Math.round((Date.now() - state.sessionStats.startTime) / 1000 / 60);
            const remaining = state.totalBatches - state.completedBatches.length;
            const progressPercent = Math.round((state.completedBatches.length / state.totalBatches) * 100);
            console.log("=== SAVED PROGRESS DETAILS ===");
            console.log(`📅 Session started: ${new Date(state.sessionStats.startTime).toLocaleString()} (${sessionAge}m ago)`);
            console.log(`📦 Batches: ${state.completedBatches.length}/${state.totalBatches} completed (${progressPercent}% done)`);
            console.log(`⏳ Remaining: ${remaining} batches to process`);
            console.log(`🎯 Instances: ${state.processedEntityIndices.size} processed`);
            console.log(`📊 Collections: ${state.sessionStats.totalCollectionsStarted} started, ${state.sessionStats.totalInstantCompletions} instant`);
            console.log(`🗑️ Ruins processed: ${state.sessionStats.totalRuinsProcessed || 0}`);
            console.log(`⚡ Hook: ${state.hookStats.installed ? 'Active' : 'Inactive'} (${state.hookStats.callCount} calls)`);
            console.log(`💎 DoJobBuyThrough: ${state.buyThroughStats.successCount} success, ${state.buyThroughStats.failureCount} failures`);
            if (state.ruinSellStats) {
                console.log(`🗑️ Ruin Selling: ${state.ruinSellStats.successCount} success, ${state.ruinSellStats.failureCount} failures`);
            }
            if (state.cleanupHookStats) {
                console.log(`🧹 Cleanup Hook: ${state.cleanupHookStats.installed ? 'Active' : 'Inactive'} (${state.cleanupHookStats.callCount} calls, ${state.cleanupHookStats.autoEnabledCount} auto-enabled)`);
            }
            if (state.completedBatches.length > 0) {
                console.log(`✅ Completed batches: ${state.completedBatches.join(', ')}`);
            }
            console.log("\n💡 Use goodyManager.resume() to continue from where you left off");
            console.log("💡 Use goodyManager.reset() to clear progress and start fresh");
        }
        // Enhanced resume functionality with better user feedback
        async resumeCollection() {
            const progressStatus = this.getProgressStatus();
            if (!progressStatus.hasProgress) {
                console.log("📊 No saved progress found - starting fresh collection");
                return this.processBatchCollection(false);
            }
            console.log("🔄 Resuming collection from saved progress...");
            console.log(`📊 ${progressStatus.summary}`);
            const details = progressStatus.details;
            if (details && details.remainingBatches > 0) {
                console.log(`⏳ Estimated remaining work: ${details.remainingBatches} batches`);
            }
            return this.processBatchCollection(false);
        }
        // Enhanced reset functionality with confirmation
        async resetAndStartFresh() {
            const progressStatus = this.getProgressStatus();
            if (progressStatus.hasProgress) {
                console.log("⚠️ Clearing existing progress and starting fresh...");
                console.log(`📊 ${progressStatus.summary}`);
                console.log("🗑️ This progress will be lost");
            }
            return this.processBatchCollection(true);
        }
    }
    // Create global instance manager
    const instanceManager = new GoodyHutInstanceManager();
    // Make it available globally
    globalThis.goodyManager = {
        scan: () => instanceManager.scanAndValidateInstances(),
        showValid: () => instanceManager.showValidInstances(),
        getBest: () => instanceManager.getBestCollectibleInstance(),
        startCollection: () => {
            const best = instanceManager.getBestCollectibleInstance();
            if (best) {
                return instanceManager.executeStartCollect(best);
            }
            else {
                console.log("❌ No collectible instances available");
                return false;
            }
        },
        batchCollection: () => {
            console.log("🔄 Starting batch collection (will resume from saved progress if available)...");
            return instanceManager.processBatchCollection();
        },
        resume: () => {
            console.log("📂 Resuming collection from saved progress...");
            return instanceManager.resumeCollection();
        },
        reset: () => {
            console.log("🔄 Resetting progress and starting fresh...");
            return instanceManager.resetAndStartFresh();
        },
        enableInstantCompletion: () => instanceManager.setInstantCompletionEnabled(true),
        disableInstantCompletion: () => instanceManager.setInstantCompletionEnabled(false),
        getStats: () => instanceManager.getInstantCompletionStats(),
        enableRuinSelling: () => instanceManager.setRuinSellingEnabled(true),
        disableRuinSelling: () => instanceManager.setRuinSellingEnabled(false),
        getRuinStats: () => instanceManager.getRuinSellingStats(),
        getHookStatus: () => instanceManager.getHookStatus(),
        reinstallHook: () => instanceManager.reinstallHook(),
        getCleanupHookStatus: () => instanceManager.getCleanupHookStatus(),
        reinstallCleanupHook: () => instanceManager.reinstallCleanupHook(),
        getProgress: () => {
            const status = instanceManager.getProgressStatus();
            console.log(status.hasProgress ? `📊 ${status.summary}` : "📊 No saved progress found");
            return status;
        },
        showProgress: () => instanceManager.showProgressDetails(),
        resetProgress: () => instanceManager.resetProgress(),
        help: () => {
            console.log("=== GoodyHut Instance Manager Commands ===");
            console.log("goodyManager.scan() - Scan and validate all instances");
            console.log("goodyManager.showValid() - Show details of valid instances");
            console.log("goodyManager.getBest() - Get best collectible instance (GEMS only)");
            console.log("goodyManager.startCollection() - Start collection on best GEMS instance");
            console.log("goodyManager.batchCollection() - Process all GEMS instances in batches (auto-resume)");
            console.log("");
            console.log("=== Enhanced Progress Management ===");
            console.log("goodyManager.resume() - Resume from saved progress with detailed status");
            console.log("goodyManager.reset() - Clear ALL progress and start completely fresh");
            console.log("goodyManager.getProgress() - Check current progress status");
            console.log("goodyManager.showProgress() - Show detailed progress with recommendations");
            console.log("goodyManager.resetProgress() - Clear saved progress without starting collection");
            console.log("");
            console.log("=== Instant Completion Controls ===");
            console.log("goodyManager.enableInstantCompletion() - Enable DoJobBuyThrough attempts");
            console.log("goodyManager.disableInstantCompletion() - Disable DoJobBuyThrough attempts");
            console.log("goodyManager.getStats() - Show instant completion statistics");
            console.log("");
            console.log("=== Ruin Selling Controls ===");
            console.log("goodyManager.enableRuinSelling() - Enable automatic ruin selling for completed instances");
            console.log("goodyManager.disableRuinSelling() - Disable automatic ruin selling");
            console.log("goodyManager.getRuinStats() - Show ruin selling statistics");
            console.log("");
            console.log("=== Hook Management ===");
            console.log("goodyManager.getHookStatus() - Check GetCollectTime hook status");
            console.log("goodyManager.reinstallHook() - Reinstall GetCollectTime hook if needed");
            console.log("goodyManager.getCleanupHookStatus() - Check GoodyHutHelperConfig cleanup hook status");
            console.log("goodyManager.reinstallCleanupHook() - Reinstall cleanup hook if needed");
            console.log("");
            console.log("=== Key Features ===");
            console.log("💎 GEMS ONLY: Only instances with GEMS rewards will be collected");
            console.log("📦 BATCH MODE: Collections will start even if instant completion fails");
            console.log("⚡ HOOK MODE: GetCollectTime hook provides instant completion without premium currency");
            console.log("🧹 CLEANUP HOOK: Automatically enables cleanup flag for non-collectible instances");
            console.log("�️ RUIN SELLING: Automatically sells ruins from completed instances to clear them");
            console.log("�💾 PERSISTENT STATE: Progress saved to goodyHutState.json, survives script restarts");
            console.log("🔄 SMART RESUME: Automatically skips already processed instances");
            console.log("⏰ AUTO-EXPIRE: Saved progress expires after 24 hours");
            console.log("🛡️ ERROR RECOVERY: Handles corrupted state files gracefully");
            console.log("");
            console.log("=== Quick Start ===");
            console.log("1. goodyManager.batchCollection() - Start/resume collection");
            console.log("2. goodyManager.showProgress() - Check current status");
            console.log("3. goodyManager.reset() - Force fresh start if needed");
        }
    };
    console.log("🔧 Robust GoodyHutHelper Instance Manager loaded!");
    console.log(`⚡ GetCollectTime hook status: ${getCollectTimeHookInstalled ? 'ACTIVE' : 'FAILED'}`);
    console.log(`🧹 Cleanup hook status: ${cleanupHookInstalled ? 'ACTIVE' : 'FAILED'}`);
    // Show startup progress status
    const startupProgress = instanceManager.getProgressStatus();
    if (startupProgress.hasProgress) {
        console.log(`📊 ${startupProgress.summary}`);
        console.log("💡 Use goodyManager.resume() to continue or goodyManager.reset() to start fresh");
    }
    else {
        console.log("📊 No saved progress found");
        console.log("💡 Use goodyManager.batchCollection() to start collecting GEMS");
    }
    console.log("Use goodyManager.help() for all available commands");
});
