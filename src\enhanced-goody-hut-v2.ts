import "frida-il2cpp-bridge";

Il2Cpp.perform(function(){
	console.log("[+] Enhanced GoodyHutHelper Auto-Complete Script v2 Starting...");
	
	// Configuration
	const CONFIG = {
		AUTO_COMPLETE_ENABLED: true,
		SCAN_INTERVAL: 2000,
		COMPLETION_DELAY: 100,
		MAX_RETRIES: 3,
		VERBOSE_LOGGING: true,
		INITIALIZATION_DELAY: 3000
	};
	
	// Statistics tracking
	const stats = {
		totalAttempts: 0,
		successfulCompletions: 0,
		failedCompletions: 0,
		startTime: Date.now()
	};
	
	// Global variables
	let AssemblyCSharp: Il2Cpp.Image;
	let GoodyHutHelper: Il2Cpp.Class;
	let isInitialized = false;
	
	// Enhanced logging function
	function log(level: string, message: string): void {
		const timestamp = new Date().toLocaleTimeString();
		console.log(`[${timestamp}] ${level} ${message}`);
	}
	
	// Function to safely load assembly
	function loadAssembly(): boolean {
		try {
			log("INFO", "Attempting to load Assembly-CSharp...");
			AssemblyCSharp = Il2Cpp.domain.assembly("Assembly-CSharp").image;
			log("SUCCESS", "Assembly-CSharp loaded successfully");
			return true;
		} catch (error) {
			log("ERROR", `Failed to load Assembly-CSharp: ${error}`);
			
			// Try to list available assemblies
			try {
				log("INFO", "Available assemblies:");
				Il2Cpp.domain.assemblies.forEach(assembly => {
					log("INFO", `  - ${assembly.name}`);
				});
			} catch (e) {
				log("ERROR", `Failed to list assemblies: ${e}`);
			}
			return false;
		}
	}
	
	// Function to find GoodyHutHelper class
	function findGoodyHutClass(): boolean {
		const possibleNames = ["GoodyHutHelper", "GoodyHut", "GoodieHutHelper", "TreasureHutHelper"];
		
		for (const className of possibleNames) {
			try {
				log("INFO", `Trying class name: ${className}`);
				GoodyHutHelper = AssemblyCSharp.class(className);
				log("SUCCESS", `Found class: ${className} at ${GoodyHutHelper.handle}`);
				return true;
			} catch (error) {
				log("DEBUG", `Class ${className} not found: ${error}`);
			}
		}
		
		// If not found, try to search for classes containing "Goody" or "Hut"
		log("INFO", "Searching for classes containing 'Goody' or 'Hut'...");
		try {
			const foundClasses: string[] = [];
			// Use Il2Cpp.domain.assemblies to iterate through classes
			for (const assembly of Il2Cpp.domain.assemblies) {
				if (assembly.name === "Assembly-CSharp") {
					try {
						// Try to enumerate classes in the assembly
						log("DEBUG", `Searching in assembly: ${assembly.name}`);
						// Note: Direct class enumeration might not be available
						// This is a limitation we'll work around
					} catch (e) {
						log("DEBUG", `Cannot enumerate classes in ${assembly.name}: ${e}`);
					}
				}
			}
			
			if (foundClasses.length > 0) {
				log("INFO", `Found ${foundClasses.length} potential classes: ${foundClasses.join(", ")}`);
				// Try the first one
				try {
					GoodyHutHelper = AssemblyCSharp.class(foundClasses[0]);
					log("SUCCESS", `Using class: ${foundClasses[0]}`);
					return true;
				} catch (error) {
					log("ERROR", `Failed to load found class ${foundClasses[0]}: ${error}`);
				}
			}
		} catch (error) {
			log("ERROR", `Error searching for classes: ${error}`);
		}
		
		log("ERROR", "No GoodyHutHelper class found");
		return false;
	}
	
	// Function to validate required methods
	function validateMethods(): boolean {
		const requiredMethods = ["StartCollect", "DoJobBuyThrough", "IsJobComplete", "CanCollect", "GetJobTimeLeft"];
		const foundMethods: string[] = [];
		const missingMethods: string[] = [];
		
		requiredMethods.forEach(methodName => {
			try {
				const method = GoodyHutHelper.method(methodName);
				if (method) {
					foundMethods.push(methodName);
				} else {
					missingMethods.push(methodName);
				}
			} catch (error) {
				missingMethods.push(methodName);
			}
		});
		
		log("INFO", `Found methods: ${foundMethods.join(", ")}`);
		if (missingMethods.length > 0) {
			log("WARN", `Missing methods: ${missingMethods.join(", ")}`);
		}
		
		// List all available methods for debugging
		if (CONFIG.VERBOSE_LOGGING) {
			log("DEBUG", "All available methods:");
			try {
				GoodyHutHelper.methods.forEach(method => {
					log("DEBUG", `  - ${method.name}`);
				});
			} catch (error) {
				log("ERROR", `Failed to list methods: ${error}`);
			}
		}
		
		return foundMethods.length >= 3; // Need at least 3 core methods
	}
	
	// Safe method call with error handling
	function safeMethodCall(instance: Il2Cpp.Object, methodName: string, ...args: any[]): any {
		try {
			const method = instance.method(methodName);
			if (method) {
				const result = method.invoke(...args);
				if (CONFIG.VERBOSE_LOGGING) {
					log("DEBUG", `${methodName}() returned: ${result}`);
				}
				return result;
			} else {
				log("WARN", `Method ${methodName} not found on instance`);
				return null;
			}
		} catch (error) {
			log("ERROR", `Error calling ${methodName}: ${error}`);
			return null;
		}
	}
	
	// Enhanced state detection with proper logic
	function getGoodyHutState(instance: Il2Cpp.Object): { state: string, isCollecting: boolean, details: string } {
		try {
			const isJobComplete = safeMethodCall(instance, "IsJobComplete");
			const canCollect = safeMethodCall(instance, "CanCollect");
			const timeLeft = safeMethodCall(instance, "GetJobTimeLeft");
			
			let state = "UNKNOWN";
			let isCollecting = false;
			let details = `JobComplete: ${isJobComplete}, CanCollect: ${canCollect}, TimeLeft: ${timeLeft}`;
			
			// Correct state detection logic
			if (isJobComplete === true && canCollect === true) {
				state = "IDLE_READY";
				isCollecting = false;
				details += " - Ready for new collection";
			} else if (isJobComplete === false && canCollect === false) {
				state = "COLLECTING";
				isCollecting = true;
				details += " - Collection in progress";
			} else if (isJobComplete === true && canCollect === false) {
				state = "COMPLETED_AWAITING";
				isCollecting = false;
				details += " - Completed, awaiting reward collection";
			} else if (isJobComplete === false && canCollect === true) {
				state = "INCONSISTENT";
				isCollecting = false;
				details += " - Inconsistent state";
			}
			
			// Validate with timer
			if (timeLeft !== null && typeof timeLeft === 'number') {
				if (timeLeft > 0) {
					if (state !== "COLLECTING") {
						details += ` - WARNING: Positive timer (${timeLeft}s) but state is ${state}`;
						isCollecting = true;
						state = "COLLECTING_BY_TIMER";
					}
				} else if (timeLeft < -1) {
					details += ` - Completed ${Math.abs(timeLeft)}s ago`;
				}
			}
			
			return { state, isCollecting, details };
		} catch (error) {
			log("ERROR", `Error checking state: ${error}`);
			return { state: "ERROR", isCollecting: false, details: `Error: ${error}` };
		}
	}
	
	// Test instance detection
	function testInstanceDetection(): void {
		try {
			log("INFO", "Testing instance detection...");
			const instances = Il2Cpp.gc.choose(GoodyHutHelper);
			log("INFO", `Found ${instances.length} GoodyHutHelper instances`);
			
			if (instances.length > 0) {
				log("INFO", "Testing first instance methods...");
				const instance = instances[0];
				const stateInfo = getGoodyHutState(instance);
				log("INFO", `Instance 0 state: ${stateInfo.state} - ${stateInfo.details}`);
			}
		} catch (error) {
			log("ERROR", `Error in instance detection test: ${error}`);
		}
	}
	
	// Auto-completion function
	function autoCompleteCollection(instance: Il2Cpp.Object): boolean {
		try {
			stats.totalAttempts++;
			log("INFO", "Attempting auto-completion...");
			
			// Check if buy-through is available
			const canBuyThrough = safeMethodCall(instance, "CanBuyThrough");
			if (!canBuyThrough) {
				log("WARN", "Cannot use buy-through - may require premium currency");
				stats.failedCompletions++;
				return false;
			}
			
			// Call DoJobBuyThrough
			const result = safeMethodCall(instance, "DoJobBuyThrough");
			if (result !== null) {
				log("SUCCESS", "Auto-completion successful!");
				stats.successfulCompletions++;
				return true;
			} else {
				log("WARN", "DoJobBuyThrough call failed");
				stats.failedCompletions++;
				return false;
			}
		} catch (error) {
			log("ERROR", `Error in auto-completion: ${error}`);
			stats.failedCompletions++;
			return false;
		}
	}
	
	// Main initialization function
	function initialize(): void {
		log("INFO", "Starting initialization...");
		
		// Step 1: Load assembly
		if (!loadAssembly()) {
			log("ERROR", "Failed to load assembly, aborting");
			return;
		}
		
		// Step 2: Find GoodyHutHelper class
		if (!findGoodyHutClass()) {
			log("ERROR", "Failed to find GoodyHutHelper class, aborting");
			return;
		}
		
		// Step 3: Validate methods
		if (!validateMethods()) {
			log("ERROR", "Required methods not found, aborting");
			return;
		}
		
		// Step 4: Test instance detection
		testInstanceDetection();
		
		// Step 5: Install hook if possible
		try {
			const startCollectMethod = GoodyHutHelper.method("StartCollect");
			if (startCollectMethod) {
				log("INFO", "Installing StartCollect hook...");
				startCollectMethod.implementation = function() {
					log("INFO", "StartCollect called!");
					const thisInstance = this as Il2Cpp.Object;
					const result = thisInstance.method("StartCollect").invoke();
					
					if (CONFIG.AUTO_COMPLETE_ENABLED) {
						setTimeout(() => {
							autoCompleteCollection(thisInstance);
						}, CONFIG.COMPLETION_DELAY);
					}
					
					return result;
				};
				log("SUCCESS", "Hook installed successfully");
			}
		} catch (error) {
			log("WARN", `Failed to install hook: ${error}`);
		}
		
		isInitialized = true;
		log("SUCCESS", "Initialization completed successfully!");
		
		// Make functions available globally
		(globalThis as any).goodyHutTest = {
			testInstance: () => testInstanceDetection(),
			stats: () => {
				const runtime = Math.floor((Date.now() - stats.startTime) / 1000);
				log("STATS", `Runtime: ${runtime}s | Attempts: ${stats.totalAttempts} | Success: ${stats.successfulCompletions} | Failed: ${stats.failedCompletions}`);
			},
			help: () => {
				log("INFO", "Available commands:");
				log("INFO", "goodyHutTest.testInstance() - Test instance detection");
				log("INFO", "goodyHutTest.stats() - Show statistics");
			}
		};
		
		log("INFO", "Use goodyHutTest.help() for available commands");
	}
	
	// Start initialization with delay
	log("INFO", `Waiting ${CONFIG.INITIALIZATION_DELAY}ms for game to load...`);
	setTimeout(() => {
		initialize();
	}, CONFIG.INITIALIZATION_DELAY);
});
