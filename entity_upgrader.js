// Frida script for EntityController auto-upgrade
// Usage: frida -U -f com.your.game.package -l script.js --no-pause

Java.perform(function() {
    console.log("[+] Frida EntityController Auto-Upgrader Started");
    
    // Configuration
    const CONFIG = {
        AUTO_UPGRADE_ENABLED: true,
        DEBUG_MODE: true,
        HOOK_INTERVAL: 1000, // Check every 1 second
        MAX_UPGRADE_ATTEMPTS: 50 // Prevent infinite loops
    };
    
    let entityControllerClass = null;
    let hookedMethods = {};
    let selectedEntities = new Set();
    let upgradeInterval = null;
    
    // Find EntityController class
    function findEntityControllerClass() {
        try {
            // Try different possible class names for IL2CPP
            const possibleNames = [
                "EntityController",
                "Assembly-CSharp.EntityController",
                "Il2CppSystem.EntityController",
                "UnityEngine.EntityController"
            ];
            
            for (let name of possibleNames) {
                try {
                    entityControllerClass = Java.use(name);
                    console.log("[+] Found EntityController class: " + name);
                    return true;
                } catch (e) {
                    // Continue searching
                }
            }
            
            // If not found, try to enumerate all classes
            Java.enumerateLoadedClasses({
                onMatch: function(className) {
                    if (className.includes("EntityController")) {
                        try {
                            entityControllerClass = Java.use(className);
                            console.log("[+] Found EntityController class via enumeration: " + className);
                            return true;
                        } catch (e) {
                            // Continue searching
                        }
                    }
                },
                onComplete: function() {}
            });
            
        } catch (error) {
            console.log("[-] Error finding EntityController class: " + error);
            return false;
        }
        
        return entityControllerClass !== null;
    }
    
    // Hook EntityController methods
    function hookEntityControllerMethods() {
        if (!entityControllerClass) return false;
        
        try {
            // Hook IsSelected method
            if (entityControllerClass.IsSelected) {
                hookedMethods.IsSelected = entityControllerClass.IsSelected;
                entityControllerClass.IsSelected.implementation = function() {
                    const result = this.IsSelected();
                    const instanceId = this.GetInstanceID();
                    
                    if (result) {
                        selectedEntities.add(instanceId);
                        if (CONFIG.DEBUG_MODE) {
                            console.log("[+] Entity selected: " + instanceId);
                        }
                    } else {
                        selectedEntities.delete(instanceId);
                    }
                    
                    return result;
                };
                console.log("[+] Hooked IsSelected method");
            }
            
            // Hook CanUpgrade method
            if (entityControllerClass.CanUpgrade) {
                hookedMethods.CanUpgrade = entityControllerClass.CanUpgrade;
                console.log("[+] Found CanUpgrade method");
            }
            
            // Hook GetLevel method
            if (entityControllerClass.GetLevel) {
                hookedMethods.GetLevel = entityControllerClass.GetLevel;
                console.log("[+] Found GetLevel method");
            }
            
            // Hook GetMaxLevel method
            if (entityControllerClass.GetMaxLevel) {
                hookedMethods.GetMaxLevel = entityControllerClass.GetMaxLevel;
                console.log("[+] Found GetMaxLevel method");
            }
            
            // Hook InstantUpgrade method
            if (entityControllerClass.InstantUpgrade) {
                hookedMethods.InstantUpgrade = entityControllerClass.InstantUpgrade;
                console.log("[+] Found InstantUpgrade method");
            }
            
            return true;
            
        } catch (error) {
            console.log("[-] Error hooking methods: " + error);
            return false;
        }
    }
    
    // Get all EntityController instances
    function getAllEntityInstances() {
        const instances = [];
        
        try {
            // Use Unity's FindObjectsOfType equivalent
            const unityEngine = Java.use("UnityEngine.Object");
            const entityInstances = unityEngine.FindObjectsOfType(entityControllerClass.class);
            
            for (let i = 0; i < entityInstances.length; i++) {
                instances.push(entityInstances[i]);
            }
            
        } catch (error) {
            if (CONFIG.DEBUG_MODE) {
                console.log("[-] Error getting instances: " + error);
            }
        }
        
        return instances;
    }
    
    // Perform auto-upgrade on selected entities
    function performAutoUpgrade() {
        if (!CONFIG.AUTO_UPGRADE_ENABLED) return;
        
        try {
            const allInstances = getAllEntityInstances();
            let upgradedCount = 0;
            
            for (let instance of allInstances) {
                try {
                    const instanceId = instance.GetInstanceID();
                    
                    // Check if entity is selected
                    if (selectedEntities.has(instanceId) || instance.IsSelected()) {
                        
                        // Check if can upgrade
                        if (instance.CanUpgrade()) {
                            const currentLevel = instance.GetLevel();
                            const maxLevel = instance.GetMaxLevel();
                            
                            if (CONFIG.DEBUG_MODE) {
                                console.log(`[+] Entity ${instanceId}: Level ${currentLevel}/${maxLevel}`);
                            }
                            
                            // Upgrade until max level
                            let upgradeCount = 0;
                            while (currentLevel < maxLevel && upgradeCount < CONFIG.MAX_UPGRADE_ATTEMPTS) {
                                instance.InstantUpgrade();
                                
                                const newLevel = instance.GetLevel();
                                if (newLevel > currentLevel) {
                                    upgradedCount++;
                                    upgradeCount++;
                                    
                                    if (CONFIG.DEBUG_MODE) {
                                        console.log(`[+] Upgraded entity ${instanceId} to level ${newLevel}`);
                                    }
                                    
                                    if (newLevel >= maxLevel) {
                                        if (CONFIG.DEBUG_MODE) {
                                            console.log(`[+] Entity ${instanceId} reached max level`);
                                        }
                                        break;
                                    }
                                } else {
                                    // Level didn't increase, break to prevent infinite loop
                                    break;
                                }
                            }
                        }
                    }
                    
                } catch (error) {
                    if (CONFIG.DEBUG_MODE) {
                        console.log("[-] Error processing entity: " + error);
                    }
                }
            }
            
            if (upgradedCount > 0 && CONFIG.DEBUG_MODE) {
                console.log(`[+] Auto-upgrade completed: ${upgradedCount} upgrades performed`);
            }
            
        } catch (error) {
            if (CONFIG.DEBUG_MODE) {
                console.log("[-] Error in performAutoUpgrade: " + error);
            }
        }
    }
    
    // Manual upgrade trigger function
    function manualUpgradeSelected() {
        console.log("[+] Manual upgrade triggered");
        performAutoUpgrade();
    }
    
    // Start the auto-upgrader
    function startAutoUpgrader() {
        if (!findEntityControllerClass()) {
            console.log("[-] Could not find EntityController class");
            return false;
        }
        
        if (!hookEntityControllerMethods()) {
            console.log("[-] Could not hook EntityController methods");
            return false;
        }
        
        // Start periodic upgrade check
        upgradeInterval = setInterval(performAutoUpgrade, CONFIG.HOOK_INTERVAL);
        
        console.log("[+] Auto-upgrader started successfully");
        console.log("[+] Available commands:");
        console.log("    - upgrade(): Manual upgrade selected entities");
        console.log("    - toggle(): Toggle auto-upgrade on/off");
        console.log("    - debug(): Toggle debug mode");
        console.log("    - stop(): Stop auto-upgrader");
        
        return true;
    }
    
    // Stop the auto-upgrader
    function stopAutoUpgrader() {
        if (upgradeInterval) {
            clearInterval(upgradeInterval);
            upgradeInterval = null;
        }
        console.log("[+] Auto-upgrader stopped");
    }
    
    // Toggle auto-upgrade
    function toggleAutoUpgrade() {
        CONFIG.AUTO_UPGRADE_ENABLED = !CONFIG.AUTO_UPGRADE_ENABLED;
        console.log("[+] Auto-upgrade " + (CONFIG.AUTO_UPGRADE_ENABLED ? "enabled" : "disabled"));
    }
    
    // Toggle debug mode
    function toggleDebugMode() {
        CONFIG.DEBUG_MODE = !CONFIG.DEBUG_MODE;
        console.log("[+] Debug mode " + (CONFIG.DEBUG_MODE ? "enabled" : "disabled"));
    }
    
    // Expose functions globally
    global.upgrade = manualUpgradeSelected;
    global.toggle = toggleAutoUpgrade;
    global.debug = toggleDebugMode;
    global.stop = stopAutoUpgrader;
    global.start = startAutoUpgrader;
    
    // Auto-start the upgrader
    setTimeout(function() {
        startAutoUpgrader();
    }, 2000); // Wait 2 seconds for the game to fully load
    
    console.log("[+] Frida EntityController Auto-Upgrader Loaded");
});

// IL2CPP specific hooks (if needed)
if (Java.available) {
    // Unity IL2CPP support
    setTimeout(function() {
        console.log("[+] Attempting IL2CPP specific hooks...");
        
        // Hook into IL2CPP domain if available
        try {
            const il2cpp = Process.getModuleByName("GameAssembly.dll");
            if (il2cpp) {
                console.log("[+] Found IL2CPP GameAssembly.dll");
                
                // You can add specific IL2CPP memory patching here if needed
                // This would require reverse engineering the specific addresses
            }
        } catch (e) {
            console.log("[-] IL2CPP GameAssembly.dll not found or accessible");
        }
        
    }, 3000);
}