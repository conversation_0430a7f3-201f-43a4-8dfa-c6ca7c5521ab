// EntityController Auto-Upgrader using Symbol Enumeration
// Usage: frida -U -l entitycontroller_auto_upgrade.js -f com.nexonm.dominations.adk

console.log("[+] EntityController Auto-Upgrader - Symbol Enumeration Method");

// Global variables
var entityControllerMethods = {};
var processedInstances = new Set();
var isSelectedCallCount = 0;
var lastSelectionTime = 0;
var manualModule = null; // For manual module selection

// Enhanced module detection with fallback options
function findIL2CPPModule() {
    console.log("[+] Searching for IL2CPP module...");

    // Check if manual module is set
    if (manualModule) {
        console.log("[+] Using manually selected module: " + manualModule.name);
        console.log("[+] Module at: " + manualModule.base + ", Size: " + (manualModule.size / 1024 / 1024).toFixed(1) + "MB");
        return manualModule;
    }

    // Try primary module name
    var il2cppModule = Process.findModuleByName("libil2cpp.so");
    if (il2cppModule) {
        console.log("[+] Found libil2cpp.so at: " + il2cppModule.base);
        console.log("[+] Module size: " + (il2cppModule.size / 1024 / 1024).toFixed(1) + "MB");
        return il2cppModule;
    }

    // Try alternative IL2CPP module names
    var alternativeNames = ["libunity.so", "libmain.so", "libgame.so"];
    for (var i = 0; i < alternativeNames.length; i++) {
        var module = Process.findModuleByName(alternativeNames[i]);
        if (module && module.size > 5 * 1024 * 1024) { // > 5MB
            console.log("[+] Found alternative IL2CPP module: " + alternativeNames[i]);
            console.log("[+] Module at: " + module.base + ", Size: " + (module.size / 1024 / 1024).toFixed(1) + "MB");
            return module;
        }
    }

    // List large modules for manual selection
    console.log("[-] IL2CPP module not found. Listing large modules (>5MB):");
    var modules = Process.enumerateModules();
    var largeModules = [];

    modules.forEach(function(module) {
        if (module.size > 5 * 1024 * 1024) {
            largeModules.push(module);
            console.log("  " + module.name + " - " + (module.size / 1024 / 1024).toFixed(1) + "MB at " + module.base);
        }
    });

    if (largeModules.length > 0) {
        console.log("[!] Use rpc.exports.setModule('module_name') to manually select the IL2CPP module");
        return null;
    }

    console.log("[-] No suitable IL2CPP module found");
    return null;
}

// Discover EntityController methods for stripped IL2CPP binaries
function discoverEntityController() {
    console.log("[+] ===== DISCOVERING ENTITYCONTROLLER IN STRIPPED IL2CPP BINARY =====");

    // Find the IL2CPP module with enhanced detection
    var il2cppModule = findIL2CPPModule();
    if (!il2cppModule) {
        return false;
    }

    // Strategy 1: Try RVA-based discovery first (most reliable for known addresses)
    console.log("[+] Strategy 1: RVA-based discovery using known addresses...");
    var rvaSuccess = tryRVADiscovery(il2cppModule);

    // Strategy 2: Export-based discovery for any remaining methods
    if (Object.keys(entityControllerMethods).length < 5) {
        console.log("[+] Strategy 2: Export-based discovery...");
        tryExportDiscovery(il2cppModule);
    }

    // Strategy 3: Pattern-based memory scanning for missing methods
    if (Object.keys(entityControllerMethods).length < 3) { // Need at least IsSelected, CanUpgrade, InstantUpgrade
        console.log("[+] Strategy 3: Pattern-based memory scanning...");
        tryPatternScanning(il2cppModule);
    }

    // Strategy 4: Runtime hook-based discovery
    if (Object.keys(entityControllerMethods).length < 3) {
        console.log("[+] Strategy 4: Setting up runtime discovery hooks...");
        setupRuntimeDiscovery(il2cppModule);
    }

    console.log("[+] ===== DISCOVERY RESULTS =====");
    console.log("[+] Found " + Object.keys(entityControllerMethods).length + " out of 5 target methods");

    Object.keys(entityControllerMethods).forEach(function(key) {
        var method = entityControllerMethods[key];
        console.log("  " + key + ": " + method.name + " at " + method.address);
    });

    return Object.keys(entityControllerMethods).length > 0;
}

// RVA-based discovery using addresses from decompiled C# code
function tryRVADiscovery(il2cppModule) {
    console.log("[+] Attempting RVA-based discovery...");

    // RVA addresses from decompiled C# code
    var rvaAddresses = {
        'IsSelected': 0x1E53050,
        'CanUpgrade': 0x1E4027C,
        'GetMaxUpgradeLevel': 0x1E4A0B8
    };

    // Calculate potential image bases based on module base
    var moduleBase = il2cppModule.base;
    var imageBases = [
        0x0,                    // Direct RVA (no image base)
        0x10000000,            // Standard IL2CPP base
        0x08000000,            // Alternative base
        0x00400000,            // PE standard base
        moduleBase.toInt32(),  // Use actual module base
        0x1000000,             // Another common base
        0x40000000             // High memory base
    ];

    var foundCount = 0;

    imageBases.forEach(function(imageBase) {
        Object.keys(rvaAddresses).forEach(function(methodName) {
            if (entityControllerMethods[methodName]) {
                return; // Already found
            }

            var rva = rvaAddresses[methodName];
            var calculatedAddr;

            if (imageBase === 0) {
                // Direct RVA - add to module base
                calculatedAddr = moduleBase.add(rva);
            } else {
                // Calculate offset from image base
                var offset = rva - imageBase;
                if (offset >= 0 && offset < il2cppModule.size) {
                    calculatedAddr = moduleBase.add(offset);
                } else {
                    return; // Invalid offset
                }
            }

            try {
                // Validate the address points to executable code
                if (isValidExecutableAddress(calculatedAddr)) {
                    entityControllerMethods[methodName] = {
                        address: calculatedAddr,
                        name: methodName + " (RVA)"
                    };
                    console.log("[+] *** FOUND " + methodName + "() via RVA at " + calculatedAddr + " ***");
                    console.log("    RVA: 0x" + rva.toString(16) + ", Image Base: 0x" + imageBase.toString(16));
                    foundCount++;
                }
            } catch (e) {
                // Address not valid, continue
            }
        });
    });

    console.log("[+] RVA discovery found " + foundCount + " methods");
    return foundCount > 0;
}

// Validate if an address points to executable code
function isValidExecutableAddress(address) {
    try {
        var bytes = Memory.readByteArray(address, 16);
        if (!bytes) return false;

        var firstBytes = new Uint8Array(bytes.slice(0, 4));
        var word = (firstBytes[3] << 24) | (firstBytes[2] << 16) | (firstBytes[1] << 8) | firstBytes[0];

        // Check for valid ARM64 instruction patterns
        if ((word & 0xFFC00000) === 0xA9000000) return true; // STP instruction
        if ((word & 0xFF000000) === 0x52000000) return true; // MOVZ instruction
        if ((word & 0x9F000000) === 0x90000000) return true; // ADRP instruction
        if ((word & 0x1F000000) === 0x14000000) return true; // B instruction
        if ((word & 0xFFE0FC00) === 0xAA0003E0) return true; // MOV instruction

        return false;
    } catch (e) {
        return false;
    }
}

// Export-based discovery
function tryExportDiscovery(il2cppModule) {
    console.log("[+] Attempting export-based discovery...");

    var exportPatterns = [
        "EntityController_IsSelected",
        "EntityController_CanUpgrade",
        "EntityController_GetLevel",
        "EntityController_GetMaxUpgradeLevel",
        "EntityController_InstantUpgrade",
        "_ZN16EntityController10IsSelectedEv",      // C++ mangled
        "_ZN16EntityController10CanUpgradeEb",     // C++ mangled
        "_ZN16EntityController8GetLevelEv",        // C++ mangled
        "_ZN16EntityController17GetMaxUpgradeLevelEv", // C++ mangled
        "_ZN16EntityController14InstantUpgradeEv"  // C++ mangled
    ];

    var foundCount = 0;

    exportPatterns.forEach(function(pattern) {
        try {
            var addr = Module.findExportByName(il2cppModule.name, pattern);
            if (addr) {
                var methodName = extractMethodName(pattern);
                if (methodName && !entityControllerMethods[methodName]) {
                    entityControllerMethods[methodName] = {
                        address: addr,
                        name: methodName + " (Export)"
                    };
                    console.log("[+] *** FOUND " + methodName + "() via export at " + addr + " ***");
                    foundCount++;
                }
            }
        } catch (e) {
            // Export not found, continue
        }
    });

    console.log("[+] Export discovery found " + foundCount + " methods");
    return foundCount > 0;
}

// Extract method name from export pattern
function extractMethodName(pattern) {
    if (pattern.includes("IsSelected")) return "IsSelected";
    if (pattern.includes("CanUpgrade")) return "CanUpgrade";
    if (pattern.includes("GetMaxUpgradeLevel")) return "GetMaxUpgradeLevel";
    if (pattern.includes("GetLevel")) return "GetLevel";
    if (pattern.includes("InstantUpgrade")) return "InstantUpgrade";
    return null;
}

// Pattern-based memory scanning for method signatures
function tryPatternScanning(il2cppModule) {
    console.log("[+] Attempting pattern-based memory scanning...");
    console.log("[+] This may take a moment - scanning for ARM64 method patterns...");

    var moduleBase = il2cppModule.base;
    var moduleSize = il2cppModule.size;
    var scanSize = Math.min(moduleSize, 0x2000000); // Limit to 32MB for performance
    var chunkSize = 0x10000; // 64KB chunks
    var foundCount = 0;

    try {
        for (var offset = 0; offset < scanSize; offset += chunkSize) {
            var currentAddr = moduleBase.add(offset);

            try {
                var data = Memory.readByteArray(currentAddr, Math.min(chunkSize, scanSize - offset));
                if (!data) continue;

                var bytes = new Uint8Array(data);

                // Look for ARM64 function prologue patterns
                for (var i = 0; i < bytes.length - 16; i += 4) {
                    // Common ARM64 function start: stp x29, x30, [sp, #-0x10]!
                    if (bytes[i] === 0xFD && bytes[i+1] === 0x7B &&
                        bytes[i+2] === 0xBF && bytes[i+3] === 0xA9) {

                        var potentialFunc = currentAddr.add(i);

                        // Validate this looks like a method
                        if (isValidExecutableAddress(potentialFunc)) {
                            // Store as potential method if we don't have enough methods yet
                            var missingMethods = ['IsSelected', 'CanUpgrade', 'GetLevel', 'GetMaxUpgradeLevel', 'InstantUpgrade']
                                .filter(function(method) { return !entityControllerMethods[method]; });

                            if (missingMethods.length > 0 && foundCount < missingMethods.length) {
                                var methodName = missingMethods[foundCount % missingMethods.length];
                                entityControllerMethods[methodName] = {
                                    address: potentialFunc,
                                    name: methodName + " (Pattern)"
                                };
                                console.log("[+] *** FOUND potential " + methodName + "() via pattern at " + potentialFunc + " ***");
                                foundCount++;
                            }
                        }
                    }
                }
            } catch (e) {
                // Continue scanning
            }

            // Progress indicator every 2MB
            if (offset % 0x200000 === 0 && offset > 0) {
                console.log("[+] Scanned " + ((offset / scanSize) * 100).toFixed(1) + "%...");
            }
        }
    } catch (e) {
        console.log("[-] Pattern scanning failed: " + e);
    }

    console.log("[+] Pattern scanning found " + foundCount + " potential methods");
    return foundCount > 0;
}

// Setup runtime discovery hooks
function setupRuntimeDiscovery(il2cppModule) {
    console.log("[+] Setting up runtime discovery hooks...");
    console.log("[+] Will attempt to discover methods when EntityController objects are created");

    // Hook common Unity object creation methods
    try {
        var mallocAddr = Module.findExportByName(null, "malloc");
        if (mallocAddr) {
            var mallocCallCount = 0;

            Interceptor.attach(mallocAddr, {
                onEnter: function(args) {
                    mallocCallCount++;

                    // Every 5000 malloc calls, retry discovery
                    if (mallocCallCount % 5000 === 0) {
                        setTimeout(function() {
                            console.log("[+] Retrying discovery after memory activity...");
                            var newCount = Object.keys(entityControllerMethods).length;

                            // Try RVA discovery again in case memory layout changed
                            tryRVADiscovery(il2cppModule);

                            if (Object.keys(entityControllerMethods).length > newCount) {
                                console.log("[+] *** Found additional methods via runtime discovery! ***");
                                createMethodWrappers();
                                installHooks();
                            }
                        }, 100);
                    }
                }
            });

            console.log("[+] Runtime discovery hook installed on malloc()");
        }
    } catch (e) {
        console.log("[-] Runtime discovery setup failed: " + e);
    }
}

// Create NativeFunction wrappers for discovered methods
function createMethodWrappers() {
    console.log("[+] Creating NativeFunction wrappers...");

    // Create function wrappers for each discovered method
    if (entityControllerMethods.IsSelected) {
        entityControllerMethods.IsSelected.func = new NativeFunction(
            entityControllerMethods.IsSelected.address, 'int', ['pointer']
        );
    }

    if (entityControllerMethods.CanUpgrade) {
        entityControllerMethods.CanUpgrade.func = new NativeFunction(
            entityControllerMethods.CanUpgrade.address, 'int', ['pointer', 'int']
        );
    }

    if (entityControllerMethods.GetLevel) {
        entityControllerMethods.GetLevel.func = new NativeFunction(
            entityControllerMethods.GetLevel.address, 'int', ['pointer']
        );
    }

    if (entityControllerMethods.GetMaxUpgradeLevel) {
        entityControllerMethods.GetMaxUpgradeLevel.func = new NativeFunction(
            entityControllerMethods.GetMaxUpgradeLevel.address, 'int', ['pointer']
        );
    }

    if (entityControllerMethods.InstantUpgrade) {
        entityControllerMethods.InstantUpgrade.func = new NativeFunction(
            entityControllerMethods.InstantUpgrade.address, 'void', ['pointer']
        );
    }

    console.log("[+] NativeFunction wrappers created successfully");
}

// Install hooks for EntityController methods
function installHooks() {
    console.log("[+] Installing EntityController method hooks...");

    // Install IsSelected hook for selection detection
    if (entityControllerMethods.IsSelected) {
        installIsSelectedHook();
    } else {
        console.log("[!] IsSelected method not found - selection detection disabled");
    }

    console.log("[+] ===== EntityController Auto-Upgrader READY =====");
    console.log("[+] Found methods: " + Object.keys(entityControllerMethods).join(", "));
    console.log("[+] Select an entity in the game to trigger auto-upgrade");
    console.log("=".repeat(60));
}

// Install IsSelected hook to detect entity selection
function installIsSelectedHook() {
    console.log("[+] Installing IsSelected() hook...");

    try {
        var isSelectedMethod = entityControllerMethods.IsSelected;

        Interceptor.attach(isSelectedMethod.address, {
            onEnter: function(args) {
                this.thisPtr = args[0]; // EntityController instance pointer
                isSelectedCallCount++;

                // Log every 100 calls or first 10 calls
                if (isSelectedCallCount % 100 === 0 || isSelectedCallCount <= 10) {
                    console.log("[DEBUG] IsSelected() called #" + isSelectedCallCount + " on instance: " + this.thisPtr);
                }
            },

            onLeave: function(retval) {
                const isSelected = retval.toInt32();
                const instanceId = this.thisPtr.toString();

                // Log selection events
                if (isSelectedCallCount <= 10 || isSelected === 1) {
                    console.log("[DEBUG] IsSelected() returned: " + isSelected + " for instance: " + instanceId);
                }

                if (isSelected === 1) { // Entity is selected
                    const currentTime = Date.now();

                    // Skip if already processed recently
                    if (processedInstances.has(instanceId)) {
                        console.log("[DEBUG] Skipping already processed instance: " + instanceId);
                        return;
                    }

                    // Avoid rapid duplicate selections
                    if (currentTime - lastSelectionTime < 1000) {
                        console.log("[DEBUG] Ignoring rapid selection (within 1 second)");
                        return;
                    }

                    lastSelectionTime = currentTime;
                    console.log("[+] *** SELECTED ENTITY DETECTED *** Instance: " + instanceId);
                    console.log("[+] Starting auto-upgrade process...");

                    // Start upgrade process with delay
                    setTimeout(() => {
                        performAutoUpgrade(this.thisPtr, instanceId);
                    }, 500);
                }
            }
        });

        isSelectedHookInstalled = true;
        console.log("[+] IsSelected() hook installed successfully");

    } catch (error) {
        console.log("[-] Failed to install IsSelected hook: " + error);
    }
}

// Perform auto-upgrade on selected entity
function performAutoUpgrade(entityPtr, instanceId) {
    console.log("[+] ===== AUTO-UPGRADE STARTED =====");
    console.log("[+] Entity: " + instanceId);

    try {
        // Validate we have required methods
        var requiredMethods = ['CanUpgrade', 'InstantUpgrade'];
        var missingMethods = requiredMethods.filter(function(method) {
            return !entityControllerMethods[method];
        });

        if (missingMethods.length > 0) {
            console.log("[-] Missing required methods: " + missingMethods.join(", "));
            console.log("[-] Cannot perform auto-upgrade");
            return;
        }

        // Check if entity can upgrade
        console.log("[+] Checking if entity can upgrade...");

        var canUpgrade;
        try {
            // Call CanUpgrade(false) - useAlternateResource = false
            canUpgrade = entityControllerMethods.CanUpgrade.func(entityPtr, 0);
            console.log("[+] CanUpgrade() returned: " + canUpgrade);
        } catch (e) {
            console.log("[-] Error calling CanUpgrade(): " + e);
            return;
        }

        // Check result (IL2CPP returns 1 for true, 0 for false)
        if (!canUpgrade || canUpgrade === 0) {
            console.log("[-] Entity cannot upgrade");
            processedInstances.add(instanceId);
            return;
        }

        console.log("[+] Entity can upgrade! Starting upgrade loop...");

        // Get current and max levels if available
        var currentLevel = "unknown";
        var maxLevel = "unknown";

        if (entityControllerMethods.GetLevel) {
            try {
                currentLevel = entityControllerMethods.GetLevel.func(entityPtr);
            } catch (e) {
                console.log("[-] Could not get current level: " + e);
            }
        }

        if (entityControllerMethods.GetMaxUpgradeLevel) {
            try {
                maxLevel = entityControllerMethods.GetMaxUpgradeLevel.func(entityPtr);
            } catch (e) {
                console.log("[-] Could not get max upgrade level: " + e);
            }
        }

        console.log("[+] Current Level: " + currentLevel + ", Max Level: " + maxLevel);

        // Perform upgrades
        performUpgradeLoop(entityPtr, instanceId, maxLevel);

    } catch (error) {
        console.log("[-] Error in performAutoUpgrade: " + error);
        processedInstances.add(instanceId);
    }
}

// Perform upgrade loop until max level is reached
function performUpgradeLoop(entityPtr, instanceId, maxLevel) {
    var upgradeCount = 0;
    var maxUpgradeAttempts = 50; // Safety limit
    var upgradedLevels = 0;

    console.log("[+] Starting upgrade loop for entity " + instanceId);

    function performSingleUpgrade() {
        try {
            upgradeCount++;

            if (upgradeCount > maxUpgradeAttempts) {
                console.log("[-] Reached maximum upgrade attempts for entity " + instanceId);
                processedInstances.add(instanceId);
                return;
            }

            // Get current level before upgrade
            var levelBefore = "unknown";
            if (entityControllerMethods.GetLevel) {
                try {
                    levelBefore = entityControllerMethods.GetLevel.func(entityPtr);
                } catch (e) {
                    console.log("[-] Could not get level before upgrade: " + e);
                }
            }

            // Perform instant upgrade
            console.log("[+] Calling InstantUpgrade() (attempt " + upgradeCount + ")...");
            try {
                entityControllerMethods.InstantUpgrade.func(entityPtr);
                console.log("[+] InstantUpgrade() called successfully");
            } catch (e) {
                console.log("[-] Error calling InstantUpgrade(): " + e);
                processedInstances.add(instanceId);
                return;
            }

            // Wait for upgrade to process
            setTimeout(() => {
                try {
                    // Check new level and upgrade status
                    let levelAfter = "unknown";
                    let newMaxLevel = maxLevel;
                    let stillCanUpgrade = false;

                    if (entityControllerMethods.GetLevel) {
                        try {
                            levelAfter = entityControllerMethods.GetLevel.func(entityPtr);
                        } catch (e) {
                            console.log("[-] Could not get level after upgrade: " + e);
                        }
                    }

                    if (entityControllerMethods.GetMaxUpgradeLevel) {
                        try {
                            newMaxLevel = entityControllerMethods.GetMaxUpgradeLevel.func(entityPtr);
                        } catch (e) {
                            console.log("[-] Could not get max upgrade level after upgrade: " + e);
                        }
                    }

                    // Check if upgrade was successful
                    if (levelAfter !== "unknown" && levelBefore !== "unknown" && levelAfter > levelBefore) {
                        upgradedLevels++;
                        console.log("[+] Successfully upgraded entity " + instanceId + " from level " + levelBefore + " to " + levelAfter);
                    } else {
                        console.log("[-] Upgrade attempt failed - level didn't increase (still " + levelAfter + ")");
                    }

                    // Check if we've reached max level
                    if (levelAfter !== "unknown" && newMaxLevel !== "unknown" && levelAfter >= newMaxLevel) {
                        console.log("[+] Entity " + instanceId + " reached maximum level: " + levelAfter);
                        console.log("[+] Total upgrades performed: " + upgradedLevels);
                        processedInstances.add(instanceId);
                        return;
                    }

                    // Check if we can still upgrade
                    try {
                        stillCanUpgrade = entityControllerMethods.CanUpgrade.func(entityPtr, 0);
                    } catch (e) {
                        console.log("[-] Error checking CanUpgrade in loop: " + e);
                        processedInstances.add(instanceId);
                        return;
                    }

                    if (!stillCanUpgrade) {
                        console.log("[+] Entity " + instanceId + " can no longer upgrade (final level: " + levelAfter + ")");
                        console.log("[+] Total upgrades performed: " + upgradedLevels);
                        processedInstances.add(instanceId);
                        return;
                    }

                    // Continue upgrading
                    setTimeout(performSingleUpgrade, 300); // 300ms delay between upgrades

                } catch (e) {
                    console.log("[-] Error checking upgrade result: " + e);
                    processedInstances.add(instanceId);
                }
            }, 500); // Wait 500ms for upgrade to process

        } catch (e) {
            console.log("[-] Error during upgrade: " + e);
            processedInstances.add(instanceId);
        }
    }

    // Start the upgrade loop
    performSingleUpgrade();
}

// Setup delayed discovery for when EntityController is loaded later
function setupDelayedDiscovery() {
    console.log("[+] Setting up delayed discovery...");
    console.log("[+] Will retry EntityController discovery every 10 seconds");

    // Retry discovery every 10 seconds
    var retryInterval = setInterval(function() {
        try {
            console.log("[+] Retrying EntityController discovery...");

            var success = discoverEntityController();
            if (success) {
                createMethodWrappers();
                installHooks();
                clearInterval(retryInterval);
            }
        } catch (e) {
            // Continue retrying
        }
    }, 10000);

    // Stop retrying after 5 minutes
    setTimeout(function() {
        clearInterval(retryInterval);
        console.log("[-] Stopped delayed discovery after 5 minutes");
    }, 300000);
}

// RPC exports for manual control and testing
rpc.exports = {
    // Set manual module for IL2CPP discovery
    setModule: function(moduleName) {
        console.log("[+] Setting manual module: " + moduleName);
        try {
            var module = Process.findModuleByName(moduleName);
            if (!module) {
                console.log("[-] Module not found: " + moduleName);
                return { success: false, error: "Module not found" };
            }

            manualModule = module;
            console.log("[+] Manual module set: " + module.name + " at " + module.base);
            console.log("[+] Module size: " + (module.size / 1024 / 1024).toFixed(1) + "MB");

            // Clear previous methods and retry discovery
            entityControllerMethods = {};
            var success = discoverEntityController();
            if (success) {
                createMethodWrappers();
                installHooks();
            }

            return {
                success: success,
                module: module.name,
                base: module.base.toString(),
                size: (module.size / 1024 / 1024).toFixed(1) + "MB",
                methodsFound: Object.keys(entityControllerMethods).length
            };
        } catch (e) {
            console.log("[-] Set module failed: " + e);
            return { success: false, error: e.toString() };
        }
    },

    // List available modules
    listModules: function() {
        console.log("[+] Listing all modules...");
        try {
            var modules = Process.enumerateModules();
            var moduleList = modules.map(function(module) {
                return {
                    name: module.name,
                    base: module.base.toString(),
                    size: (module.size / 1024 / 1024).toFixed(1) + "MB"
                };
            });

            // Sort by size (largest first)
            moduleList.sort(function(a, b) {
                return parseFloat(b.size) - parseFloat(a.size);
            });

            console.log("[+] Found " + moduleList.length + " modules");
            return { success: true, modules: moduleList };
        } catch (e) {
            console.log("[-] List modules failed: " + e);
            return { success: false, error: e.toString() };
        }
    },

    // Re-run EntityController discovery
    discoverMethods: function() {
        console.log("[+] Manual EntityController discovery triggered");
        try {
            // Clear previous methods
            entityControllerMethods = {};

            var success = discoverEntityController();
            if (success) {
                createMethodWrappers();
                installHooks();
            }

            return {
                success: success,
                methodsFound: Object.keys(entityControllerMethods).length,
                methods: Object.keys(entityControllerMethods),
                module: manualModule ? manualModule.name : "auto-detected"
            };
        } catch (e) {
            console.log("[-] Manual discovery failed: " + e);
            return { success: false, error: e.toString() };
        }
    },

    // Test CanUpgrade on specific entity
    testCanUpgrade: function(entityAddress) {
        console.log("[+] Testing CanUpgrade() on address: " + entityAddress);
        try {
            if (!entityControllerMethods.CanUpgrade) {
                return { success: false, error: "CanUpgrade method not found" };
            }

            var entityPtr = ptr(entityAddress);
            var result = entityControllerMethods.CanUpgrade.func(entityPtr, 0);
            console.log("[+] CanUpgrade() returned: " + result);

            return { success: true, canUpgrade: result };
        } catch (e) {
            console.log("[-] CanUpgrade test failed: " + e);
            return { success: false, error: e.toString() };
        }
    },

    // Manual upgrade test
    manualTest: function(entityAddress) {
        console.log("[+] Manual upgrade test for address: " + entityAddress);
        try {
            const entityPtr = ptr(entityAddress);
            performAutoUpgrade(entityPtr, entityAddress);
            return { success: true, message: "Manual upgrade started" };
        } catch (e) {
            console.log("[-] Manual test failed: " + e);
            return { success: false, error: e.toString() };
        }
    },

    // Test all methods on specific entity
    testAllMethods: function(entityAddress) {
        console.log("[+] Testing all EntityController methods on address: " + entityAddress);
        var results = {};

        try {
            var entityPtr = ptr(entityAddress);

            // Test each available method
            Object.keys(entityControllerMethods).forEach(function(methodKey) {
                var method = entityControllerMethods[methodKey];
                try {
                    var result;

                    if (methodKey === 'CanUpgrade') {
                        result = method.func(entityPtr, 0);
                    } else if (methodKey === 'InstantUpgrade') {
                        // Don't actually call InstantUpgrade in test
                        result = "Method available (not called in test)";
                    } else {
                        result = method.func(entityPtr);
                    }

                    results[methodKey] = { success: true, result: result };
                } catch (e) {
                    results[methodKey] = { success: false, error: e.toString() };
                }
            });

            console.log("[+] Test results:", JSON.stringify(results, null, 2));
            return results;
        } catch (e) {
            console.log("[-] Method testing failed: " + e);
            return { error: e.toString() };
        }
    },

    // Get current status
    getStatus: function() {
        return {
            methodsFound: Object.keys(entityControllerMethods).length,
            methods: Object.keys(entityControllerMethods),
            isSelectedCalls: isSelectedCallCount,
            processedInstances: processedInstances.size,
            hookInstalled: isSelectedHookInstalled
        };
    },

    // Clear processed instances
    clearProcessed: function() {
        const count = processedInstances.size;
        processedInstances.clear();
        console.log("[+] Cleared " + count + " processed instances");
        return { cleared: count };
    }
};

// Initialize the script with timeout and retry mechanism
function main() {
    console.log("[+] Starting EntityController Auto-Upgrader initialization...");
    console.log("[+] Waiting for IL2CPP module to load...");

    var attempts = 0;
    var maxAttempts = 120; // 60 seconds with 500ms intervals

    function tryInitialization() {
        attempts++;

        try {
            // Try to find IL2CPP module
            var il2cppModule = findIL2CPPModule();

            if (il2cppModule) {
                console.log("[+] IL2CPP module found, starting discovery...");

                // Discover EntityController methods
                var success = discoverEntityController();

                if (success) {
                    // Create NativeFunction wrappers
                    createMethodWrappers();

                    // Install hooks
                    installHooks();

                    console.log("[+] ===== EntityController Auto-Upgrader READY =====");
                    console.log("[+] Found " + Object.keys(entityControllerMethods).length + " methods");
                    console.log("[+] Select an entity in the game to trigger auto-upgrade");
                    console.log("=".repeat(60));
                } else {
                    console.log("[-] EntityController discovery failed");
                    console.log("[+] Manual options:");
                    console.log("    rpc.exports.listModules() - List all modules");
                    console.log("    rpc.exports.setModule('module_name') - Set IL2CPP module manually");
                    console.log("    rpc.exports.discoverMethods() - Retry discovery");
                }
            } else {
                if (attempts < maxAttempts) {
                    // Continue waiting
                    setTimeout(tryInitialization, 500);
                } else {
                    console.log("[-] Timeout waiting for IL2CPP module (60 seconds)");
                    console.log("[+] Manual options:");
                    console.log("    rpc.exports.listModules() - List all modules");
                    console.log("    rpc.exports.setModule('module_name') - Set IL2CPP module manually");
                }
            }
        } catch (error) {
            console.log("[-] Initialization error (attempt " + attempts + "): " + error);

            if (attempts < maxAttempts) {
                setTimeout(tryInitialization, 500);
            } else {
                console.log("[-] Initialization failed after " + maxAttempts + " attempts");
                console.log("[+] Use rpc.exports.discoverMethods() to retry discovery");
            }
        }
    }

    // Start initialization
    tryInitialization();
}

// Start the script
console.log("[+] EntityController Auto-Upgrader - Enhanced Module Detection");
console.log("[+] Supports stripped IL2CPP binaries with multiple discovery strategies");
main();